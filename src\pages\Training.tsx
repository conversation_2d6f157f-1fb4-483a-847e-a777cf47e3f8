
import React, { useContext, useState } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

interface TrainingModule {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  level: 'foundation' | 'intermediate' | 'advanced' | 'professional';
  duration: { en: string; ar: string };
  lessons: number;
  assessments: number;
  certificate: boolean;
  icon: string;
  progress: number;
}

interface TrainingPath {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  modules: string[];
  totalDuration: { en: string; ar: string };
  icon: string;
}

const Training: React.FC = () => {
  const { language } = useContext(LanguageContext);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);

  const translations = {
    en: {
      title: 'Professional Training Programs',
      subtitle: 'Structured Learning Paths for Biomedical Engineering Excellence',
      description: 'Choose from our comprehensive training programs designed to advance your skills in biomedical engineering and instrumentation.',
      trainingPaths: 'Training Paths',
      modules: 'Training Modules',
      selectPath: 'Select Training Path',
      startTraining: 'Start Training',
      duration: 'Duration',
      lessons: 'Lessons',
      assessments: 'Assessments',
      certificate: 'Certificate',
      progress: 'Progress',
      level: 'Level',
      foundation: 'Foundation',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      professional: 'Professional',
      totalModules: 'Total Modules',
      estimatedTime: 'Estimated Time',
      backToPaths: 'Back to Training Paths',
      enrollNow: 'Enroll Now',
      continueTraining: 'Continue Training',
      completed: 'Completed',
      inProgress: 'In Progress',
      notStarted: 'Not Started'
    },
    ar: {
      title: 'برامج التدريب المهني',
      subtitle: 'مسارات تعليمية منظمة للتميز في الهندسة الطبية الحيوية',
      description: 'اختر من برامجنا التدريبية الشاملة المصممة لتطوير مهاراتك في الهندسة الطبية الحيوية والأجهزة الطبية.',
      trainingPaths: 'مسارات التدريب',
      modules: 'وحدات التدريب',
      selectPath: 'اختر مسار التدريب',
      startTraining: 'ابدأ التدريب',
      duration: 'المدة',
      lessons: 'الدروس',
      assessments: 'التقييمات',
      certificate: 'الشهادة',
      progress: 'التقدم',
      level: 'المستوى',
      foundation: 'أساسي',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      professional: 'مهني',
      totalModules: 'إجمالي الوحدات',
      estimatedTime: 'الوقت المقدر',
      backToPaths: 'العودة إلى مسارات التدريب',
      enrollNow: 'سجل الآن',
      continueTraining: 'تابع التدريب',
      completed: 'مكتمل',
      inProgress: 'قيد التنفيذ',
      notStarted: 'لم يبدأ'
    },
  };

  const trainingPaths: TrainingPath[] = [
    {
      id: 'fundamentals',
      title: {
        en: 'Biomedical Engineering Fundamentals',
        ar: 'أساسيات الهندسة الطبية الحيوية'
      },
      description: {
        en: 'Master the core principles of biomedical engineering, from basic physiology to medical device design.',
        ar: 'إتقان المبادئ الأساسية للهندسة الطبية الحيوية، من علم وظائف الأعضاء الأساسي إلى تصميم الأجهزة الطبية.'
      },
      modules: ['anatomy-physiology', 'medical-electronics', 'biosafety', 'ethics'],
      totalDuration: { en: '12-16 weeks', ar: '12-16 أسبوع' },
      icon: '🎓'
    },
    {
      id: 'instrumentation',
      title: {
        en: 'Medical Instrumentation Specialist',
        ar: 'أخصائي الأجهزة الطبية'
      },
      description: {
        en: 'Become an expert in medical instrumentation, calibration, and maintenance of biomedical equipment.',
        ar: 'كن خبيرًا في الأجهزة الطبية والمعايرة وصيانة المعدات الطبية الحيوية.'
      },
      modules: ['ecg-analysis', 'medical-imaging', 'patient-monitoring', 'equipment-maintenance'],
      totalDuration: { en: '16-20 weeks', ar: '16-20 أسبوع' },
      icon: '🔧'
    },
    {
      id: 'signal-processing',
      title: {
        en: 'Biosignal Processing Expert',
        ar: 'خبير معالجة الإشارات الحيوية'
      },
      description: {
        en: 'Advanced training in digital signal processing techniques for biomedical applications.',
        ar: 'تدريب متقدم في تقنيات معالجة الإشارات الرقمية للتطبيقات الطبية الحيوية.'
      },
      modules: ['biosignals', 'signal-analysis', 'machine-learning', 'data-processing'],
      totalDuration: { en: '14-18 weeks', ar: '14-18 أسبوع' },
      icon: '📊'
    },
    {
      id: 'clinical-engineering',
      title: {
        en: 'Clinical Engineering Professional',
        ar: 'مهني الهندسة السريرية'
      },
      description: {
        en: 'Comprehensive training for clinical engineers working in healthcare facilities.',
        ar: 'تدريب شامل للمهندسين السريريين العاملين في المرافق الصحية.'
      },
      modules: ['hospital-systems', 'quality-assurance', 'regulatory-compliance', 'project-management'],
      totalDuration: { en: '20-24 weeks', ar: '20-24 أسبوع' },
      icon: '🏥'
    }
  ];

  const trainingModules: TrainingModule[] = [
    {
      id: 'anatomy-physiology',
      title: {
        en: 'Human Anatomy & Physiology for Engineers',
        ar: 'تشريح ووظائف الأعضاء البشرية للمهندسين'
      },
      description: {
        en: 'Essential knowledge of human body systems relevant to biomedical engineering applications.',
        ar: 'المعرفة الأساسية لأنظمة الجسم البشري ذات الصلة بتطبيقات الهندسة الطبية الحيوية.'
      },
      level: 'foundation',
      duration: { en: '3 weeks', ar: '3 أسابيع' },
      lessons: 15,
      assessments: 3,
      certificate: true,
      icon: '🫀',
      progress: 0
    },
    {
      id: 'medical-electronics',
      title: {
        en: 'Medical Electronics & Circuits',
        ar: 'الإلكترونيات الطبية والدوائر'
      },
      description: {
        en: 'Design and analysis of electronic circuits used in medical devices and instrumentation.',
        ar: 'تصميم وتحليل الدوائر الإلكترونية المستخدمة في الأجهزة الطبية والأجهزة.'
      },
      level: 'intermediate',
      duration: { en: '4 weeks', ar: '4 أسابيع' },
      lessons: 20,
      assessments: 4,
      certificate: true,
      icon: '⚡',
      progress: 25
    },
    {
      id: 'ecg-analysis',
      title: {
        en: 'ECG Signal Acquisition & Analysis',
        ar: 'اكتساب وتحليل إشارات تخطيط القلب'
      },
      description: {
        en: 'Comprehensive training in ECG technology, signal processing, and clinical interpretation.',
        ar: 'تدريب شامل في تقنية تخطيط القلب ومعالجة الإشارات والتفسير السريري.'
      },
      level: 'intermediate',
      duration: { en: '3 weeks', ar: '3 أسابيع' },
      lessons: 18,
      assessments: 3,
      certificate: true,
      icon: '💓',
      progress: 60
    },
    {
      id: 'medical-imaging',
      title: {
        en: 'Medical Imaging Systems',
        ar: 'أنظمة التصوير الطبي'
      },
      description: {
        en: 'Advanced study of X-ray, CT, MRI, ultrasound, and other medical imaging modalities.',
        ar: 'دراسة متقدمة للأشعة السينية والمقطعية والرنين المغناطيسي والموجات فوق الصوتية وطرق التصوير الطبي الأخرى.'
      },
      level: 'advanced',
      duration: { en: '5 weeks', ar: '5 أسابيع' },
      lessons: 25,
      assessments: 5,
      certificate: true,
      icon: '🏥',
      progress: 0
    },
    {
      id: 'biosignals',
      title: {
        en: 'Biosignal Processing & Analysis',
        ar: 'معالجة وتحليل الإشارات الحيوية'
      },
      description: {
        en: 'Digital signal processing techniques for biomedical signals including filtering, feature extraction, and classification.',
        ar: 'تقنيات معالجة الإشارات الرقمية للإشارات الطبية الحيوية بما في ذلك الترشيح واستخراج الميزات والتصنيف.'
      },
      level: 'advanced',
      duration: { en: '4 weeks', ar: '4 أسابيع' },
      lessons: 22,
      assessments: 4,
      certificate: true,
      icon: '📊',
      progress: 80
    },
    {
      id: 'patient-monitoring',
      title: {
        en: 'Patient Monitoring Systems',
        ar: 'أنظمة مراقبة المرضى'
      },
      description: {
        en: 'Design, implementation, and maintenance of patient monitoring systems in clinical environments.',
        ar: 'تصميم وتنفيذ وصيانة أنظمة مراقبة المرضى في البيئات السريرية.'
      },
      level: 'professional',
      duration: { en: '4 weeks', ar: '4 أسابيع' },
      lessons: 20,
      assessments: 4,
      certificate: true,
      icon: '📱',
      progress: 0
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'foundation': return 'bg-blue-100 text-blue-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-orange-100 text-orange-800';
      case 'professional': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress === 0) return 'bg-gray-200';
    if (progress < 50) return 'bg-yellow-400';
    if (progress < 100) return 'bg-blue-400';
    return 'bg-green-400';
  };

  const getProgressStatus = (progress: number) => {
    if (progress === 0) return translations[language].notStarted;
    if (progress < 100) return translations[language].inProgress;
    return translations[language].completed;
  };

  const selectedPathData = trainingPaths.find(path => path.id === selectedPath);
  const pathModules = selectedPathData ? trainingModules.filter(module =>
    selectedPathData.modules.includes(module.id)
  ) : [];

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-12 px-6 rounded-lg mb-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4">{translations[language].title}</h1>
          <h2 className="text-xl mb-4 opacity-90">{translations[language].subtitle}</h2>
          <p className="text-lg opacity-80">{translations[language].description}</p>
        </div>
      </div>

      {!selectedPath ? (
        /* Training Paths Selection */
        <div>
          <h2 className="text-2xl font-bold mb-6">{translations[language].selectPath}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            {trainingPaths.map((path) => (
              <div key={path.id} className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                <div className="bg-gradient-to-r from-purple-500 to-blue-500 p-6 text-center">
                  <div className="text-4xl mb-2">{path.icon}</div>
                  <h3 className="text-xl font-bold text-white">{path.title[language]}</h3>
                </div>

                <div className="p-6">
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {path.description[language]}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].totalModules}:</span>
                      <span className="text-sm font-medium">{path.modules.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].estimatedTime}:</span>
                      <span className="text-sm font-medium">{path.totalDuration[language]}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => setSelectedPath(path.id)}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200"
                  >
                    {translations[language].startTraining}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* All Modules Overview */}
          <div>
            <h2 className="text-2xl font-bold mb-6">{translations[language].modules}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {trainingModules.map((module) => (
                <div key={module.id} className="bg-white rounded-lg shadow-lg p-6">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-3">{module.icon}</span>
                    <div>
                      <h3 className="text-lg font-bold">{module.title[language]}</h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${getLevelColor(module.level)}`}>
                        {translations[language][module.level]}
                      </span>
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4">{module.description[language]}</p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{translations[language].duration}:</span>
                      <span>{module.duration[language]}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{translations[language].lessons}:</span>
                      <span>{module.lessons}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">{translations[language].progress}:</span>
                      <span>{getProgressStatus(module.progress)}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>{translations[language].progress}</span>
                      <span>{module.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(module.progress)}`}
                        style={{ width: `${module.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                    {module.progress > 0 ? translations[language].continueTraining : translations[language].enrollNow}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        /* Selected Path Details */
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">{selectedPathData?.title[language]}</h2>
            <button
              onClick={() => setSelectedPath(null)}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              {translations[language].backToPaths}
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <p className="text-gray-600 mb-4">{selectedPathData?.description[language]}</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{pathModules.length}</div>
                <div className="text-sm text-gray-600">{translations[language].modules}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{selectedPathData?.totalDuration[language]}</div>
                <div className="text-sm text-gray-600">{translations[language].duration}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {pathModules.reduce((sum, module) => sum + module.lessons, 0)}
                </div>
                <div className="text-sm text-gray-600">{translations[language].lessons}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {pathModules.reduce((sum, module) => sum + module.assessments, 0)}
                </div>
                <div className="text-sm text-gray-600">{translations[language].assessments}</div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {pathModules.map((module) => (
              <div key={module.id} className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <span className="text-3xl mr-3">{module.icon}</span>
                  <div>
                    <h3 className="text-lg font-bold">{module.title[language]}</h3>
                    <span className={`text-xs px-2 py-1 rounded-full ${getLevelColor(module.level)}`}>
                      {translations[language][module.level]}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4">{module.description[language]}</p>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">{translations[language].duration}:</span>
                    <span>{module.duration[language]}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">{translations[language].lessons}:</span>
                    <span>{module.lessons}</span>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span>{translations[language].progress}</span>
                    <span>{module.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(module.progress)}`}
                      style={{ width: `${module.progress}%` }}
                    ></div>
                  </div>
                </div>

                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                  {module.progress > 0 ? translations[language].continueTraining : translations[language].enrollNow}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Training;
