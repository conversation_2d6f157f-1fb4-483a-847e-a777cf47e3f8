
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const Training: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'Training',
      description: 'Access our training modules here.',
    },
    ar: {
      title: 'التدريب',
      description: 'ادخل إلى وحدات التدريب الخاصة بنا من هنا.',
    },
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">{translations[language].title}</h1>
      <p>{translations[language].description}</p>
      {/* Training modules will be added here */}
    </div>
  );
};

export default Training;
