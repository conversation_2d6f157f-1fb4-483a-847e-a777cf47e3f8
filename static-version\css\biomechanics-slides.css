/* ===== BIOMECHANICS & REHABILITATION SLIDES STYLES ===== */

/* Motion Fundamentals */
.motion-fundamentals {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.motion-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.motion-planes {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 2rem;
    border-radius: 1rem;
}

.motion-planes h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.planes-visualization {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    position: relative;
}

.human-figure {
    position: relative;
    width: 150px;
    height: 250px;
}

.body-outline {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 100%;
}

.head {
    width: 30px;
    height: 30px;
    background: #fbbf24;
    border-radius: 50%;
    margin: 0 auto 5px;
}

.torso {
    width: 40px;
    height: 80px;
    background: #3b82f6;
    border-radius: 20px 20px 10px 10px;
    margin: 0 auto 5px;
}

.arm {
    position: absolute;
    width: 8px;
    height: 60px;
    background: #ef4444;
    border-radius: 4px;
    top: 40px;
}

.left-arm {
    left: -15px;
    transform: rotate(-15deg);
    animation: arm-swing-left 2s ease-in-out infinite;
}

.right-arm {
    right: -15px;
    transform: rotate(15deg);
    animation: arm-swing-right 2s ease-in-out infinite;
}

.leg {
    position: absolute;
    width: 12px;
    height: 100px;
    top: 120px;
}

.left-leg {
    left: 15px;
    animation: leg-swing-left 2s ease-in-out infinite;
}

.right-leg {
    right: 15px;
    animation: leg-swing-right 2s ease-in-out infinite;
}

.leg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: #10b981;
    border-radius: 6px;
}

.leg::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: #059669;
    border-radius: 6px;
}

@keyframes arm-swing-left {
    0%, 100% { transform: rotate(-15deg); }
    50% { transform: rotate(15deg); }
}

@keyframes arm-swing-right {
    0%, 100% { transform: rotate(15deg); }
    50% { transform: rotate(-15deg); }
}

@keyframes leg-swing-left {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(10deg); }
}

@keyframes leg-swing-right {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(-10deg); }
}

.motion-planes-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.plane {
    position: absolute;
    border: 2px solid;
    opacity: 0.7;
    transition: all var(--transition-fast);
}

.plane.sagittal {
    top: 10%;
    left: 50%;
    width: 2px;
    height: 80%;
    border-color: #ef4444;
    transform: translateX(-50%);
}

.plane.frontal {
    top: 50%;
    left: 10%;
    width: 80%;
    height: 2px;
    border-color: #10b981;
    transform: translateY(-50%);
}

.plane.transverse {
    top: 40%;
    left: 20%;
    width: 60%;
    height: 60%;
    border-color: #3b82f6;
    border-radius: 50%;
    border-style: dashed;
}

.plane-label {
    position: absolute;
    background: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.sagittal .plane-label {
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    color: #dc2626;
}

.frontal .plane-label {
    top: 50%;
    right: -60px;
    transform: translateY(-50%);
    color: #059669;
}

.transverse .plane-label {
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    color: #1d4ed8;
}

.motion-types {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.motion-types h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.motion-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.motion-card {
    background: var(--gray-50);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border-left: 4px solid #3b82f6;
    transition: all var(--transition-fast);
}

.motion-card:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
    border-left-color: #1d4ed8;
}

.motion-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.motion-card h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.motion-card p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.motion-demo {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.joint-demo {
    width: 30px;
    height: 30px;
    background: #3b82f6;
    border-radius: 50%;
    position: relative;
}

.joint-demo::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 4px;
    background: #1d4ed8;
    transform-origin: left center;
}

.flexion-extension::before {
    animation: flexion-extension 2s ease-in-out infinite;
}

.abduction-adduction::before {
    animation: abduction-adduction 2s ease-in-out infinite;
}

.rotation::before {
    animation: rotation 2s linear infinite;
}

@keyframes flexion-extension {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    50% { transform: translate(-50%, -50%) rotate(45deg); }
}

@keyframes abduction-adduction {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    50% { transform: translate(-50%, -50%) rotate(-45deg); }
}

@keyframes rotation {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Gait Analysis */
.gait-analysis {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.gait-cycle {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    padding: 2rem;
    border-radius: 1rem;
}

.gait-cycle h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.gait-visualization {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.gait-timeline {
    background: var(--white);
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.timeline-track {
    position: relative;
    height: 60px;
    background: var(--gray-200);
    border-radius: 30px;
    overflow: hidden;
}

.phase-marker {
    position: absolute;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.phase-marker.stance {
    left: 0;
    width: 60%;
}

.phase-marker.swing {
    right: 0;
    width: 40%;
}

.phase-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30px;
}

.stance-bar {
    background: linear-gradient(135deg, #10b981, #059669);
    animation: stance-phase 3s ease-in-out infinite;
}

.swing-bar {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    animation: swing-phase 3s ease-in-out infinite;
}

@keyframes stance-phase {
    0%, 60% { opacity: 1; }
    61%, 100% { opacity: 0.3; }
}

@keyframes swing-phase {
    0%, 60% { opacity: 0.3; }
    61%, 100% { opacity: 1; }
}

.phase-label {
    position: relative;
    z-index: 2;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.walking-figure {
    background: var(--white);
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: center;
    align-items: center;
}

.figure-container {
    position: relative;
    width: 200px;
    height: 150px;
}

.stick-figure {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 120px;
}

.stick-figure .head {
    width: 20px;
    height: 20px;
    background: #fbbf24;
    border-radius: 50%;
    margin: 0 auto 5px;
}

.stick-figure .body {
    width: 4px;
    height: 50px;
    background: #374151;
    margin: 0 auto;
}

.stick-figure .arm {
    position: absolute;
    width: 3px;
    height: 30px;
    background: #6b7280;
    top: 25px;
}

.stick-figure .left-arm {
    left: 15px;
    transform-origin: top center;
    animation: walking-arm-left 1.5s ease-in-out infinite;
}

.stick-figure .right-arm {
    right: 15px;
    transform-origin: top center;
    animation: walking-arm-right 1.5s ease-in-out infinite;
}

.stick-figure .leg {
    position: absolute;
    top: 70px;
    width: 4px;
    height: 50px;
}

.stick-figure .left-leg {
    left: 25px;
    transform-origin: top center;
    animation: walking-leg-left 1.5s ease-in-out infinite;
}

.stick-figure .right-leg {
    right: 25px;
    transform-origin: top center;
    animation: walking-leg-right 1.5s ease-in-out infinite;
}

.stick-figure .thigh {
    width: 100%;
    height: 50%;
    background: #059669;
    border-radius: 2px;
}

.stick-figure .shin {
    width: 100%;
    height: 50%;
    background: #10b981;
    border-radius: 2px;
}

.stick-figure .foot {
    position: absolute;
    bottom: -5px;
    left: -3px;
    width: 10px;
    height: 3px;
    background: #374151;
    border-radius: 1px;
}

@keyframes walking-arm-left {
    0%, 100% { transform: rotate(-20deg); }
    50% { transform: rotate(20deg); }
}

@keyframes walking-arm-right {
    0%, 100% { transform: rotate(20deg); }
    50% { transform: rotate(-20deg); }
}

@keyframes walking-leg-left {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(15deg); }
    75% { transform: rotate(-15deg); }
}

@keyframes walking-leg-right {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(15deg); }
}

.gait-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.gait-btn {
    background: linear-gradient(135deg, #059669, #10b981);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-sm);
}

.gait-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, #047857, #059669);
}

.gait-btn:active {
    transform: translateY(0);
}

.gait-parameters {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    height: fit-content;
}

.gait-parameters h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.parameter-display {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.parameter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 4px solid #10b981;
}

.param-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.param-value {
    font-weight: 700;
    color: #059669;
    font-size: 1rem;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--gray-300);
}

/* Biomechanics Page Specific Styles */
.biomechanics-slides-page .slide-deck-header {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.biomechanics-slides-page .control-btn {
    background: rgba(255, 255, 255, 0.2);
}

.biomechanics-slides-page .control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
}

/* Walking Animation */
@keyframes walk-animation {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(10px); }
}

.walk-animation {
    animation: walk-animation 1.5s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .motion-overview,
    .gait-analysis {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .planes-visualization {
        height: 250px;
    }
    
    .human-figure {
        width: 120px;
        height: 200px;
    }
}

@media (max-width: 768px) {
    .motion-cards {
        gap: 0.75rem;
    }
    
    .motion-card {
        padding: 1rem;
    }
    
    .gait-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .gait-btn {
        width: 200px;
        justify-content: center;
    }
    
    .parameter-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .motion-planes,
    .motion-types,
    .gait-cycle,
    .gait-parameters {
        padding: 1.5rem;
    }
    
    .figure-container {
        width: 150px;
        height: 120px;
    }
    
    .stick-figure {
        width: 50px;
        height: 100px;
    }
}
