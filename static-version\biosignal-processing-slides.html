<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-en="Biosignal Processing - Interactive Slides" data-lang-ar="معالجة الإشارات الحيوية - شرائح تفاعلية">Biosignal Processing - Interactive Slides</title>
    <meta name="description" content="Interactive slide deck for biosignal processing education with real-time demonstrations">
    <meta name="keywords" content="biosignal processing, EEG, EMG, signal analysis, biomedical engineering">
    <meta name="author" content="Dr<PERSON>, SUST - BME">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    
    <!-- Fonts for bilingual support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slide-deck.css">
    <link rel="stylesheet" href="css/biosignal-slides.css">
</head>
<body class="biosignal-slides-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo and Title -->
                <div class="logo-section">
                    <div class="logo-icon">🧬</div>
                    <div class="logo-text">
                        <h1 data-lang-en="BioEngage" data-lang-ar="بايو إنجيج">BioEngage</h1>
                        <p class="subtitle" data-lang-en="Interactive Virtual LMS" data-lang-ar="نظام إدارة التعلم الافتراضي التفاعلي">Interactive Virtual LMS</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="nav-desktop">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                        <li><a href="modules.html" class="nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                        <li><a href="extended-modules.html" class="nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                        <li><a href="interactive_lectures.html" class="nav-link active" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                        <li><a href="training.html" class="nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                        <li><a href="virtual_lab.html" class="nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                    </ul>
                </nav>

                <!-- Language Toggle and Mobile Menu -->
                <div class="header-controls">
                    <button id="lang-toggle" class="lang-toggle" data-lang="en">
                        <span class="flag">🇸🇦</span>
                        <span class="lang-text" data-lang-en="العربية" data-lang-ar="English">العربية</span>
                    </button>
                    <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <nav id="mobile-nav" class="nav-mobile">
                <ul class="mobile-nav-list">
                    <li><a href="index.html" class="mobile-nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                    <li><a href="modules.html" class="mobile-nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                    <li><a href="extended-modules.html" class="mobile-nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                    <li><a href="interactive_lectures.html" class="mobile-nav-link" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                    <li><a href="training.html" class="mobile-nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                    <li><a href="virtual_lab.html" class="mobile-nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                </ul>
            </nav>
        </div>
        <div class="header-progress"></div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Slide Deck Container -->
            <div class="slide-deck-container">
                <!-- Slide Deck Header -->
                <div class="slide-deck-header">
                    <div class="deck-info">
                        <h1 class="deck-title" data-lang-en="Biosignal Processing & Analysis" data-lang-ar="معالجة وتحليل الإشارات الحيوية">Biosignal Processing & Analysis</h1>
                        <p class="deck-subtitle" data-lang-en="Advanced Signal Processing Techniques for Biomedical Applications" data-lang-ar="تقنيات معالجة الإشارات المتقدمة للتطبيقات الطبية الحيوية">Advanced Signal Processing Techniques for Biomedical Applications</p>
                    </div>
                    
                    <div class="deck-controls">
                        <div class="slide-counter">
                            <span id="current-slide">1</span> / <span id="total-slides">12</span>
                        </div>
                        <div class="control-buttons">
                            <button id="prev-slide" class="control-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="play-pause" class="control-btn">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="next-slide" class="control-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <button id="fullscreen-btn" class="control-btn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Slide Container -->
                <div class="slide-container" id="slide-container">
                    <!-- Slide 1: Title Slide -->
                    <div class="slide active" data-slide="1">
                        <div class="slide-content title-slide">
                            <div class="title-animation">
                                <div class="animated-icon wave-animation">📊</div>
                                <h1 data-lang-en="Biosignal Processing & Analysis" data-lang-ar="معالجة وتحليل الإشارات الحيوية">Biosignal Processing & Analysis</h1>
                                <h2 data-lang-en="Advanced Digital Signal Processing for Biomedical Applications" data-lang-ar="معالجة الإشارات الرقمية المتقدمة للتطبيقات الطبية الحيوية">Advanced Digital Signal Processing for Biomedical Applications</h2>
                            </div>
                            <div class="title-features">
                                <div class="feature-item fade-in-up" data-delay="0.2s">
                                    <div class="feature-icon wave-animation">🧠</div>
                                    <span data-lang-en="EEG Analysis" data-lang-ar="تحليل تخطيط الدماغ">EEG Analysis</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.4s">
                                    <div class="feature-icon pulse-animation">💪</div>
                                    <span data-lang-en="EMG Processing" data-lang-ar="معالجة تخطيط العضلات">EMG Processing</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.6s">
                                    <div class="feature-icon rotate-animation">🔄</div>
                                    <span data-lang-en="Real-time Analysis" data-lang-ar="التحليل في الوقت الفعلي">Real-time Analysis</span>
                                </div>
                            </div>
                            <div class="author-info fade-in" data-delay="0.8s">
                                <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                                <p data-lang-en="SUST - Biomedical Engineering Department" data-lang-ar="جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية">SUST - Biomedical Engineering Department</p>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2: Signal Types Overview -->
                    <div class="slide" data-slide="2">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Biosignal Types & Characteristics" data-lang-ar="أنواع وخصائص الإشارات الحيوية">Biosignal Types & Characteristics</h2>
                                <div class="slide-icon pulse-animation">📈</div>
                            </div>
                            <div class="signal-types-overview">
                                <div class="signal-comparison">
                                    <div class="signal-type-card animated-card hover-lift" data-delay="0.1s">
                                        <div class="signal-header">
                                            <div class="signal-icon heartbeat-animation">💓</div>
                                            <h3 data-lang-en="ECG - Electrocardiogram" data-lang-ar="تخطيط القلب الكهربائي">ECG - Electrocardiogram</h3>
                                        </div>
                                        <div class="signal-waveform">
                                            <svg class="waveform-svg" viewBox="0 0 300 80">
                                                <path class="ecg-waveform" d="M0,40 L50,40 L55,20 L60,60 L65,10 L70,40 L120,40 L125,20 L130,60 L135,10 L140,40 L190,40 L195,20 L200,60 L205,10 L210,40 L260,40 L265,20 L270,60 L275,10 L280,40 L300,40"></path>
                                            </svg>
                                        </div>
                                        <div class="signal-specs">
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Frequency:" data-lang-ar="التردد:">Frequency:</span>
                                                <span class="spec-value">0.05-100 Hz</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Amplitude:" data-lang-ar="السعة:">Amplitude:</span>
                                                <span class="spec-value">0.1-5 mV</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Source:" data-lang-ar="المصدر:">Source:</span>
                                                <span class="spec-value" data-lang-en="Heart muscle" data-lang-ar="عضلة القلب">Heart muscle</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="signal-type-card animated-card hover-lift" data-delay="0.2s">
                                        <div class="signal-header">
                                            <div class="signal-icon wave-animation">🧠</div>
                                            <h3 data-lang-en="EEG - Electroencephalogram" data-lang-ar="تخطيط الدماغ الكهربائي">EEG - Electroencephalogram</h3>
                                        </div>
                                        <div class="signal-waveform">
                                            <svg class="waveform-svg" viewBox="0 0 300 80">
                                                <path class="eeg-waveform" d="M0,40 Q30,30 60,40 Q90,50 120,40 Q150,30 180,40 Q210,50 240,40 Q270,30 300,40"></path>
                                            </svg>
                                        </div>
                                        <div class="signal-specs">
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Frequency:" data-lang-ar="التردد:">Frequency:</span>
                                                <span class="spec-value">0.5-100 Hz</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Amplitude:" data-lang-ar="السعة:">Amplitude:</span>
                                                <span class="spec-value">10-100 μV</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Source:" data-lang-ar="المصدر:">Source:</span>
                                                <span class="spec-value" data-lang-en="Brain neurons" data-lang-ar="خلايا الدماغ العصبية">Brain neurons</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="signal-type-card animated-card hover-lift" data-delay="0.3s">
                                        <div class="signal-header">
                                            <div class="signal-icon bounce-animation">💪</div>
                                            <h3 data-lang-en="EMG - Electromyogram" data-lang-ar="تخطيط العضلات الكهربائي">EMG - Electromyogram</h3>
                                        </div>
                                        <div class="signal-waveform">
                                            <svg class="waveform-svg" viewBox="0 0 300 80">
                                                <path class="emg-waveform" d="M0,40 L10,35 L20,45 L30,30 L40,50 L50,25 L60,55 L70,35 L80,45 L90,30 L100,50 L110,40 L120,35 L130,45 L140,30 L150,50 L160,25 L170,55 L180,35 L190,45 L200,30 L210,50 L220,40 L230,35 L240,45 L250,30 L260,50 L270,25 L280,55 L290,35 L300,40"></path>
                                            </svg>
                                        </div>
                                        <div class="signal-specs">
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Frequency:" data-lang-ar="التردد:">Frequency:</span>
                                                <span class="spec-value">10-500 Hz</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Amplitude:" data-lang-ar="السعة:">Amplitude:</span>
                                                <span class="spec-value">0.1-5 mV</span>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-label" data-lang-en="Source:" data-lang-ar="المصدر:">Source:</span>
                                                <span class="spec-value" data-lang-en="Muscle fibers" data-lang-ar="ألياف العضلات">Muscle fibers</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3: EEG Frequency Bands Interactive -->
                    <div class="slide" data-slide="3">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="EEG Frequency Bands Analysis" data-lang-ar="تحليل نطاقات تردد تخطيط الدماغ">EEG Frequency Bands Analysis</h2>
                                <div class="slide-icon wave-animation">🧠</div>
                            </div>
                            <div class="eeg-frequency-analysis">
                                <div class="frequency-bands">
                                    <div class="band-selector">
                                        <h4 data-lang-en="Select EEG Band:" data-lang-ar="اختر نطاق تخطيط الدماغ:">Select EEG Band:</h4>
                                        <div class="band-buttons">
                                            <button class="band-btn active" data-band="delta" onclick="selectEEGBand('delta')">
                                                <span class="band-name">Delta</span>
                                                <span class="band-freq">0.5-4 Hz</span>
                                            </button>
                                            <button class="band-btn" data-band="theta" onclick="selectEEGBand('theta')">
                                                <span class="band-name">Theta</span>
                                                <span class="band-freq">4-8 Hz</span>
                                            </button>
                                            <button class="band-btn" data-band="alpha" onclick="selectEEGBand('alpha')">
                                                <span class="band-name">Alpha</span>
                                                <span class="band-freq">8-13 Hz</span>
                                            </button>
                                            <button class="band-btn" data-band="beta" onclick="selectEEGBand('beta')">
                                                <span class="band-name">Beta</span>
                                                <span class="band-freq">13-30 Hz</span>
                                            </button>
                                            <button class="band-btn" data-band="gamma" onclick="selectEEGBand('gamma')">
                                                <span class="band-name">Gamma</span>
                                                <span class="band-freq">30-100 Hz</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="band-visualization">
                                        <div class="eeg-display">
                                            <div class="eeg-channels">
                                                <div class="channel" data-channel="Fp1">
                                                    <span class="channel-label">Fp1</span>
                                                    <div class="channel-waveform">
                                                        <svg class="channel-svg" viewBox="0 0 400 60">
                                                            <path class="eeg-signal delta-signal" id="fp1-signal" d="M0,30 Q50,20 100,30 Q150,40 200,30 Q250,20 300,30 Q350,40 400,30"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="channel" data-channel="C3">
                                                    <span class="channel-label">C3</span>
                                                    <div class="channel-waveform">
                                                        <svg class="channel-svg" viewBox="0 0 400 60">
                                                            <path class="eeg-signal delta-signal" id="c3-signal" d="M0,30 Q40,25 80,30 Q120,35 160,30 Q200,25 240,30 Q280,35 320,30 Q360,25 400,30"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="channel" data-channel="O1">
                                                    <span class="channel-label">O1</span>
                                                    <div class="channel-waveform">
                                                        <svg class="channel-svg" viewBox="0 0 400 60">
                                                            <path class="eeg-signal delta-signal" id="o1-signal" d="M0,30 Q60,15 120,30 Q180,45 240,30 Q300,15 360,30 L400,30"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="band-info" id="band-info">
                                            <h5 id="band-title">Delta Waves (0.5-4 Hz)</h5>
                                            <p id="band-description" data-lang-en="Associated with deep sleep and unconscious states. High amplitude, low frequency waves." data-lang-ar="مرتبطة بالنوم العميق والحالات اللاواعية. موجات عالية السعة ومنخفضة التردد.">Associated with deep sleep and unconscious states. High amplitude, low frequency waves.</p>
                                            <div class="band-characteristics">
                                                <div class="characteristic">
                                                    <span class="char-label" data-lang-en="State:" data-lang-ar="الحالة:">State:</span>
                                                    <span class="char-value" id="band-state" data-lang-en="Deep sleep" data-lang-ar="النوم العميق">Deep sleep</span>
                                                </div>
                                                <div class="characteristic">
                                                    <span class="char-label" data-lang-en="Amplitude:" data-lang-ar="السعة:">Amplitude:</span>
                                                    <span class="char-value" id="band-amplitude">100-200 μV</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script src="js/slide-deck.js"></script>
    <script src="js/biosignal-slides.js"></script>
</body>
</html>
