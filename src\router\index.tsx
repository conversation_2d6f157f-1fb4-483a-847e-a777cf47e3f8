
import { createBrowserRouter } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import Home from '../pages/Home';
import Courses from '../pages/Courses';
import InteractiveLectures from '../pages/InteractiveLectures';
import Training from '../pages/Training';
import VirtualLab from '../pages/VirtualLab';

const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { path: '/', element: <Home /> },
      { path: '/courses', element: <Courses /> },
      { path: '/interactive-lectures', element: <InteractiveLectures /> },
      { path: '/training', element: <Training /> },
      { path: '/virtual-lab', element: <VirtualLab /> },
    ],
  },
]);

export default router;
