/* ===== BIOSIGNAL PROCESSING SLIDES STYLES ===== */

/* Signal Types Overview */
.signal-types-overview {
    padding: 2rem 0;
}

.signal-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.signal-type-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.signal-type-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: #2563eb;
}

.signal-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid var(--gray-200);
}

.signal-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.signal-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.signal-waveform {
    padding: 1.5rem;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.waveform-svg {
    width: 100%;
    height: 80px;
}

.ecg-waveform {
    fill: none;
    stroke: #00ff00;
    stroke-width: 2;
    stroke-dasharray: 600;
    stroke-dashoffset: 600;
    animation: signal-trace 4s ease-in-out infinite;
}

.eeg-waveform {
    fill: none;
    stroke: #00aaff;
    stroke-width: 2;
    stroke-dasharray: 400;
    stroke-dashoffset: 400;
    animation: signal-trace 3s ease-in-out infinite;
}

.emg-waveform {
    fill: none;
    stroke: #ff6b6b;
    stroke-width: 1.5;
    stroke-dasharray: 800;
    stroke-dashoffset: 800;
    animation: signal-trace 2s ease-in-out infinite;
}

@keyframes signal-trace {
    to {
        stroke-dashoffset: 0;
    }
}

.signal-specs {
    padding: 1.5rem;
    background: var(--gray-50);
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.spec-value {
    font-weight: 500;
    color: var(--gray-600);
    font-size: 0.875rem;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--gray-300);
}

/* EEG Frequency Bands */
.eeg-frequency-analysis {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.frequency-bands {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 1rem;
    padding: 2rem;
}

.band-selector {
    margin-bottom: 2rem;
}

.band-selector h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    text-align: center;
}

.band-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.band-btn {
    background: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 100px;
}

.band-btn:hover {
    border-color: #2563eb;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.band-btn.active {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    border-color: #1d4ed8;
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.band-name {
    font-weight: 600;
    font-size: 0.875rem;
}

.band-freq {
    font-size: 0.75rem;
    opacity: 0.8;
}

.band-visualization {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.eeg-display {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
}

.eeg-channels {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.channel {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #000;
    border-radius: 0.5rem;
}

.channel-label {
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
    min-width: 40px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.channel-waveform {
    flex: 1;
}

.channel-svg {
    width: 100%;
    height: 60px;
}

.eeg-signal {
    fill: none;
    stroke-width: 2;
    transition: all var(--transition-fast);
}

.delta-signal {
    stroke: #ff6b6b;
    animation: eeg-delta 4s ease-in-out infinite;
}

.theta-signal {
    stroke: #ffa500;
    animation: eeg-theta 3s ease-in-out infinite;
}

.alpha-signal {
    stroke: #00ff00;
    animation: eeg-alpha 2s ease-in-out infinite;
}

.beta-signal {
    stroke: #00aaff;
    animation: eeg-beta 1.5s ease-in-out infinite;
}

.gamma-signal {
    stroke: #ff00ff;
    animation: eeg-gamma 1s ease-in-out infinite;
}

@keyframes eeg-delta {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes eeg-theta {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes eeg-alpha {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

@keyframes eeg-beta {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes eeg-gamma {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
}

.band-info {
    background: var(--white);
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    height: fit-content;
}

.band-info h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    text-align: center;
}

.band-info p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.band-characteristics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.characteristic {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 4px solid #2563eb;
}

.char-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.char-value {
    font-weight: 500;
    color: var(--gray-600);
    font-size: 0.875rem;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--gray-300);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .signal-comparison {
        grid-template-columns: 1fr;
    }
    
    .band-visualization {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .band-buttons {
        gap: 0.5rem;
    }
    
    .band-btn {
        min-width: 80px;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 768px) {
    .band-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .band-btn {
        width: 200px;
    }
    
    .channel {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .channel-label {
        min-width: auto;
    }
    
    .characteristic {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .signal-type-card {
        margin: 0 1rem;
    }
    
    .frequency-bands {
        padding: 1.5rem;
    }
    
    .band-info {
        padding: 1.5rem;
    }
    
    .eeg-display {
        padding: 1rem;
    }
    
    .channel-svg {
        height: 40px;
    }
}

/* Additional Animations */
@keyframes brainwave-pulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.05);
        opacity: 1;
    }
}

.signal-icon.wave-animation {
    animation: brainwave-pulse 2s ease-in-out infinite;
}

/* Signal Processing Specific Styles */
.biosignal-slides-page .slide-deck-header {
    background: linear-gradient(135deg, #059669, #10b981);
}

.biosignal-slides-page .control-btn {
    background: rgba(255, 255, 255, 0.2);
}

.biosignal-slides-page .control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
}

/* Interactive Elements */
.interactive-element {
    cursor: pointer;
    transition: all var(--transition-fast);
}

.interactive-element:hover {
    transform: scale(1.02);
}

.processing-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 1.5s ease-in-out infinite;
    margin-left: 0.5rem;
}

.processing-indicator.active {
    background: #ef4444;
    animation: pulse 0.5s ease-in-out infinite;
}

/* Frequency Band Colors */
.band-btn[data-band="delta"].active {
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

.band-btn[data-band="theta"].active {
    background: linear-gradient(135deg, #ea580c, #f97316);
}

.band-btn[data-band="alpha"].active {
    background: linear-gradient(135deg, #059669, #10b981);
}

.band-btn[data-band="beta"].active {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.band-btn[data-band="gamma"].active {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}
