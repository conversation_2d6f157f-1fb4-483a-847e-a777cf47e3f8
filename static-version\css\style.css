/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-blue: #2563eb;
    --primary-purple: #7c3aed;
    --secondary-green: #059669;
    --accent-orange: #ea580c;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-green), var(--primary-blue));
    --gradient-accent: linear-gradient(135deg, var(--accent-orange), var(--primary-purple));
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 4rem 0;
    --card-padding: 2rem;
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-arabic: 'Noto Sans Arabic', sans-serif;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Base Typography */
body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    transition: font-family var(--transition-normal);
}

/* Arabic Language Support */
html[lang="ar"] body {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

html[lang="ar"] .container {
    direction: rtl;
}

/* RTL Spacing Adjustments */
html[lang="ar"] .space-x-2 > * + * {
    margin-left: 0;
    margin-right: 0.5rem;
}

html[lang="ar"] .space-x-3 > * + * {
    margin-left: 0;
    margin-right: 0.75rem;
}

html[lang="ar"] .space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== HEADER STYLES ===== */
.header {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    flex-wrap: wrap;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.subtitle {
    font-size: 0.75rem;
    opacity: 0.8;
    margin: 0;
}

/* Navigation */
.nav-desktop {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Header Controls */
.header-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lang-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.lang-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
}

.flag {
    font-size: 1rem;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.hamburger-line {
    width: 1.5rem;
    height: 2px;
    background: var(--white);
    transition: all var(--transition-normal);
    border-radius: 1px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.nav-mobile {
    display: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
}

.nav-mobile.active {
    display: block;
    animation: slideDown var(--transition-normal);
}

.mobile-nav-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-nav-link {
    color: var(--white);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mobile-nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
}

.header-progress {
    height: 3px;
    background: var(--gradient-accent);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    padding: var(--section-padding);
}

/* ===== HERO SECTION ===== */
.hero {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--gradient-secondary);
    border-radius: 1rem;
    margin-bottom: 3rem;
    color: var(--white);
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-blue);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* ===== STATS SECTION ===== */
.stats {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* ===== FEATURES SECTION ===== */
.features {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
    color: var(--gray-800);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--white);
    padding: var(--card-padding);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--gray-800);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== CTA SECTION ===== */
.cta {
    background: var(--gray-100);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    margin-bottom: 3rem;
}

.cta-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--gray-800);
}

.cta-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
    max-width: 36rem;
    margin-left: auto;
    margin-right: auto;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gradient-primary);
    color: var(--white);
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 3rem 0;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.footer-subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.footer-description {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-author h4,
.footer-links h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.author-info p {
    margin-bottom: 0.5rem;
}

.contact-info {
    margin-top: 1rem;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.contact-info a {
    color: var(--white);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
}

.footer-links a:hover {
    opacity: 1;
    text-decoration: underline;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 0;
    text-align: center;
}

.footer-bottom p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.footer-love {
    font-size: 0.875rem;
}

.footer-gradient {
    height: 3px;
    background: var(--gradient-accent);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (min-width: 768px) {
    .nav-desktop {
        display: block;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
}

@media (max-width: 767px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .cta-title {
        font-size: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .hero {
        padding: 2rem 1rem;
        margin-bottom: 2rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .cta {
        padding: 2rem 1rem;
    }
}

/* ===== PAGE-SPECIFIC STYLES ===== */

/* Page Header */
.page-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 3rem 2rem;
    border-radius: 1rem;
    margin-bottom: 3rem;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.page-description {
    font-size: 1.125rem;
    opacity: 0.8;
    max-width: 48rem;
    margin: 0 auto;
}

/* Modules Grid */
.modules-section {
    margin-bottom: 3rem;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.module-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.module-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.module-header {
    background: var(--gradient-secondary);
    color: var(--white);
    padding: 2rem;
    text-align: center;
}

.module-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

.module-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.module-content {
    padding: 2rem;
}

.module-description {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.module-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.stat-label {
    color: var(--gray-500);
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: var(--gray-700);
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-badge.beginner {
    background: #dcfce7;
    color: #166534;
}

.difficulty-badge.intermediate {
    background: #fef3c7;
    color: #92400e;
}

.difficulty-badge.advanced {
    background: #fee2e2;
    color: #991b1b;
}

.module-btn {
    width: 100%;
    justify-content: center;
}

/* Author Section */
.author-section {
    margin-bottom: 3rem;
}

.author-card {
    background: var(--gray-100);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
}

.author-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--gray-800);
}

.author-content p {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.author-contact {
    margin-top: 1rem;
}

.author-contact p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.author-contact a {
    color: var(--primary-blue);
    text-decoration: none;
}

.author-contact a:hover {
    text-decoration: underline;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for accessibility */
.nav-link:focus,
.mobile-nav-link:focus,
.btn:focus,
.lang-toggle:focus {
    outline: 2px solid var(--white);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --gray-600: #000000;
        --gray-700: #000000;
        --gray-800: #000000;
    }
}
