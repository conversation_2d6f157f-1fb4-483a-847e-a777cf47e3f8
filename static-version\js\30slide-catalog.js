/**
 * 30-Slide Catalog JavaScript
 * BioEngage LMS - Module Catalog for 30-Slide Interactive Presentations
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let moduleData = {
    fundamentals: {
        name: { en: 'BME Fundamentals & Instrumentation', ar: 'أساسيات الهندسة الطبية الحيوية والأجهزة' },
        description: { 
            en: 'Comprehensive introduction to biomedical engineering principles, human anatomy, and medical instrumentation',
            ar: 'مقدمة شاملة لمبادئ الهندسة الطبية الحيوية والتشريح البشري والأجهزة الطبية'
        },
        status: 'available',
        url: 'bme-fundamentals-30slides.html',
        duration: 90,
        rating: 4.9,
        features: [
            { en: 'Interactive Anatomy', ar: 'التشريح التفاعلي' },
            { en: 'BME Timeline', ar: 'الجدول الزمني للهندسة الطبية الحيوية' },
            { en: 'Career Paths', ar: 'المسارات المهنية' }
        ]
    },
    imaging: {
        name: { en: 'Medical Imaging Systems', ar: 'أنظمة التصوير الطبي' },
        description: { 
            en: 'Advanced medical imaging technologies including X-ray, CT, MRI, ultrasound, and nuclear medicine',
            ar: 'تقنيات التصوير الطبي المتقدمة بما في ذلك الأشعة السينية والمقطعية والرنين المغناطيسي والموجات فوق الصوتية والطب النووي'
        },
        status: 'available',
        url: 'medical-imaging-30slides.html',
        duration: 120,
        rating: 4.8,
        features: [
            { en: 'X-Ray Simulator', ar: 'محاكي الأشعة السينية' },
            { en: 'MRI Physics', ar: 'فيزياء الرنين المغناطيسي' },
            { en: 'Ultrasound Demo', ar: 'عرض الموجات فوق الصوتية' }
        ]
    },
    biosignals: {
        name: { en: 'Biosignal Processing & Analysis', ar: 'معالجة وتحليل الإشارات الحيوية' },
        description: { 
            en: 'Advanced signal processing techniques for EEG, EMG, ECG, and other physiological signals',
            ar: 'تقنيات معالجة الإشارات المتقدمة لتخطيط الدماغ والعضلات والقلب والإشارات الفسيولوجية الأخرى'
        },
        status: 'coming-soon',
        url: null,
        duration: 100,
        rating: null,
        features: [
            { en: 'EEG Analysis', ar: 'تحليل تخطيط الدماغ' },
            { en: 'Signal Filters', ar: 'مرشحات الإشارات' },
            { en: 'Real-time Processing', ar: 'المعالجة في الوقت الفعلي' }
        ]
    },
    biomechanics: {
        name: { en: 'Biomechanics & Rehabilitation', ar: 'الميكانيكا الحيوية والتأهيل' },
        description: { 
            en: 'Motion analysis, gait studies, and rehabilitation engineering with interactive demonstrations',
            ar: 'تحليل الحركة ودراسات المشي وهندسة التأهيل مع العروض التوضيحية التفاعلية'
        },
        status: 'coming-soon',
        url: null,
        duration: 110,
        rating: null,
        features: [
            { en: 'Gait Analysis', ar: 'تحليل المشي' },
            { en: 'Motion Capture', ar: 'التقاط الحركة' },
            { en: 'Prosthetics Design', ar: 'تصميم الأطراف الصناعية' }
        ]
    },
    tissue: {
        name: { en: 'Tissue Engineering & Biomaterials', ar: 'هندسة الأنسجة والمواد الحيوية' },
        description: { 
            en: 'Cellular engineering, scaffold design, and biomaterial properties with virtual lab experiments',
            ar: 'هندسة الخلايا وتصميم السقالات وخصائص المواد الحيوية مع تجارب المعمل الافتراضي'
        },
        status: 'coming-soon',
        url: null,
        duration: 105,
        rating: null,
        features: [
            { en: 'Cell Culture', ar: 'زراعة الخلايا' },
            { en: 'Scaffold Design', ar: 'تصميم السقالات' },
            { en: 'Biomaterial Testing', ar: 'اختبار المواد الحيوية' }
        ]
    },
    devices: {
        name: { en: 'Medical Device Design', ar: 'تصميم الأجهزة الطبية' },
        description: { 
            en: 'Medical device development, regulatory affairs, and quality assurance in healthcare technology',
            ar: 'تطوير الأجهزة الطبية والشؤون التنظيمية وضمان الجودة في تكنولوجيا الرعاية الصحية'
        },
        status: 'coming-soon',
        url: null,
        duration: 95,
        rating: null,
        features: [
            { en: 'Device Prototyping', ar: 'نمذجة الأجهزة' },
            { en: 'FDA Regulations', ar: 'لوائح إدارة الغذاء والدواء' },
            { en: 'Quality Systems', ar: 'أنظمة الجودة' }
        ]
    },
    bioinformatics: {
        name: { en: 'Bioinformatics & Computational Biology', ar: 'المعلوماتية الحيوية والبيولوجيا الحاسوبية' },
        description: { 
            en: 'Computational methods for biological data analysis, genomics, and personalized medicine',
            ar: 'الطرق الحاسوبية لتحليل البيانات البيولوجية وعلم الجينوم والطب الشخصي'
        },
        status: 'coming-soon',
        url: null,
        duration: 115,
        rating: null,
        features: [
            { en: 'Genomic Analysis', ar: 'التحليل الجيني' },
            { en: 'Machine Learning', ar: 'التعلم الآلي' },
            { en: 'Data Visualization', ar: 'تصور البيانات' }
        ]
    },
    neural: {
        name: { en: 'Neural Engineering & Brain-Computer Interfaces', ar: 'الهندسة العصبية وواجهات الدماغ والحاسوب' },
        description: { 
            en: 'Neural signal processing, brain-computer interfaces, and neuroprosthetics technology',
            ar: 'معالجة الإشارات العصبية وواجهات الدماغ والحاسوب وتكنولوجيا الأطراف الصناعية العصبية'
        },
        status: 'coming-soon',
        url: null,
        duration: 125,
        rating: null,
        features: [
            { en: 'Neural Interfaces', ar: 'الواجهات العصبية' },
            { en: 'Brain Stimulation', ar: 'تحفيز الدماغ' },
            { en: 'Neuroprosthetics', ar: 'الأطراف الصناعية العصبية' }
        ]
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeCatalog();
});

function initializeCatalog() {
    // Initialize module interactions
    initializeModuleCards();
    
    // Initialize floating module icons
    initializeFloatingIcons();
    
    // Initialize hero animations
    initializeHeroAnimations();
    
    console.log('30-slide catalog initialized successfully');
}

// ===== MODULE CARD INTERACTIONS =====
function initializeModuleCards() {
    const moduleCards = document.querySelectorAll('.module-card');
    moduleCards.forEach(card => {
        const moduleType = card.dataset.module;
        
        // Add hover effects
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-12px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
        });
        
        // Add feature tag interactions
        const featureTags = card.querySelectorAll('.feature-tag');
        featureTags.forEach(tag => {
            tag.addEventListener('click', () => {
                const featureText = tag.textContent;
                const currentLang = document.documentElement.lang || 'en';
                const message = currentLang === 'en' 
                    ? `Feature highlight: ${featureText}`
                    : `تسليط الضوء على الميزة: ${featureText}`;
                
                showCatalogNotification(message, 'info');
                
                // Animate feature tag
                tag.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    tag.style.transform = '';
                }, 300);
            });
        });
        
        // Add stat interactions
        const stats = card.querySelectorAll('.stat');
        stats.forEach(stat => {
            stat.addEventListener('click', () => {
                const statValue = stat.querySelector('.stat-value').textContent;
                const statLabel = stat.querySelector('.stat-label').textContent;
                const currentLang = document.documentElement.lang || 'en';
                const message = currentLang === 'en' 
                    ? `Module statistic: ${statValue} ${statLabel}`
                    : `إحصائية الوحدة: ${statValue} ${statLabel}`;
                
                showCatalogNotification(message, 'info');
            });
        });
    });
}

// ===== FLOATING ICONS ANIMATION =====
function initializeFloatingIcons() {
    const floatingIcons = document.querySelectorAll('.module-icon');
    floatingIcons.forEach((icon, index) => {
        // Add click interaction
        icon.addEventListener('click', () => {
            const moduleType = icon.dataset.module;
            const moduleInfo = moduleData[moduleType];
            if (moduleInfo) {
                const currentLang = document.documentElement.lang || 'en';
                const moduleName = moduleInfo.name[currentLang];
                const message = currentLang === 'en' 
                    ? `Quick access: ${moduleName}`
                    : `الوصول السريع: ${moduleName}`;
                
                showCatalogNotification(message, 'info');
                
                // Scroll to corresponding module card
                const moduleCard = document.querySelector(`[data-module="${moduleType}"]`);
                if (moduleCard) {
                    moduleCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    
                    // Highlight the card
                    moduleCard.style.boxShadow = '0 0 30px rgba(59, 130, 246, 0.5)';
                    setTimeout(() => {
                        moduleCard.style.boxShadow = '';
                    }, 2000);
                }
            }
        });
        
        // Add hover effect
        icon.addEventListener('mouseenter', () => {
            icon.style.transform = 'scale(1.2)';
            icon.style.background = 'rgba(255, 255, 255, 0.2)';
        });
        
        icon.addEventListener('mouseleave', () => {
            icon.style.transform = '';
            icon.style.background = '';
        });
    });
}

// ===== HERO ANIMATIONS =====
function initializeHeroAnimations() {
    // Animate hero stats on scroll
    const heroStats = document.querySelectorAll('.hero-stats .stat-item');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.8s ease-out forwards';
            }
        });
    }, { threshold: 0.5 });
    
    heroStats.forEach(stat => {
        observer.observe(stat);
    });
}

// ===== MODULE ACTIONS =====
function previewModule(moduleType) {
    const moduleInfo = moduleData[moduleType];
    if (!moduleInfo) return;
    
    const currentLang = document.documentElement.lang || 'en';
    const moduleName = moduleInfo.name[currentLang];
    
    if (moduleInfo.status === 'available') {
        const message = currentLang === 'en' 
            ? `Opening preview for ${moduleName}... Loading interactive content!`
            : `فتح معاينة لـ ${moduleName}... تحميل المحتوى التفاعلي!`;
        
        showCatalogNotification(message, 'info');
        
        // Simulate loading and open module
        setTimeout(() => {
            if (moduleInfo.url) {
                window.open(moduleInfo.url, '_blank');
            }
        }, 1000);
    } else {
        const message = currentLang === 'en' 
            ? `${moduleName} is coming soon! We'll notify you when it's ready.`
            : `${moduleName} قريباً! سنخبرك عندما يكون جاهزاً.`;
        
        showCatalogNotification(message, 'warning');
    }
}

function notifyWhenReady(moduleType) {
    const moduleInfo = moduleData[moduleType];
    if (!moduleInfo) return;
    
    const currentLang = document.documentElement.lang || 'en';
    const moduleName = moduleInfo.name[currentLang];
    const message = currentLang === 'en' 
        ? `You'll be notified when ${moduleName} is ready! Expected release: Q2 2024`
        : `سيتم إخطارك عندما يكون ${moduleName} جاهزاً! الإصدار المتوقع: الربع الثاني 2024`;
    
    showCatalogNotification(message, 'success');
    
    // Add to notification list (simulate)
    const moduleCard = document.querySelector(`[data-module="${moduleType}"]`);
    if (moduleCard) {
        const notifyBtn = moduleCard.querySelector('.btn-secondary');
        if (notifyBtn) {
            notifyBtn.innerHTML = '<i class="fas fa-check"></i><span>Notification Set</span>';
            notifyBtn.style.background = '#10b981';
            notifyBtn.style.color = 'white';
            notifyBtn.style.borderColor = '#10b981';
        }
    }
}

// ===== UTILITY FUNCTIONS =====
function showCatalogNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `catalog-notification catalog-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getModuleStatistics() {
    const totalModules = Object.keys(moduleData).length;
    const availableModules = Object.values(moduleData).filter(m => m.status === 'available').length;
    const totalSlides = totalModules * 30;
    const totalDuration = Object.values(moduleData).reduce((sum, m) => sum + m.duration, 0);
    
    return {
        totalModules,
        availableModules,
        totalSlides,
        totalDuration,
        completionRate: (availableModules / totalModules) * 100
    };
}

// ===== GLOBAL FUNCTIONS FOR HTML ONCLICK =====
window.previewModule = previewModule;
window.notifyWhenReady = notifyWhenReady;

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Catalog language changed to: ${newLang}`);
    
    // Update any dynamic content if needed
    const stats = getModuleStatistics();
    console.log('Module statistics:', stats);
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        previewModule,
        notifyWhenReady,
        getModuleStatistics,
        showCatalogNotification,
        initializeModuleCards,
        initializeFloatingIcons
    };
}
