<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-en="BME Fundamentals & Instrumentation - 30 Interactive Slides" data-lang-ar="أساسيات الهندسة الطبية الحيوية والأجهزة - 30 شريحة تفاعلية">BME Fundamentals & Instrumentation - 30 Interactive Slides</title>
    <meta name="description" content="Comprehensive 30-slide interactive presentation covering biomedical engineering fundamentals and medical instrumentation">
    <meta name="keywords" content="biomedical engineering, medical instrumentation, BME fundamentals, interactive education">
    <meta name="author" content="Dr<PERSON>, SUST - BME">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧬</text></svg>">
    
    <!-- Fonts for bilingual support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slide-deck.css">
    <link rel="stylesheet" href="css/30slide-modules.css">
</head>
<body class="bme-fundamentals-30slides-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo and Title -->
                <div class="logo-section">
                    <div class="logo-icon">🧬</div>
                    <div class="logo-text">
                        <h1 data-lang-en="BioEngage" data-lang-ar="بايو إنجيج">BioEngage</h1>
                        <p class="subtitle" data-lang-en="Interactive Virtual LMS" data-lang-ar="نظام إدارة التعلم الافتراضي التفاعلي">Interactive Virtual LMS</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="nav-desktop">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                        <li><a href="modules.html" class="nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                        <li><a href="extended-modules.html" class="nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                        <li><a href="interactive_lectures.html" class="nav-link active" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                        <li><a href="training.html" class="nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                        <li><a href="virtual_lab.html" class="nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                    </ul>
                </nav>

                <!-- Language Toggle and Mobile Menu -->
                <div class="header-controls">
                    <button id="lang-toggle" class="lang-toggle" data-lang="en">
                        <span class="flag">🇸🇦</span>
                        <span class="lang-text" data-lang-en="العربية" data-lang-ar="English">العربية</span>
                    </button>
                    <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <nav id="mobile-nav" class="nav-mobile">
                <ul class="mobile-nav-list">
                    <li><a href="index.html" class="mobile-nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                    <li><a href="modules.html" class="mobile-nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                    <li><a href="extended-modules.html" class="mobile-nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                    <li><a href="interactive_lectures.html" class="mobile-nav-link" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                    <li><a href="training.html" class="mobile-nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                    <li><a href="virtual_lab.html" class="mobile-nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                </ul>
            </nav>
        </div>
        <div class="header-progress"></div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Slide Deck Container -->
            <div class="slide-deck-container">
                <!-- Slide Deck Header -->
                <div class="slide-deck-header">
                    <div class="deck-info">
                        <h1 class="deck-title" data-lang-en="BME Fundamentals & Instrumentation" data-lang-ar="أساسيات الهندسة الطبية الحيوية والأجهزة">BME Fundamentals & Instrumentation</h1>
                        <p class="deck-subtitle" data-lang-en="Comprehensive 30-Slide Interactive Journey Through Biomedical Engineering" data-lang-ar="رحلة تفاعلية شاملة من 30 شريحة عبر الهندسة الطبية الحيوية">Comprehensive 30-Slide Interactive Journey Through Biomedical Engineering</p>
                    </div>
                    
                    <div class="deck-controls">
                        <div class="slide-counter">
                            <span id="current-slide">1</span> / <span id="total-slides">30</span>
                        </div>
                        <div class="control-buttons">
                            <button id="prev-slide" class="control-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="play-pause" class="control-btn">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="next-slide" class="control-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <button id="fullscreen-btn" class="control-btn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="slide-progress-container">
                    <div class="slide-progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-sections">
                        <div class="section-marker" data-section="introduction" data-slides="1-5">
                            <span data-lang-en="Introduction" data-lang-ar="المقدمة">Introduction</span>
                        </div>
                        <div class="section-marker" data-section="fundamentals" data-slides="6-15">
                            <span data-lang-en="Fundamentals" data-lang-ar="الأساسيات">Fundamentals</span>
                        </div>
                        <div class="section-marker" data-section="instrumentation" data-slides="16-25">
                            <span data-lang-en="Instrumentation" data-lang-ar="الأجهزة">Instrumentation</span>
                        </div>
                        <div class="section-marker" data-section="applications" data-slides="26-30">
                            <span data-lang-en="Applications" data-lang-ar="التطبيقات">Applications</span>
                        </div>
                    </div>
                </div>

                <!-- Slide Container -->
                <div class="slide-container" id="slide-container">
                    <!-- Slide 1: Title Slide -->
                    <div class="slide active" data-slide="1" data-section="introduction">
                        <div class="slide-content title-slide">
                            <div class="title-animation">
                                <div class="animated-icon pulse-animation">🧬</div>
                                <h1 data-lang-en="Biomedical Engineering Fundamentals & Instrumentation" data-lang-ar="أساسيات الهندسة الطبية الحيوية والأجهزة">Biomedical Engineering Fundamentals & Instrumentation</h1>
                                <h2 data-lang-en="A Comprehensive Interactive Journey Through BME Principles" data-lang-ar="رحلة تفاعلية شاملة عبر مبادئ الهندسة الطبية الحيوية">A Comprehensive Interactive Journey Through BME Principles</h2>
                            </div>
                            <div class="title-features">
                                <div class="feature-item fade-in-up" data-delay="0.2s">
                                    <div class="feature-icon heartbeat-animation">💓</div>
                                    <span data-lang-en="Physiological Systems" data-lang-ar="الأنظمة الفسيولوجية">Physiological Systems</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.4s">
                                    <div class="feature-icon rotate-animation">⚙️</div>
                                    <span data-lang-en="Medical Instrumentation" data-lang-ar="الأجهزة الطبية">Medical Instrumentation</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.6s">
                                    <div class="feature-icon wave-animation">📊</div>
                                    <span data-lang-en="Signal Processing" data-lang-ar="معالجة الإشارات">Signal Processing</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.8s">
                                    <div class="feature-icon bounce-animation">🔬</div>
                                    <span data-lang-en="Virtual Laboratory" data-lang-ar="المعمل الافتراضي">Virtual Laboratory</span>
                                </div>
                            </div>
                            <div class="author-info fade-in" data-delay="1.0s">
                                <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                                <p data-lang-en="SUST - Biomedical Engineering Department" data-lang-ar="جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية">SUST - Biomedical Engineering Department</p>
                                <div class="course-stats">
                                    <span class="stat-item">
                                        <i class="fas fa-clock"></i>
                                        <span data-lang-en="90 minutes" data-lang-ar="90 دقيقة">90 minutes</span>
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-users"></i>
                                        <span data-lang-en="3,247 students" data-lang-ar="3,247 طالب">3,247 students</span>
                                    </span>
                                    <span class="stat-item">
                                        <i class="fas fa-star"></i>
                                        <span>4.9/5</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2: Learning Objectives -->
                    <div class="slide" data-slide="2" data-section="introduction">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Learning Objectives" data-lang-ar="أهداف التعلم">Learning Objectives</h2>
                                <div class="slide-icon pulse-animation">🎯</div>
                            </div>
                            <div class="objectives-container">
                                <div class="objectives-intro">
                                    <p data-lang-en="By the end of this comprehensive 30-slide presentation, you will master:" data-lang-ar="بنهاية هذا العرض الشامل المكون من 30 شريحة، ستتقن:">By the end of this comprehensive 30-slide presentation, you will master:</p>
                                </div>
                                <div class="objectives-grid">
                                    <div class="objective-card animated-card hover-lift" data-delay="0.1s">
                                        <div class="objective-icon">🧬</div>
                                        <h3 data-lang-en="BME Fundamentals" data-lang-ar="أساسيات الهندسة الطبية الحيوية">BME Fundamentals</h3>
                                        <ul>
                                            <li data-lang-en="Understand the interdisciplinary nature of biomedical engineering" data-lang-ar="فهم الطبيعة متعددة التخصصات للهندسة الطبية الحيوية">Understand the interdisciplinary nature of biomedical engineering</li>
                                            <li data-lang-en="Explore the integration of engineering and biological sciences" data-lang-ar="استكشاف تكامل الهندسة والعلوم البيولوجية">Explore the integration of engineering and biological sciences</li>
                                            <li data-lang-en="Identify key application areas and career paths" data-lang-ar="تحديد مجالات التطبيق الرئيسية والمسارات المهنية">Identify key application areas and career paths</li>
                                        </ul>
                                    </div>
                                    <div class="objective-card animated-card hover-lift" data-delay="0.2s">
                                        <div class="objective-icon">⚙️</div>
                                        <h3 data-lang-en="Medical Instrumentation" data-lang-ar="الأجهزة الطبية">Medical Instrumentation</h3>
                                        <ul>
                                            <li data-lang-en="Master the principles of medical device design" data-lang-ar="إتقان مبادئ تصميم الأجهزة الطبية">Master the principles of medical device design</li>
                                            <li data-lang-en="Understand sensor technologies and signal acquisition" data-lang-ar="فهم تقنيات أجهزة الاستشعار واكتساب الإشارات">Understand sensor technologies and signal acquisition</li>
                                            <li data-lang-en="Learn safety standards and regulatory requirements" data-lang-ar="تعلم معايير السلامة والمتطلبات التنظيمية">Learn safety standards and regulatory requirements</li>
                                        </ul>
                                    </div>
                                    <div class="objective-card animated-card hover-lift" data-delay="0.3s">
                                        <div class="objective-icon">📊</div>
                                        <h3 data-lang-en="Signal Processing" data-lang-ar="معالجة الإشارات">Signal Processing</h3>
                                        <ul>
                                            <li data-lang-en="Analyze physiological signals and their characteristics" data-lang-ar="تحليل الإشارات الفسيولوجية وخصائصها">Analyze physiological signals and their characteristics</li>
                                            <li data-lang-en="Apply digital filtering and noise reduction techniques" data-lang-ar="تطبيق تقنيات الترشيح الرقمي وتقليل الضوضاء">Apply digital filtering and noise reduction techniques</li>
                                            <li data-lang-en="Implement real-time signal processing algorithms" data-lang-ar="تنفيذ خوارزميات معالجة الإشارات في الوقت الفعلي">Implement real-time signal processing algorithms</li>
                                        </ul>
                                    </div>
                                    <div class="objective-card animated-card hover-lift" data-delay="0.4s">
                                        <div class="objective-icon">🔬</div>
                                        <h3 data-lang-en="Practical Applications" data-lang-ar="التطبيقات العملية">Practical Applications</h3>
                                        <ul>
                                            <li data-lang-en="Operate virtual laboratory equipment and simulations" data-lang-ar="تشغيل معدات المختبر الافتراضي والمحاكاة">Operate virtual laboratory equipment and simulations</li>
                                            <li data-lang-en="Design and test biomedical systems" data-lang-ar="تصميم واختبار الأنظمة الطبية الحيوية">Design and test biomedical systems</li>
                                            <li data-lang-en="Solve real-world clinical engineering problems" data-lang-ar="حل مشاكل الهندسة السريرية في العالم الحقيقي">Solve real-world clinical engineering problems</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3: Course Overview & Structure -->
                    <div class="slide" data-slide="3" data-section="introduction">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Course Structure & Journey Map" data-lang-ar="هيكل الدورة وخريطة الرحلة">Course Structure & Journey Map</h2>
                                <div class="slide-icon rotate-animation">🗺️</div>
                            </div>
                            <div class="course-structure">
                                <div class="journey-timeline">
                                    <div class="timeline-section" data-section="introduction">
                                        <div class="section-header">
                                            <div class="section-icon">🚀</div>
                                            <h3 data-lang-en="Introduction & Foundation" data-lang-ar="المقدمة والأساس">Introduction & Foundation</h3>
                                            <span class="slide-range" data-lang-en="Slides 1-5" data-lang-ar="الشرائح 1-5">Slides 1-5</span>
                                        </div>
                                        <div class="section-content">
                                            <ul>
                                                <li data-lang-en="Course overview and learning objectives" data-lang-ar="نظرة عامة على الدورة وأهداف التعلم">Course overview and learning objectives</li>
                                                <li data-lang-en="History and evolution of biomedical engineering" data-lang-ar="تاريخ وتطور الهندسة الطبية الحيوية">History and evolution of biomedical engineering</li>
                                                <li data-lang-en="Interdisciplinary nature and scope" data-lang-ar="الطبيعة متعددة التخصصات والنطاق">Interdisciplinary nature and scope</li>
                                                <li data-lang-en="Career opportunities and pathways" data-lang-ar="الفرص المهنية والمسارات">Career opportunities and pathways</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="timeline-section" data-section="fundamentals">
                                        <div class="section-header">
                                            <div class="section-icon">🧬</div>
                                            <h3 data-lang-en="Core Fundamentals" data-lang-ar="الأساسيات الأساسية">Core Fundamentals</h3>
                                            <span class="slide-range" data-lang-en="Slides 6-15" data-lang-ar="الشرائح 6-15">Slides 6-15</span>
                                        </div>
                                        <div class="section-content">
                                            <ul>
                                                <li data-lang-en="Human anatomy and physiology for engineers" data-lang-ar="التشريح البشري وعلم وظائف الأعضاء للمهندسين">Human anatomy and physiology for engineers</li>
                                                <li data-lang-en="Biomechanics and tissue properties" data-lang-ar="الميكانيكا الحيوية وخصائص الأنسجة">Biomechanics and tissue properties</li>
                                                <li data-lang-en="Cellular and molecular engineering" data-lang-ar="الهندسة الخلوية والجزيئية">Cellular and molecular engineering</li>
                                                <li data-lang-en="Biocompatibility and biomaterials" data-lang-ar="التوافق الحيوي والمواد الحيوية">Biocompatibility and biomaterials</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="timeline-section" data-section="instrumentation">
                                        <div class="section-header">
                                            <div class="section-icon">⚙️</div>
                                            <h3 data-lang-en="Medical Instrumentation" data-lang-ar="الأجهزة الطبية">Medical Instrumentation</h3>
                                            <span class="slide-range" data-lang-en="Slides 16-25" data-lang-ar="الشرائح 16-25">Slides 16-25</span>
                                        </div>
                                        <div class="section-content">
                                            <ul>
                                                <li data-lang-en="Sensor technologies and transducers" data-lang-ar="تقنيات أجهزة الاستشعار والمحولات">Sensor technologies and transducers</li>
                                                <li data-lang-en="Signal acquisition and conditioning" data-lang-ar="اكتساب الإشارات وتكييفها">Signal acquisition and conditioning</li>
                                                <li data-lang-en="Medical imaging systems" data-lang-ar="أنظمة التصوير الطبي">Medical imaging systems</li>
                                                <li data-lang-en="Therapeutic and diagnostic devices" data-lang-ar="الأجهزة العلاجية والتشخيصية">Therapeutic and diagnostic devices</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="timeline-section" data-section="applications">
                                        <div class="section-header">
                                            <div class="section-icon">🏥</div>
                                            <h3 data-lang-en="Clinical Applications" data-lang-ar="التطبيقات السريرية">Clinical Applications</h3>
                                            <span class="slide-range" data-lang-en="Slides 26-30" data-lang-ar="الشرائح 26-30">Slides 26-30</span>
                                        </div>
                                        <div class="section-content">
                                            <ul>
                                                <li data-lang-en="Case studies and real-world applications" data-lang-ar="دراسات الحالة والتطبيقات في العالم الحقيقي">Case studies and real-world applications</li>
                                                <li data-lang-en="Virtual laboratory experiences" data-lang-ar="تجارب المختبر الافتراضي">Virtual laboratory experiences</li>
                                                <li data-lang-en="Future trends and emerging technologies" data-lang-ar="الاتجاهات المستقبلية والتقنيات الناشئة">Future trends and emerging technologies</li>
                                                <li data-lang-en="Assessment and knowledge validation" data-lang-ar="التقييم والتحقق من المعرفة">Assessment and knowledge validation</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 4: History & Evolution of BME -->
                    <div class="slide" data-slide="4" data-section="introduction">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="History & Evolution of Biomedical Engineering" data-lang-ar="تاريخ وتطور الهندسة الطبية الحيوية">History & Evolution of Biomedical Engineering</h2>
                                <div class="slide-icon rotate-animation">⏳</div>
                            </div>
                            <div class="history-timeline">
                                <div class="timeline-container">
                                    <div class="timeline-item" data-year="1950s" data-delay="0.1s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="Birth of BME" data-lang-ar="ولادة الهندسة الطبية الحيوية">Birth of BME</h3>
                                            <p data-lang-en="First pacemakers and artificial heart-lung machines developed" data-lang-ar="تطوير أول أجهزة تنظيم ضربات القلب وآلات القلب والرئة الاصطناعية">First pacemakers and artificial heart-lung machines developed</p>
                                            <div class="timeline-icon">💓</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item" data-year="1960s" data-delay="0.2s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="Medical Imaging Revolution" data-lang-ar="ثورة التصوير الطبي">Medical Imaging Revolution</h3>
                                            <p data-lang-en="CT scanners and ultrasound technology emerge" data-lang-ar="ظهور أجهزة الأشعة المقطعية وتقنية الموجات فوق الصوتية">CT scanners and ultrasound technology emerge</p>
                                            <div class="timeline-icon">📷</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item" data-year="1970s" data-delay="0.3s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="Biomaterials & Prosthetics" data-lang-ar="المواد الحيوية والأطراف الصناعية">Biomaterials & Prosthetics</h3>
                                            <p data-lang-en="Advanced biomaterials and sophisticated prosthetic devices" data-lang-ar="المواد الحيوية المتقدمة والأجهزة التعويضية المتطورة">Advanced biomaterials and sophisticated prosthetic devices</p>
                                            <div class="timeline-icon">🦾</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item" data-year="1980s" data-delay="0.4s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="Digital Revolution" data-lang-ar="الثورة الرقمية">Digital Revolution</h3>
                                            <p data-lang-en="Computer-aided diagnosis and digital signal processing" data-lang-ar="التشخيص بمساعدة الكمبيوتر ومعالجة الإشارات الرقمية">Computer-aided diagnosis and digital signal processing</p>
                                            <div class="timeline-icon">💻</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item" data-year="2000s" data-delay="0.5s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="Tissue Engineering Era" data-lang-ar="عصر هندسة الأنسجة">Tissue Engineering Era</h3>
                                            <p data-lang-en="Regenerative medicine and tissue engineering breakthroughs" data-lang-ar="اختراقات الطب التجديدي وهندسة الأنسجة">Regenerative medicine and tissue engineering breakthroughs</p>
                                            <div class="timeline-icon">🧬</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item" data-year="2020s" data-delay="0.6s">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h3 data-lang-en="AI & Precision Medicine" data-lang-ar="الذكاء الاصطناعي والطب الدقيق">AI & Precision Medicine</h3>
                                            <p data-lang-en="Artificial intelligence and personalized healthcare solutions" data-lang-ar="الذكاء الاصطناعي وحلول الرعاية الصحية الشخصية">Artificial intelligence and personalized healthcare solutions</p>
                                            <div class="timeline-icon">🤖</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 5: Career Opportunities & Pathways -->
                    <div class="slide" data-slide="5" data-section="introduction">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Career Opportunities & Professional Pathways" data-lang-ar="الفرص المهنية والمسارات المهنية">Career Opportunities & Professional Pathways</h2>
                                <div class="slide-icon bounce-animation">🎯</div>
                            </div>
                            <div class="career-pathways">
                                <div class="career-overview">
                                    <div class="career-stats">
                                        <div class="stat-card animated-card" data-delay="0.1s">
                                            <div class="stat-icon">📈</div>
                                            <div class="stat-number">23%</div>
                                            <div class="stat-label" data-lang-en="Job Growth Rate" data-lang-ar="معدل نمو الوظائف">Job Growth Rate</div>
                                        </div>
                                        <div class="stat-card animated-card" data-delay="0.2s">
                                            <div class="stat-icon">💰</div>
                                            <div class="stat-number">$95K</div>
                                            <div class="stat-label" data-lang-en="Average Salary" data-lang-ar="متوسط الراتب">Average Salary</div>
                                        </div>
                                        <div class="stat-card animated-card" data-delay="0.3s">
                                            <div class="stat-icon">🏢</div>
                                            <div class="stat-number">15+</div>
                                            <div class="stat-label" data-lang-en="Industry Sectors" data-lang-ar="القطاعات الصناعية">Industry Sectors</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="career-sectors">
                                    <h3 data-lang-en="Major Career Sectors" data-lang-ar="القطاعات المهنية الرئيسية">Major Career Sectors</h3>
                                    <div class="sectors-grid">
                                        <div class="sector-card hover-lift" data-delay="0.1s">
                                            <div class="sector-icon">🏥</div>
                                            <h4 data-lang-en="Clinical Engineering" data-lang-ar="الهندسة السريرية">Clinical Engineering</h4>
                                            <ul>
                                                <li data-lang-en="Hospital equipment management" data-lang-ar="إدارة معدات المستشفى">Hospital equipment management</li>
                                                <li data-lang-en="Medical device maintenance" data-lang-ar="صيانة الأجهزة الطبية">Medical device maintenance</li>
                                                <li data-lang-en="Safety compliance" data-lang-ar="الامتثال للسلامة">Safety compliance</li>
                                            </ul>
                                        </div>
                                        <div class="sector-card hover-lift" data-delay="0.2s">
                                            <div class="sector-icon">🔬</div>
                                            <h4 data-lang-en="Research & Development" data-lang-ar="البحث والتطوير">Research & Development</h4>
                                            <ul>
                                                <li data-lang-en="Medical device innovation" data-lang-ar="ابتكار الأجهزة الطبية">Medical device innovation</li>
                                                <li data-lang-en="Pharmaceutical research" data-lang-ar="البحث الصيدلاني">Pharmaceutical research</li>
                                                <li data-lang-en="Academic research" data-lang-ar="البحث الأكاديمي">Academic research</li>
                                            </ul>
                                        </div>
                                        <div class="sector-card hover-lift" data-delay="0.3s">
                                            <div class="sector-icon">🏭</div>
                                            <h4 data-lang-en="Medical Device Industry" data-lang-ar="صناعة الأجهزة الطبية">Medical Device Industry</h4>
                                            <ul>
                                                <li data-lang-en="Product design & development" data-lang-ar="تصميم وتطوير المنتجات">Product design & development</li>
                                                <li data-lang-en="Quality assurance" data-lang-ar="ضمان الجودة">Quality assurance</li>
                                                <li data-lang-en="Regulatory affairs" data-lang-ar="الشؤون التنظيمية">Regulatory affairs</li>
                                            </ul>
                                        </div>
                                        <div class="sector-card hover-lift" data-delay="0.4s">
                                            <div class="sector-icon">💻</div>
                                            <h4 data-lang-en="Healthcare IT" data-lang-ar="تكنولوجيا المعلومات الصحية">Healthcare IT</h4>
                                            <ul>
                                                <li data-lang-en="Health informatics" data-lang-ar="المعلوماتية الصحية">Health informatics</li>
                                                <li data-lang-en="Telemedicine systems" data-lang-ar="أنظمة الطب عن بعد">Telemedicine systems</li>
                                                <li data-lang-en="AI in healthcare" data-lang-ar="الذكاء الاصطناعي في الرعاية الصحية">AI in healthcare</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 6: Human Anatomy for Engineers -->
                    <div class="slide" data-slide="6" data-section="fundamentals">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Human Anatomy & Physiology for Engineers" data-lang-ar="التشريح البشري وعلم وظائف الأعضاء للمهندسين">Human Anatomy & Physiology for Engineers</h2>
                                <div class="slide-icon heartbeat-animation">🫀</div>
                            </div>
                            <div class="anatomy-overview">
                                <div class="body-systems">
                                    <div class="human-body-diagram">
                                        <div class="body-outline">
                                            <div class="system-overlay cardiovascular" data-system="cardiovascular">
                                                <div class="heart-icon pulse-animation">❤️</div>
                                                <div class="blood-vessels">
                                                    <div class="vessel artery"></div>
                                                    <div class="vessel vein"></div>
                                                </div>
                                            </div>
                                            <div class="system-overlay respiratory" data-system="respiratory">
                                                <div class="lungs-icon">🫁</div>
                                                <div class="airways">
                                                    <div class="airway trachea"></div>
                                                    <div class="airway bronchi"></div>
                                                </div>
                                            </div>
                                            <div class="system-overlay nervous" data-system="nervous">
                                                <div class="brain-icon">🧠</div>
                                                <div class="neural-pathways">
                                                    <div class="pathway spinal"></div>
                                                    <div class="pathway peripheral"></div>
                                                </div>
                                            </div>
                                            <div class="system-overlay musculoskeletal" data-system="musculoskeletal">
                                                <div class="skeleton-icon">🦴</div>
                                                <div class="muscle-groups">
                                                    <div class="muscle-group arms"></div>
                                                    <div class="muscle-group legs"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="system-controls">
                                        <h3 data-lang-en="Explore Body Systems" data-lang-ar="استكشف أجهزة الجسم">Explore Body Systems</h3>
                                        <div class="system-buttons">
                                            <button class="system-btn active" data-system="cardiovascular" onclick="selectBodySystem('cardiovascular')">
                                                <i class="fas fa-heart"></i>
                                                <span data-lang-en="Cardiovascular" data-lang-ar="القلب والأوعية الدموية">Cardiovascular</span>
                                            </button>
                                            <button class="system-btn" data-system="respiratory" onclick="selectBodySystem('respiratory')">
                                                <i class="fas fa-lungs"></i>
                                                <span data-lang-en="Respiratory" data-lang-ar="التنفسي">Respiratory</span>
                                            </button>
                                            <button class="system-btn" data-system="nervous" onclick="selectBodySystem('nervous')">
                                                <i class="fas fa-brain"></i>
                                                <span data-lang-en="Nervous" data-lang-ar="العصبي">Nervous</span>
                                            </button>
                                            <button class="system-btn" data-system="musculoskeletal" onclick="selectBodySystem('musculoskeletal')">
                                                <i class="fas fa-dumbbell"></i>
                                                <span data-lang-en="Musculoskeletal" data-lang-ar="العضلي الهيكلي">Musculoskeletal</span>
                                            </button>
                                        </div>
                                        <div class="system-info" id="system-info">
                                            <h4 id="system-title" data-lang-en="Cardiovascular System" data-lang-ar="جهاز القلب والأوعية الدموية">Cardiovascular System</h4>
                                            <p id="system-description" data-lang-en="Pumps blood throughout the body, delivering oxygen and nutrients to tissues" data-lang-ar="يضخ الدم في جميع أنحاء الجسم، وينقل الأكسجين والمواد المغذية إلى الأنسجة">Pumps blood throughout the body, delivering oxygen and nutrients to tissues</p>
                                            <div class="system-specs">
                                                <div class="spec-item">
                                                    <span class="spec-label" data-lang-en="Heart Rate:" data-lang-ar="معدل ضربات القلب:">Heart Rate:</span>
                                                    <span class="spec-value" id="heart-rate">60-100 bpm</span>
                                                </div>
                                                <div class="spec-item">
                                                    <span class="spec-label" data-lang-en="Blood Pressure:" data-lang-ar="ضغط الدم:">Blood Pressure:</span>
                                                    <span class="spec-value" id="blood-pressure">120/80 mmHg</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script src="js/slide-deck.js"></script>
    <script src="js/30slide-modules.js"></script>
</body>
</html>
