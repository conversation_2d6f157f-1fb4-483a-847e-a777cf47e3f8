import React, { useContext, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

interface Section {
  id: string;
  title: { en: string; ar: string };
  type: 'presentation' | 'interactive' | 'video' | 'lab' | 'quiz';
  duration: { en: string; ar: string };
  completed: boolean;
  content: {
    en: string;
    ar: string;
  };
}

interface ModuleData {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  objectives: { en: string[]; ar: string[] };
  sections: Section[];
}

const ModuleView: React.FC = () => {
  const { moduleId } = useParams<{ moduleId: string }>();
  const { language } = useContext(LanguageContext);
  const [activeSection, setActiveSection] = useState<string>('');

  const translations = {
    en: {
      backToCourses: 'Back to Courses',
      moduleObjectives: 'Module Objectives',
      sections: 'Sections',
      duration: 'Duration',
      completed: 'Completed',
      startSection: 'Start Section',
      presentation: 'Presentation',
      interactive: 'Interactive',
      video: 'Video',
      lab: 'Virtual Lab',
      quiz: 'Quiz',
      progress: 'Progress',
    },
    ar: {
      backToCourses: 'العودة إلى الدورات',
      moduleObjectives: 'أهداف الوحدة',
      sections: 'الأقسام',
      duration: 'المدة',
      completed: 'مكتمل',
      startSection: 'ابدأ القسم',
      presentation: 'عرض تقديمي',
      interactive: 'تفاعلي',
      video: 'فيديو',
      lab: 'معمل افتراضي',
      quiz: 'اختبار',
      progress: 'التقدم',
    },
  };

  // ECG Module Data
  const ecgModuleData: ModuleData = {
    id: 'ecg-analysis',
    title: {
      en: 'ECG Signal Acquisition and Analysis',
      ar: 'اكتساب وتحليل إشارات تخطيط القلب'
    },
    description: {
      en: 'This comprehensive module covers the fundamentals of electrocardiography, from physiological basis to signal processing and clinical interpretation.',
      ar: 'تغطي هذه الوحدة الشاملة أساسيات تخطيط القلب الكهربائي، من الأساس الفسيولوجي إلى معالجة الإشارات والتفسير السريري.'
    },
    objectives: {
      en: [
        'Understand the physiological basis of the ECG signal',
        'Identify standard ECG leads and electrode placement',
        'Learn the principles of ECG signal acquisition instrumentation',
        'Recognize common ECG artifacts and noise sources',
        'Perform basic ECG signal processing steps (filtering, QRS detection)',
        'Interpret basic ECG waveforms and intervals'
      ],
      ar: [
        'فهم الأساس الفسيولوجي لإشارة تخطيط القلب',
        'تحديد أقطاب تخطيط القلب القياسية ومواضع الأقطاب الكهربائية',
        'تعلم مبادئ أجهزة اكتساب إشارات تخطيط القلب',
        'التعرف على التشويش الشائع في تخطيط القلب ومصادر الضوضاء',
        'تنفيذ خطوات معالجة إشارات تخطيط القلب الأساسية (الترشيح، كشف QRS)',
        'تفسير أشكال الموجات والفترات الأساسية في تخطيط القلب'
      ]
    },
    sections: [
      {
        id: 'intro-ecg',
        title: {
          en: 'Introduction to Electrocardiography',
          ar: 'مقدمة في تخطيط القلب الكهربائي'
        },
        type: 'presentation',
        duration: { en: '30 min', ar: '30 دقيقة' },
        completed: false,
        content: {
          en: 'What is ECG? Historical context and clinical significance.',
          ar: 'ما هو تخطيط القلب؟ السياق التاريخي والأهمية السريرية.'
        }
      },
      {
        id: 'physiology',
        title: {
          en: 'Physiological Basis of ECG',
          ar: 'الأساس الفسيولوجي لتخطيط القلب'
        },
        type: 'interactive',
        duration: { en: '45 min', ar: '45 دقيقة' },
        completed: false,
        content: {
          en: 'Depolarization and repolarization. Cardiac vectors and electrical activity.',
          ar: 'إزالة الاستقطاب وإعادة الاستقطاب. المتجهات القلبية والنشاط الكهربائي.'
        }
      },
      {
        id: 'leads-placement',
        title: {
          en: 'ECG Leads and Placement',
          ar: 'أقطاب تخطيط القلب ومواضعها'
        },
        type: 'interactive',
        duration: { en: '40 min', ar: '40 دقيقة' },
        completed: false,
        content: {
          en: 'Standard 12-lead system: Limb leads, Augmented leads, Precordial leads.',
          ar: 'نظام الـ 12 قطب القياسي: أقطاب الأطراف، الأقطاب المعززة، الأقطاب الصدرية.'
        }
      },
      {
        id: 'instrumentation',
        title: {
          en: 'ECG Instrumentation',
          ar: 'أجهزة تخطيط القلب'
        },
        type: 'presentation',
        duration: { en: '35 min', ar: '35 دقيقة' },
        completed: false,
        content: {
          en: 'Components of an ECG machine: electrodes, amplifiers, filters, display.',
          ar: 'مكونات جهاز تخطيط القلب: الأقطاب الكهربائية، المضخمات، المرشحات، الشاشة.'
        }
      },
      {
        id: 'artifacts',
        title: {
          en: 'ECG Signal Acquisition Challenges',
          ar: 'تحديات اكتساب إشارات تخطيط القلب'
        },
        type: 'interactive',
        duration: { en: '30 min', ar: '30 دقيقة' },
        completed: false,
        content: {
          en: 'Artifacts: motion, power line, electrode contact. Noise reduction techniques.',
          ar: 'التشويش: الحركة، خط الطاقة، اتصال القطب الكهربائي. تقنيات تقليل الضوضاء.'
        }
      },
      {
        id: 'signal-processing',
        title: {
          en: 'Basic ECG Signal Processing',
          ar: 'معالجة إشارات تخطيط القلب الأساسية'
        },
        type: 'interactive',
        duration: { en: '50 min', ar: '50 دقيقة' },
        completed: false,
        content: {
          en: 'Filtering: High-pass, Low-pass, Notch. Baseline wander removal.',
          ar: 'الترشيح: مرشح عالي التمرير، منخفض التمرير، مرشح الشق. إزالة انحراف الخط الأساسي.'
        }
      },
      {
        id: 'virtual-lab',
        title: {
          en: 'Virtual Lab: Acquiring and Analyzing ECG',
          ar: 'المعمل الافتراضي: اكتساب وتحليل تخطيط القلب'
        },
        type: 'lab',
        duration: { en: '60 min', ar: '60 دقيقة' },
        completed: false,
        content: {
          en: 'Hands-on simulation: Setup virtual patient, configure ECG machine, analyze signals.',
          ar: 'محاكاة عملية: إعداد مريض افتراضي، تكوين جهاز تخطيط القلب، تحليل الإشارات.'
        }
      },
      {
        id: 'interpretation',
        title: {
          en: 'Clinical Interpretation Basics',
          ar: 'أساسيات التفسير السريري'
        },
        type: 'presentation',
        duration: { en: '40 min', ar: '40 دقيقة' },
        completed: false,
        content: {
          en: 'Normal ECG vs. common abnormalities. Case studies and analysis.',
          ar: 'تخطيط القلب الطبيعي مقابل الشذوذات الشائعة. دراسات الحالة والتحليل.'
        }
      },
      {
        id: 'quiz',
        title: {
          en: 'Module Summary & Quiz',
          ar: 'ملخص الوحدة والاختبار'
        },
        type: 'quiz',
        duration: { en: '20 min', ar: '20 دقيقة' },
        completed: false,
        content: {
          en: 'Test your knowledge with interactive questions covering all module content.',
          ar: 'اختبر معرفتك بأسئلة تفاعلية تغطي جميع محتويات الوحدة.'
        }
      }
    ]
  };

  const moduleData = moduleId === 'ecg-analysis' ? ecgModuleData : null;

  if (!moduleData) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-600">
          {language === 'en' ? 'Module not found' : 'الوحدة غير موجودة'}
        </h1>
        <Link to="/courses" className="text-blue-600 hover:underline mt-4 inline-block">
          {translations[language].backToCourses}
        </Link>
      </div>
    );
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'presentation': return '📊';
      case 'interactive': return '🎯';
      case 'video': return '🎥';
      case 'lab': return '🔬';
      case 'quiz': return '📝';
      default: return '📄';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'presentation': return 'bg-blue-100 text-blue-800';
      case 'interactive': return 'bg-green-100 text-green-800';
      case 'video': return 'bg-purple-100 text-purple-800';
      case 'lab': return 'bg-orange-100 text-orange-800';
      case 'quiz': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const completedSections = moduleData.sections.filter(s => s.completed).length;
  const progressPercentage = (completedSections / moduleData.sections.length) * 100;

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Navigation */}
      <div className="mb-6">
        <Link 
          to="/courses" 
          className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
        >
          ← {translations[language].backToCourses}
        </Link>
      </div>

      {/* Module Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-4">{moduleData.title[language]}</h1>
        <p className="text-lg opacity-90 mb-6">{moduleData.description[language]}</p>
        
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">{translations[language].progress}</span>
            <span className="text-sm">{completedSections}/{moduleData.sections.length}</span>
          </div>
          <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
            <div 
              className="bg-white h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Module Objectives */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">{translations[language].moduleObjectives}</h2>
            <ul className="space-y-2">
              {moduleData.objectives[language].map((objective, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2 mt-1">✓</span>
                  <span className="text-sm text-gray-700">{objective}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Sections List */}
        <div className="lg:col-span-2">
          <h2 className="text-2xl font-bold mb-6">{translations[language].sections}</h2>
          <div className="space-y-4">
            {moduleData.sections.map((section, index) => (
              <div key={section.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{getTypeIcon(section.type)}</span>
                      <div>
                        <h3 className="text-lg font-semibold">{section.title[language]}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(section.type)}`}>
                          {translations[language][section.type]}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">{section.duration[language]}</div>
                      {section.completed && (
                        <div className="text-green-600 text-sm font-medium">
                          ✓ {translations[language].completed}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{section.content[language]}</p>
                  
                  <button 
                    onClick={() => setActiveSection(section.id)}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    {translations[language].startSection}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleView;
