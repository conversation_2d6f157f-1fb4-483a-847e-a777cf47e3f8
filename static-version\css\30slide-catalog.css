/* ===== 30-SLIDE CATALOG STYLES ===== */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1e40af, #3b82f6, #10b981);
    color: var(--white);
    padding: 4rem 0;
    margin-bottom: 4rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.hero-stats .stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem 1rem;
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #fbbf24;
}

.hero-stats .stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    opacity: 0.8;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-modules {
    position: relative;
    width: 300px;
    height: 300px;
}

.module-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
}

.module-icon[data-module="fundamentals"] {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.module-icon[data-module="imaging"] {
    top: 25%;
    right: 0;
    animation-delay: 0.5s;
}

.module-icon[data-module="biosignals"] {
    top: 50%;
    right: 15%;
    animation-delay: 1s;
}

.module-icon[data-module="biomechanics"] {
    bottom: 25%;
    right: 0;
    animation-delay: 1.5s;
}

.module-icon[data-module="tissue"] {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 2s;
}

.module-icon[data-module="devices"] {
    bottom: 25%;
    left: 0;
    animation-delay: 2.5s;
}

.module-icon[data-module="bioinformatics"] {
    top: 50%;
    left: 15%;
    animation-delay: 3s;
}

.module-icon[data-module="neural"] {
    top: 25%;
    left: 0;
    animation-delay: 3.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Modules Section */
.modules-section {
    padding: 2rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.module-card {
    background: var(--white);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: #3b82f6;
}

.module-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
}

.module-icon-large {
    font-size: 4rem;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

.module-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.completed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: var(--white);
}

.status-badge.in-development {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--white);
}

.status-badge.coming-soon {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: var(--white);
}

.module-content {
    padding: 2rem;
}

.module-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.module-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.module-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.feature-tag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gray-100);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    transition: all var(--transition-fast);
}

.feature-tag:hover {
    background: #3b82f6;
    color: var(--white);
    transform: scale(1.05);
}

.feature-tag i {
    font-size: 0.875rem;
}

.module-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.module-stats .stat {
    text-align: center;
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
}

.module-stats .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 0.25rem;
}

.module-stats .stat-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-actions {
    padding: 0 2rem 2rem;
    display: flex;
    gap: 1rem;
}

.module-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
}

.btn.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn.btn-primary:hover:not(.disabled) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn.btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.btn.btn-secondary:hover {
    background: var(--gray-50);
    border-color: #3b82f6;
    color: #3b82f6;
}

.btn.disabled {
    background: var(--gray-300);
    color: var(--gray-500);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Module-Specific Styling */
.module-card[data-module="fundamentals"] .module-icon-large {
    color: #3b82f6;
}

.module-card[data-module="imaging"] .module-icon-large {
    color: #10b981;
}

.module-card[data-module="biosignals"] .module-icon-large {
    color: #ef4444;
}

.module-card[data-module="biomechanics"] .module-icon-large {
    color: #8b5cf6;
}

.module-card[data-module="tissue"] .module-icon-large {
    color: #f59e0b;
}

.module-card[data-module="devices"] .module-icon-large {
    color: #06b6d4;
}

.module-card[data-module="bioinformatics"] .module-icon-large {
    color: #ec4899;
}

.module-card[data-module="neural"] .module-icon-large {
    color: #6366f1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }
    
    .floating-modules {
        width: 250px;
        height: 250px;
    }
    
    .module-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-stats .stat-item {
        padding: 1.5rem 1rem;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .module-header {
        padding: 1.5rem;
    }
    
    .module-content {
        padding: 1.5rem;
    }
    
    .module-actions {
        padding: 0 1.5rem 1.5rem;
        flex-direction: column;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .floating-modules {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 1.75rem;
    }
    
    .module-content h3 {
        font-size: 1.25rem;
    }
    
    .module-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .feature-tag {
        font-size: 0.625rem;
        padding: 0.375rem 0.75rem;
    }
    
    .module-header {
        padding: 1rem;
    }
    
    .module-content {
        padding: 1rem;
    }
    
    .module-actions {
        padding: 0 1rem 1rem;
    }
}

/* Loading Animation */
.module-card.loading {
    position: relative;
    overflow: hidden;
}

.module-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}
