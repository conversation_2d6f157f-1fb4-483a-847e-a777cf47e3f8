/* ===== EXTENDED MODULES PAGE STYLES ===== */

/* Module Statistics Section */
.module-stats-section {
    margin-bottom: 3rem;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Filter Section */
.filter-section {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 3rem;
}

.filter-controls {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 1rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.category-filter select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.875rem;
    cursor: pointer;
    min-width: 200px;
}

.category-filter select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

/* Category Sections */
.modules-catalog {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.category-section {
    opacity: 1;
    transition: all var(--transition-normal);
}

.category-section.hidden {
    display: none;
}

.category-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--gradient-secondary);
    color: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.category-icon {
    font-size: 3rem;
    opacity: 0.9;
    animation: float 3s ease-in-out infinite;
}

.category-info {
    flex: 1;
}

.category-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.category-description {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.875rem;
    opacity: 0.8;
}

.module-count {
    font-weight: 600;
}

.difficulty-range {
    font-style: italic;
}

/* Enhanced Module Cards */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
}

.module-card-extended {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
    border: 1px solid var(--gray-100);
}

.module-card-extended:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.module-card-extended::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.module-card-extended:hover::before {
    transform: scaleX(1);
}

.module-header-extended {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-100);
}

.module-title-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.module-icon-extended {
    font-size: 2rem;
    opacity: 0.8;
}

.module-title-extended {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    line-height: 1.3;
}

.module-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.module-duration {
    font-size: 0.75rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.difficulty-badge-extended {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty-badge-extended.beginner {
    background: #dcfce7;
    color: #166534;
}

.difficulty-badge-extended.intermediate {
    background: #fef3c7;
    color: #92400e;
}

.difficulty-badge-extended.advanced {
    background: #fee2e2;
    color: #991b1b;
}

.module-content-extended {
    padding: 1.5rem;
}

.module-description-extended {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.module-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature-tag {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.feature-tag.lab {
    background: #dbeafe;
    color: #1e40af;
}

.feature-tag.quiz {
    background: #fef3c7;
    color: #92400e;
}

.feature-tag.project {
    background: #f3e8ff;
    color: #7c3aed;
}

.module-stats-extended {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.stat-item-extended {
    text-align: center;
}

.stat-value-extended {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
    display: block;
}

.stat-label-extended {
    font-size: 0.75rem;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.module-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-module {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
}

.btn-primary-module {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-primary-module:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
}

.btn-secondary-module {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.btn-secondary-module:hover {
    background: var(--gray-200);
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin-top: 4rem;
    padding: 3rem 0;
}

.modules-loaded-info {
    margin-top: 1rem;
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    border-top-color: var(--primary-blue);
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filter-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .category-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .category-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .module-stats-extended {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .module-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .filter-section {
        padding: 1.5rem;
    }
    
    .category-header {
        padding: 1.5rem;
    }
    
    .category-title {
        font-size: 1.5rem;
    }
    
    .module-card-extended {
        margin: 0 -0.5rem;
    }
}

/* Animation for module cards appearing */
.module-card-extended.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search highlight */
.search-highlight {
    background: #fef3c7;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray-500);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.empty-state-description {
    font-size: 1rem;
    line-height: 1.5;
}
