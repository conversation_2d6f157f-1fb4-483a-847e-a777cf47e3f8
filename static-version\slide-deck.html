<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-en="Interactive Slide Deck - BioEngage LMS" data-lang-ar="عرض الشرائح التفاعلي - بايو إنجيج">Interactive Slide Deck - BioEngage LMS</title>
    <meta name="description" content="Interactive slide presentation for biomedical engineering education with animated tools and virtual labs">
    <meta name="keywords" content="biomedical engineering, slide deck, interactive presentation, virtual lab, medical instrumentation">
    <meta name="author" content="Dr<PERSON> <PERSON>, SUST - BME">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">

    <!-- Fonts for bilingual support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slide-deck.css">
</head>
<body class="slide-deck-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo and Title -->
                <div class="logo-section">
                    <div class="logo-icon">🧬</div>
                    <div class="logo-text">
                        <h1 data-lang-en="BioEngage" data-lang-ar="بايو إنجيج">BioEngage</h1>
                        <p class="subtitle" data-lang-en="Interactive Virtual LMS" data-lang-ar="نظام إدارة التعلم الافتراضي التفاعلي">Interactive Virtual LMS</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="nav-desktop">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                        <li><a href="modules.html" class="nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                        <li><a href="extended-modules.html" class="nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                        <li><a href="interactive_lectures.html" class="nav-link active" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                        <li><a href="training.html" class="nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                        <li><a href="virtual_lab.html" class="nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                    </ul>
                </nav>

                <!-- Language Toggle and Mobile Menu -->
                <div class="header-controls">
                    <button id="lang-toggle" class="lang-toggle" data-lang="en">
                        <span class="flag">🇸🇦</span>
                        <span class="lang-text" data-lang-en="العربية" data-lang-ar="English">العربية</span>
                    </button>
                    <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <nav id="mobile-nav" class="nav-mobile">
                <ul class="mobile-nav-list">
                    <li><a href="index.html" class="mobile-nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                    <li><a href="modules.html" class="mobile-nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                    <li><a href="extended-modules.html" class="mobile-nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                    <li><a href="interactive_lectures.html" class="mobile-nav-link" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                    <li><a href="training.html" class="mobile-nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                    <li><a href="virtual_lab.html" class="mobile-nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                </ul>
            </nav>
        </div>
        <div class="header-progress"></div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Slide Deck Container -->
            <div class="slide-deck-container">
                <!-- Slide Deck Header -->
                <div class="slide-deck-header">
                    <div class="deck-info">
                        <h1 class="deck-title" data-lang-en="Biomedical Engineering & Instrumentation" data-lang-ar="الهندسة الطبية الحيوية والأجهزة">Biomedical Engineering & Instrumentation</h1>
                        <p class="deck-subtitle" data-lang-en="Interactive Educational Slide Deck with Virtual Lab Components" data-lang-ar="عرض شرائح تعليمي تفاعلي مع مكونات المعمل الافتراضي">Interactive Educational Slide Deck with Virtual Lab Components</p>
                    </div>

                    <div class="deck-controls">
                        <div class="slide-counter">
                            <span id="current-slide">1</span> / <span id="total-slides">11</span>
                        </div>
                        <div class="control-buttons">
                            <button id="prev-slide" class="control-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="play-pause" class="control-btn">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="next-slide" class="control-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <button id="fullscreen-btn" class="control-btn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Slide Container -->
                <div class="slide-container" id="slide-container">
                    <!-- Slide 1: Title Slide -->
                    <div class="slide active" data-slide="1">
                        <div class="slide-content title-slide">
                            <div class="title-animation">
                                <div class="animated-icon pulse-animation">🧬</div>
                                <h1 data-lang-en="Biomedical Engineering & Instrumentation" data-lang-ar="الهندسة الطبية الحيوية والأجهزة">Biomedical Engineering & Instrumentation</h1>
                                <h2 data-lang-en="Interactive Learning Experience" data-lang-ar="تجربة التعلم التفاعلية">Interactive Learning Experience</h2>
                            </div>
                            <div class="title-features">
                                <div class="feature-item fade-in-up" data-delay="0.2s">
                                    <div class="feature-icon rotate-animation">🎯</div>
                                    <span data-lang-en="Interactive Tools" data-lang-ar="أدوات تفاعلية">Interactive Tools</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.4s">
                                    <div class="feature-icon bounce-animation">🔬</div>
                                    <span data-lang-en="Virtual Labs" data-lang-ar="معامل افتراضية">Virtual Labs</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.6s">
                                    <div class="feature-icon wave-animation">📊</div>
                                    <span data-lang-en="Real-time Simulations" data-lang-ar="محاكاة في الوقت الفعلي">Real-time Simulations</span>
                                </div>
                            </div>
                            <div class="author-info fade-in" data-delay="0.8s">
                                <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                                <p data-lang-en="SUST - Biomedical Engineering Department" data-lang-ar="جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية">SUST - Biomedical Engineering Department</p>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2: Learning Objectives -->
                    <div class="slide" data-slide="2">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Learning Objectives" data-lang-ar="أهداف التعلم">Learning Objectives</h2>
                                <div class="slide-icon pulse-animation">🎯</div>
                            </div>
                            <div class="objectives-grid">
                                <div class="objective-card animated-card hover-lift" data-delay="0.1s">
                                    <div class="objective-icon heartbeat-animation">🫀</div>
                                    <h3 data-lang-en="Understand Physiological Systems" data-lang-ar="فهم الأنظمة الفسيولوجية">Understand Physiological Systems</h3>
                                    <p data-lang-en="Learn how biological systems work and their electrical properties" data-lang-ar="تعلم كيف تعمل الأنظمة البيولوجية وخصائصها الكهربائية">Learn how biological systems work and their electrical properties</p>
                                </div>
                                <div class="objective-card animated-card hover-lift" data-delay="0.2s">
                                    <div class="objective-icon rotate-animation">🔧</div>
                                    <h3 data-lang-en="Master Medical Instrumentation" data-lang-ar="إتقان الأجهزة الطبية">Master Medical Instrumentation</h3>
                                    <p data-lang-en="Explore the design and operation of medical devices and sensors" data-lang-ar="استكشف تصميم وتشغيل الأجهزة الطبية وأجهزة الاستشعار">Explore the design and operation of medical devices and sensors</p>
                                </div>
                                <div class="objective-card animated-card hover-lift" data-delay="0.3s">
                                    <div class="objective-icon wave-animation">📊</div>
                                    <h3 data-lang-en="Analyze Biosignals" data-lang-ar="تحليل الإشارات الحيوية">Analyze Biosignals</h3>
                                    <p data-lang-en="Process and interpret biological signals using digital techniques" data-lang-ar="معالجة وتفسير الإشارات البيولوجية باستخدام التقنيات الرقمية">Process and interpret biological signals using digital techniques</p>
                                </div>
                                <div class="objective-card animated-card hover-lift" data-delay="0.4s">
                                    <div class="objective-icon bounce-animation">🔬</div>
                                    <h3 data-lang-en="Apply Virtual Lab Skills" data-lang-ar="تطبيق مهارات المعمل الافتراضي">Apply Virtual Lab Skills</h3>
                                    <p data-lang-en="Gain hands-on experience through interactive simulations" data-lang-ar="اكتسب خبرة عملية من خلال المحاكاة التفاعلية">Gain hands-on experience through interactive simulations</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3: Introduction to Biomedical Engineering -->
                    <div class="slide" data-slide="3">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="What is Biomedical Engineering?" data-lang-ar="ما هي الهندسة الطبية الحيوية؟">What is Biomedical Engineering?</h2>
                                <div class="slide-icon rotate-animation">🧬</div>
                            </div>
                            <div class="content-layout two-column">
                                <div class="content-left">
                                    <div class="definition-box fade-in-left">
                                        <h3 data-lang-en="Definition" data-lang-ar="التعريف">Definition</h3>
                                        <p data-lang-en="Biomedical Engineering applies engineering principles and design concepts to medicine and biology for healthcare purposes." data-lang-ar="تطبق الهندسة الطبية الحيوية المبادئ الهندسية ومفاهيم التصميم على الطب والبيولوجيا لأغراض الرعاية الصحية.">Biomedical Engineering applies engineering principles and design concepts to medicine and biology for healthcare purposes.</p>
                                    </div>
                                    <div class="key-areas fade-in-left" data-delay="0.3s">
                                        <h3 data-lang-en="Key Areas" data-lang-ar="المجالات الرئيسية">Key Areas</h3>
                                        <ul class="animated-list">
                                            <li class="list-item" data-delay="0.1s">
                                                <span class="list-icon">🔬</span>
                                                <span data-lang-en="Medical Device Design" data-lang-ar="تصميم الأجهزة الطبية">Medical Device Design</span>
                                            </li>
                                            <li class="list-item" data-delay="0.2s">
                                                <span class="list-icon">🧬</span>
                                                <span data-lang-en="Tissue Engineering" data-lang-ar="هندسة الأنسجة">Tissue Engineering</span>
                                            </li>
                                            <li class="list-item" data-delay="0.3s">
                                                <span class="list-icon">📊</span>
                                                <span data-lang-en="Biosignal Processing" data-lang-ar="معالجة الإشارات الحيوية">Biosignal Processing</span>
                                            </li>
                                            <li class="list-item" data-delay="0.4s">
                                                <span class="list-icon">🏥</span>
                                                <span data-lang-en="Medical Imaging" data-lang-ar="التصوير الطبي">Medical Imaging</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="content-right">
                                    <div class="interactive-diagram fade-in-right">
                                        <div class="diagram-container">
                                            <div class="bme-circle">
                                                <div class="center-icon pulse-animation">🧬</div>
                                                <div class="orbit-item" data-angle="0">
                                                    <div class="orbit-icon bounce-animation">⚕️</div>
                                                    <span data-lang-en="Medicine" data-lang-ar="الطب">Medicine</span>
                                                </div>
                                                <div class="orbit-item" data-angle="90">
                                                    <div class="orbit-icon rotate-animation">🔧</div>
                                                    <span data-lang-en="Engineering" data-lang-ar="الهندسة">Engineering</span>
                                                </div>
                                                <div class="orbit-item" data-angle="180">
                                                    <div class="orbit-icon wave-animation">🧪</div>
                                                    <span data-lang-en="Biology" data-lang-ar="البيولوجيا">Biology</span>
                                                </div>
                                                <div class="orbit-item" data-angle="270">
                                                    <div class="orbit-icon heartbeat-animation">💻</div>
                                                    <span data-lang-en="Technology" data-lang-ar="التكنولوجيا">Technology</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 4: Medical Instrumentation Overview -->
                    <div class="slide" data-slide="4">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Medical Instrumentation Systems" data-lang-ar="أنظمة الأجهزة الطبية">Medical Instrumentation Systems</h2>
                                <div class="slide-icon pulse-animation">🔧</div>
                            </div>
                            <div class="instrumentation-overview">
                                <div class="system-flow">
                                    <div class="flow-step animated-card" data-delay="0.1s">
                                        <div class="step-icon heartbeat-animation">🫀</div>
                                        <h3 data-lang-en="Biological Signal" data-lang-ar="الإشارة البيولوجية">Biological Signal</h3>
                                        <p data-lang-en="Physiological processes generate electrical, mechanical, or chemical signals" data-lang-ar="العمليات الفسيولوجية تولد إشارات كهربائية أو ميكانيكية أو كيميائية">Physiological processes generate electrical, mechanical, or chemical signals</p>
                                    </div>
                                    <div class="flow-arrow">
                                        <i class="fas fa-arrow-right pulse-animation"></i>
                                    </div>
                                    <div class="flow-step animated-card" data-delay="0.2s">
                                        <div class="step-icon rotate-animation">📡</div>
                                        <h3 data-lang-en="Sensor/Transducer" data-lang-ar="المستشعر/المحول">Sensor/Transducer</h3>
                                        <p data-lang-en="Converts biological signals into electrical signals for processing" data-lang-ar="يحول الإشارات البيولوجية إلى إشارات كهربائية للمعالجة">Converts biological signals into electrical signals for processing</p>
                                    </div>
                                    <div class="flow-arrow">
                                        <i class="fas fa-arrow-right pulse-animation"></i>
                                    </div>
                                    <div class="flow-step animated-card" data-delay="0.3s">
                                        <div class="step-icon wave-animation">⚡</div>
                                        <h3 data-lang-en="Signal Processing" data-lang-ar="معالجة الإشارة">Signal Processing</h3>
                                        <p data-lang-en="Amplification, filtering, and digital conversion of signals" data-lang-ar="تضخيم وترشيح وتحويل رقمي للإشارات">Amplification, filtering, and digital conversion of signals</p>
                                    </div>
                                    <div class="flow-arrow">
                                        <i class="fas fa-arrow-right pulse-animation"></i>
                                    </div>
                                    <div class="flow-step animated-card" data-delay="0.4s">
                                        <div class="step-icon bounce-animation">📊</div>
                                        <h3 data-lang-en="Display/Analysis" data-lang-ar="العرض/التحليل">Display/Analysis</h3>
                                        <p data-lang-en="Visualization and interpretation of processed signals for diagnosis" data-lang-ar="تصور وتفسير الإشارات المعالجة للتشخيص">Visualization and interpretation of processed signals for diagnosis</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 5: Interactive ECG System Demo -->
                    <div class="slide" data-slide="5">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Interactive ECG System" data-lang-ar="نظام تخطيط القلب التفاعلي">Interactive ECG System</h2>
                                <div class="slide-icon heartbeat-animation">💓</div>
                            </div>
                            <div class="interactive-demo">
                                <div class="demo-container">
                                    <div class="virtual-patient">
                                        <div class="patient-body">
                                            <div class="heart-animation">
                                                <div class="heart-icon heartbeat-animation">❤️</div>
                                                <div class="electrical-waves">
                                                    <div class="wave wave-1"></div>
                                                    <div class="wave wave-2"></div>
                                                    <div class="wave wave-3"></div>
                                                </div>
                                            </div>
                                            <div class="electrode-points">
                                                <div class="electrode" data-position="RA" style="top: 20%; right: 15%;">
                                                    <div class="electrode-dot pulse-animation"></div>
                                                    <span class="electrode-label">RA</span>
                                                </div>
                                                <div class="electrode" data-position="LA" style="top: 20%; left: 15%;">
                                                    <div class="electrode-dot pulse-animation"></div>
                                                    <span class="electrode-label">LA</span>
                                                </div>
                                                <div class="electrode" data-position="LL" style="bottom: 20%; left: 15%;">
                                                    <div class="electrode-dot pulse-animation"></div>
                                                    <span class="electrode-label">LL</span>
                                                </div>
                                                <div class="electrode" data-position="V1" style="top: 35%; right: 25%;">
                                                    <div class="electrode-dot pulse-animation"></div>
                                                    <span class="electrode-label">V1</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ecg-display">
                                        <div class="ecg-screen">
                                            <div class="ecg-grid"></div>
                                            <div class="ecg-waveform">
                                                <svg class="ecg-svg" viewBox="0 0 400 100">
                                                    <path class="ecg-path" d="M0,50 L50,50 L55,30 L60,70 L65,20 L70,50 L120,50 L125,30 L130,70 L135,20 L140,50 L190,50 L195,30 L200,70 L205,20 L210,50 L260,50 L265,30 L270,70 L275,20 L280,50 L330,50 L335,30 L340,70 L345,20 L350,50 L400,50"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ecg-controls">
                                            <button class="demo-btn" onclick="startECGDemo()">
                                                <i class="fas fa-play"></i>
                                                <span data-lang-en="Start Demo" data-lang-ar="ابدأ العرض">Start Demo</span>
                                            </button>
                                            <button class="demo-btn" onclick="pauseECGDemo()">
                                                <i class="fas fa-pause"></i>
                                                <span data-lang-en="Pause" data-lang-ar="إيقاف مؤقت">Pause</span>
                                            </button>
                                            <button class="demo-btn" onclick="resetECGDemo()">
                                                <i class="fas fa-redo"></i>
                                                <span data-lang-en="Reset" data-lang-ar="إعادة تعيين">Reset</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 6: Virtual Lab Introduction -->
                    <div class="slide" data-slide="6">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Virtual Laboratory Experience" data-lang-ar="تجربة المعمل الافتراضي">Virtual Laboratory Experience</h2>
                                <div class="slide-icon bounce-animation">🔬</div>
                            </div>
                            <div class="lab-preview">
                                <div class="lab-features">
                                    <div class="feature-card animated-card hover-lift" data-delay="0.1s">
                                        <div class="feature-icon pulse-animation">🎯</div>
                                        <h3 data-lang-en="Hands-on Learning" data-lang-ar="التعلم العملي">Hands-on Learning</h3>
                                        <p data-lang-en="Practice with virtual medical equipment in a safe environment" data-lang-ar="تدرب مع المعدات الطبية الافتراضية في بيئة آمنة">Practice with virtual medical equipment in a safe environment</p>
                                    </div>
                                    <div class="feature-card animated-card hover-lift" data-delay="0.2s">
                                        <div class="feature-icon rotate-animation">⚡</div>
                                        <h3 data-lang-en="Real-time Feedback" data-lang-ar="ردود فعل فورية">Real-time Feedback</h3>
                                        <p data-lang-en="Immediate responses to your actions and decisions" data-lang-ar="استجابات فورية لأفعالك وقراراتك">Immediate responses to your actions and decisions</p>
                                    </div>
                                    <div class="feature-card animated-card hover-lift" data-delay="0.3s">
                                        <div class="feature-icon wave-animation">📊</div>
                                        <h3 data-lang-en="Data Analysis" data-lang-ar="تحليل البيانات">Data Analysis</h3>
                                        <p data-lang-en="Analyze and interpret biological signals and measurements" data-lang-ar="تحليل وتفسير الإشارات البيولوجية والقياسات">Analyze and interpret biological signals and measurements</p>
                                    </div>
                                    <div class="feature-card animated-card hover-lift" data-delay="0.4s">
                                        <div class="feature-icon heartbeat-animation">🎮</div>
                                        <h3 data-lang-en="Interactive Simulations" data-lang-ar="محاكاة تفاعلية">Interactive Simulations</h3>
                                        <p data-lang-en="Engage with realistic patient scenarios and equipment" data-lang-ar="تفاعل مع سيناريوهات المرضى الواقعية والمعدات">Engage with realistic patient scenarios and equipment</p>
                                    </div>
                                </div>
                                <div class="lab-access">
                                    <button class="lab-btn" onclick="launchVirtualLab()">
                                        <i class="fas fa-rocket"></i>
                                        <span data-lang-en="Launch Virtual Lab" data-lang-ar="تشغيل المعمل الافتراضي">Launch Virtual Lab</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 7: Biosignal Types -->
                    <div class="slide" data-slide="7">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Types of Biosignals" data-lang-ar="أنواع الإشارات الحيوية">Types of Biosignals</h2>
                                <div class="slide-icon wave-animation">📊</div>
                            </div>
                            <div class="biosignals-grid">
                                <div class="signal-card animated-card hover-lift" data-delay="0.1s">
                                    <div class="signal-icon heartbeat-animation">💓</div>
                                    <h3 data-lang-en="ECG - Electrocardiogram" data-lang-ar="تخطيط القلب الكهربائي">ECG - Electrocardiogram</h3>
                                    <div class="signal-details">
                                        <p data-lang-en="Electrical activity of the heart" data-lang-ar="النشاط الكهربائي للقلب">Electrical activity of the heart</p>
                                        <div class="signal-specs">
                                            <span class="spec-item">Frequency: 0.05-100 Hz</span>
                                            <span class="spec-item">Amplitude: 0.1-5 mV</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="signal-card animated-card hover-lift" data-delay="0.2s">
                                    <div class="signal-icon wave-animation">🧠</div>
                                    <h3 data-lang-en="EEG - Electroencephalogram" data-lang-ar="تخطيط الدماغ الكهربائي">EEG - Electroencephalogram</h3>
                                    <div class="signal-details">
                                        <p data-lang-en="Brain electrical activity" data-lang-ar="النشاط الكهربائي للدماغ">Brain electrical activity</p>
                                        <div class="signal-specs">
                                            <span class="spec-item">Frequency: 0.5-100 Hz</span>
                                            <span class="spec-item">Amplitude: 10-100 μV</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="signal-card animated-card hover-lift" data-delay="0.3s">
                                    <div class="signal-icon bounce-animation">💪</div>
                                    <h3 data-lang-en="EMG - Electromyogram" data-lang-ar="تخطيط العضلات الكهربائي">EMG - Electromyogram</h3>
                                    <div class="signal-details">
                                        <p data-lang-en="Muscle electrical activity" data-lang-ar="النشاط الكهربائي للعضلات">Muscle electrical activity</p>
                                        <div class="signal-specs">
                                            <span class="spec-item">Frequency: 10-500 Hz</span>
                                            <span class="spec-item">Amplitude: 0.1-5 mV</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="signal-card animated-card hover-lift" data-delay="0.4s">
                                    <div class="signal-icon pulse-animation">🫁</div>
                                    <h3 data-lang-en="Respiratory Signals" data-lang-ar="إشارات التنفس">Respiratory Signals</h3>
                                    <div class="signal-details">
                                        <p data-lang-en="Breathing patterns and lung function" data-lang-ar="أنماط التنفس ووظائف الرئة">Breathing patterns and lung function</p>
                                        <div class="signal-specs">
                                            <span class="spec-item">Frequency: 0.1-2 Hz</span>
                                            <span class="spec-item">Rate: 12-20 breaths/min</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 8: Signal Processing Pipeline -->
                    <div class="slide" data-slide="8">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Signal Processing Pipeline" data-lang-ar="خط معالجة الإشارات">Signal Processing Pipeline</h2>
                                <div class="slide-icon rotate-animation">⚙️</div>
                            </div>
                            <div class="processing-pipeline">
                                <div class="pipeline-step animated-card" data-delay="0.1s">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h3 data-lang-en="Signal Acquisition" data-lang-ar="اكتساب الإشارة">Signal Acquisition</h3>
                                        <p data-lang-en="Capture biological signals using sensors and electrodes" data-lang-ar="التقاط الإشارات البيولوجية باستخدام أجهزة الاستشعار والأقطاب الكهربائية">Capture biological signals using sensors and electrodes</p>
                                        <div class="step-tools">
                                            <span class="tool-tag">Electrodes</span>
                                            <span class="tool-tag">Amplifiers</span>
                                            <span class="tool-tag">ADC</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="pipeline-arrow">
                                    <i class="fas fa-arrow-down pulse-animation"></i>
                                </div>
                                <div class="pipeline-step animated-card" data-delay="0.2s">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h3 data-lang-en="Preprocessing" data-lang-ar="المعالجة المسبقة">Preprocessing</h3>
                                        <p data-lang-en="Remove noise and artifacts from raw signals" data-lang-ar="إزالة الضوضاء والتشويش من الإشارات الخام">Remove noise and artifacts from raw signals</p>
                                        <div class="step-tools">
                                            <span class="tool-tag">Filtering</span>
                                            <span class="tool-tag">Baseline Correction</span>
                                            <span class="tool-tag">Artifact Removal</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="pipeline-arrow">
                                    <i class="fas fa-arrow-down pulse-animation"></i>
                                </div>
                                <div class="pipeline-step animated-card" data-delay="0.3s">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h3 data-lang-en="Feature Extraction" data-lang-ar="استخراج الخصائص">Feature Extraction</h3>
                                        <p data-lang-en="Identify and extract meaningful features from signals" data-lang-ar="تحديد واستخراج الخصائص المفيدة من الإشارات">Identify and extract meaningful features from signals</p>
                                        <div class="step-tools">
                                            <span class="tool-tag">Peak Detection</span>
                                            <span class="tool-tag">Frequency Analysis</span>
                                            <span class="tool-tag">Time Domain</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="pipeline-arrow">
                                    <i class="fas fa-arrow-down pulse-animation"></i>
                                </div>
                                <div class="pipeline-step animated-card" data-delay="0.4s">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <h3 data-lang-en="Analysis & Interpretation" data-lang-ar="التحليل والتفسير">Analysis & Interpretation</h3>
                                        <p data-lang-en="Analyze features and provide clinical interpretation" data-lang-ar="تحليل الخصائص وتقديم التفسير السريري">Analyze features and provide clinical interpretation</p>
                                        <div class="step-tools">
                                            <span class="tool-tag">Classification</span>
                                            <span class="tool-tag">Pattern Recognition</span>
                                            <span class="tool-tag">Diagnosis</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 9: Interactive Filter Demo -->
                    <div class="slide" data-slide="9">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Interactive Filter Demonstration" data-lang-ar="عرض المرشحات التفاعلي">Interactive Filter Demonstration</h2>
                                <div class="slide-icon wave-animation">🎛️</div>
                            </div>
                            <div class="filter-demo">
                                <div class="demo-controls">
                                    <div class="control-group">
                                        <label data-lang-en="Filter Type:" data-lang-ar="نوع المرشح:">Filter Type:</label>
                                        <select id="filter-type" class="demo-select">
                                            <option value="lowpass" data-lang-en="Low Pass" data-lang-ar="تمرير منخفض">Low Pass</option>
                                            <option value="highpass" data-lang-en="High Pass" data-lang-ar="تمرير عالي">High Pass</option>
                                            <option value="bandpass" data-lang-en="Band Pass" data-lang-ar="تمرير نطاق">Band Pass</option>
                                            <option value="notch" data-lang-en="Notch (50/60 Hz)" data-lang-ar="مرشح التداخل">Notch (50/60 Hz)</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label data-lang-en="Cutoff Frequency:" data-lang-ar="تردد القطع:">Cutoff Frequency:</label>
                                        <input type="range" id="cutoff-freq" min="1" max="100" value="40" class="demo-slider">
                                        <span id="freq-value">40 Hz</span>
                                    </div>
                                    <div class="control-group">
                                        <label data-lang-en="Noise Level:" data-lang-ar="مستوى الضوضاء:">Noise Level:</label>
                                        <input type="range" id="noise-level" min="0" max="100" value="30" class="demo-slider">
                                        <span id="noise-value">30%</span>
                                    </div>
                                </div>
                                <div class="signal-display">
                                    <div class="signal-panel">
                                        <h4 data-lang-en="Original Signal" data-lang-ar="الإشارة الأصلية">Original Signal</h4>
                                        <div class="signal-canvas" id="original-signal">
                                            <svg viewBox="0 0 400 100" class="signal-svg">
                                                <path class="signal-path original" d="M0,50 Q100,30 200,50 T400,50"></path>
                                                <path class="noise-path" d="M0,45 L10,55 L20,45 L30,55 L40,45 L50,55 L60,45 L70,55 L80,45 L90,55 L100,45"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="signal-panel">
                                        <h4 data-lang-en="Filtered Signal" data-lang-ar="الإشارة المرشحة">Filtered Signal</h4>
                                        <div class="signal-canvas" id="filtered-signal">
                                            <svg viewBox="0 0 400 100" class="signal-svg">
                                                <path class="signal-path filtered" d="M0,50 Q100,30 200,50 T400,50"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="demo-actions">
                                    <button class="demo-btn" onclick="applyFilter()">
                                        <i class="fas fa-play"></i>
                                        <span data-lang-en="Apply Filter" data-lang-ar="تطبيق المرشح">Apply Filter</span>
                                    </button>
                                    <button class="demo-btn" onclick="resetFilter()">
                                        <i class="fas fa-redo"></i>
                                        <span data-lang-en="Reset" data-lang-ar="إعادة تعيين">Reset</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 10: Medical Device Categories -->
                    <div class="slide" data-slide="10">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Medical Device Categories" data-lang-ar="فئات الأجهزة الطبية">Medical Device Categories</h2>
                                <div class="slide-icon rotate-animation">🏥</div>
                            </div>
                            <div class="device-categories">
                                <div class="category-section">
                                    <h3 data-lang-en="Diagnostic Devices" data-lang-ar="أجهزة التشخيص">Diagnostic Devices</h3>
                                    <div class="device-grid">
                                        <div class="device-item animated-card hover-lift" data-delay="0.1s">
                                            <div class="device-icon heartbeat-animation">💓</div>
                                            <span data-lang-en="ECG Machines" data-lang-ar="أجهزة تخطيط القلب">ECG Machines</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.2s">
                                            <div class="device-icon pulse-animation">🩺</div>
                                            <span data-lang-en="Blood Pressure Monitors" data-lang-ar="أجهزة قياس ضغط الدم">Blood Pressure Monitors</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.3s">
                                            <div class="device-icon wave-animation">🔬</div>
                                            <span data-lang-en="Laboratory Analyzers" data-lang-ar="محللات المختبر">Laboratory Analyzers</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.4s">
                                            <div class="device-icon bounce-animation">📷</div>
                                            <span data-lang-en="Imaging Systems" data-lang-ar="أنظمة التصوير">Imaging Systems</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="category-section">
                                    <h3 data-lang-en="Therapeutic Devices" data-lang-ar="أجهزة العلاج">Therapeutic Devices</h3>
                                    <div class="device-grid">
                                        <div class="device-item animated-card hover-lift" data-delay="0.5s">
                                            <div class="device-icon pulse-animation">⚡</div>
                                            <span data-lang-en="Defibrillators" data-lang-ar="أجهزة إزالة الرجفان">Defibrillators</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.6s">
                                            <div class="device-icon heartbeat-animation">🫁</div>
                                            <span data-lang-en="Ventilators" data-lang-ar="أجهزة التنفس الصناعي">Ventilators</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.7s">
                                            <div class="device-icon rotate-animation">💉</div>
                                            <span data-lang-en="Infusion Pumps" data-lang-ar="مضخات التسريب">Infusion Pumps</span>
                                        </div>
                                        <div class="device-item animated-card hover-lift" data-delay="0.8s">
                                            <div class="device-icon wave-animation">🔥</div>
                                            <span data-lang-en="Electrosurgery Units" data-lang-ar="وحدات الجراحة الكهربائية">Electrosurgery Units</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 11: Summary and Next Steps -->
                    <div class="slide" data-slide="11">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Summary & Next Steps" data-lang-ar="الملخص والخطوات التالية">Summary & Next Steps</h2>
                                <div class="slide-icon pulse-animation">🎯</div>
                            </div>
                            <div class="summary-content">
                                <div class="key-takeaways">
                                    <h3 data-lang-en="Key Takeaways" data-lang-ar="النقاط الرئيسية">Key Takeaways</h3>
                                    <div class="takeaway-list">
                                        <div class="takeaway-item animated-card" data-delay="0.1s">
                                            <div class="takeaway-icon">✅</div>
                                            <p data-lang-en="Biomedical engineering combines engineering principles with medical applications" data-lang-ar="تجمع الهندسة الطبية الحيوية بين المبادئ الهندسية والتطبيقات الطبية">Biomedical engineering combines engineering principles with medical applications</p>
                                        </div>
                                        <div class="takeaway-item animated-card" data-delay="0.2s">
                                            <div class="takeaway-icon">✅</div>
                                            <p data-lang-en="Medical instrumentation involves signal acquisition, processing, and analysis" data-lang-ar="تتضمن الأجهزة الطبية اكتساب الإشارات ومعالجتها وتحليلها">Medical instrumentation involves signal acquisition, processing, and analysis</p>
                                        </div>
                                        <div class="takeaway-item animated-card" data-delay="0.3s">
                                            <div class="takeaway-icon">✅</div>
                                            <p data-lang-en="Virtual labs provide safe, interactive learning experiences" data-lang-ar="توفر المعامل الافتراضية تجارب تعلم آمنة وتفاعلية">Virtual labs provide safe, interactive learning experiences</p>
                                        </div>
                                        <div class="takeaway-item animated-card" data-delay="0.4s">
                                            <div class="takeaway-icon">✅</div>
                                            <p data-lang-en="Signal processing is crucial for accurate medical diagnosis" data-lang-ar="معالجة الإشارات أمر بالغ الأهمية للتشخيص الطبي الدقيق">Signal processing is crucial for accurate medical diagnosis</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="next-steps">
                                    <h3 data-lang-en="Continue Your Learning Journey" data-lang-ar="تابع رحلة التعلم">Continue Your Learning Journey</h3>
                                    <div class="action-buttons">
                                        <a href="ecg-module.html" class="action-btn primary">
                                            <i class="fas fa-heartbeat"></i>
                                            <span data-lang-en="ECG Module" data-lang-ar="وحدة تخطيط القلب">ECG Module</span>
                                        </a>
                                        <a href="virtual_lab.html" class="action-btn secondary">
                                            <i class="fas fa-flask"></i>
                                            <span data-lang-en="Virtual Lab" data-lang-ar="المعمل الافتراضي">Virtual Lab</span>
                                        </a>
                                        <a href="extended-modules.html" class="action-btn tertiary">
                                            <i class="fas fa-book"></i>
                                            <span data-lang-en="All Modules" data-lang-ar="جميع الوحدات">All Modules</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script src="js/slide-deck.js"></script>
</body>
</html>