/* ===== ECG MODULE SPECIFIC STYLES ===== */

/* Module Header */
.module-header {
    margin-bottom: 3rem;
}

.module-hero {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: var(--white);
    padding: 3rem 2rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.module-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0 10 Q25 0 50 10 T100 10" stroke="rgba(255,255,255,0.1)" stroke-width="2" fill="none"/></svg>') repeat-x;
    opacity: 0.3;
    animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scaleX(1); }
    50% { transform: scaleX(1.1); }
}

.module-icon-large {
    font-size: 4rem;
    opacity: 0.9;
    animation: pulse 2s ease-in-out infinite;
}

.module-info {
    flex: 1;
}

.module-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.module-description {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.module-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Module Objectives */
.module-objectives {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 2rem;
    text-align: center;
}

.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.objective-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-100);
}

.objective-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: #dc2626;
}

.objective-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.objective-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.objective-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Module Navigation */
.module-navigation {
    margin-bottom: 2rem;
}

.nav-tabs {
    display: flex;
    background: var(--gray-100);
    border-radius: 0.75rem;
    padding: 0.5rem;
    gap: 0.5rem;
}

.nav-tab {
    flex: 1;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-tab.active {
    background: var(--white);
    color: #dc2626;
    box-shadow: var(--shadow-sm);
}

.nav-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
    color: var(--gray-700);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

/* Section Cards */
.sections-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.section-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.section-card:hover {
    box-shadow: var(--shadow-lg);
    border-color: #dc2626;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.section-header:hover {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-50));
}

.section-number {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    box-shadow: var(--shadow-md);
}

.section-info {
    flex: 1;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
    text-align: left;
}

.section-description {
    color: var(--gray-600);
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.section-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.section-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: 1.25rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.section-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.section-card.expanded .section-toggle {
    transform: rotate(180deg);
}

/* Section Content */
.section-content {
    display: none;
    padding: 2rem;
    background: var(--white);
}

.section-card.expanded .section-content {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.content-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: 0.75rem;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.content-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    border-color: #dc2626;
}

.item-icon {
    font-size: 2rem;
    opacity: 0.8;
    min-width: 3rem;
    text-align: center;
}

.item-info {
    flex: 1;
}

.item-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.item-info p {
    color: var(--gray-600);
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.item-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.item-action {
    background: #dc2626;
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    justify-content: center;
}

.item-action:hover {
    background: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Virtual Lab Styles */
.virtual-lab-container {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.lab-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    text-align: center;
    justify-content: center;
}

.lab-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.lab-info h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.lab-info p {
    color: var(--gray-600);
    font-size: 1.125rem;
}

.lab-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.step-card {
    background: var(--gray-50);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.step-card:hover {
    background: var(--white);
    border-color: #dc2626;
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.step-number {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
    box-shadow: var(--shadow-md);
}

.step-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.step-content p {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.step-action {
    background: #dc2626;
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.step-action:hover {
    background: #b91c1c;
    transform: translateY(-1px);
}

.lab-simulation {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-50));
    border-radius: 1rem;
    padding: 3rem;
    text-align: center;
    border: 2px dashed var(--gray-300);
}

.simulation-placeholder {
    max-width: 400px;
    margin: 0 auto;
}

.simulation-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.simulation-placeholder h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 1rem;
}

.simulation-placeholder p {
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Quiz Styles */
.quiz-container {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.quiz-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    text-align: center;
    justify-content: center;
}

.quiz-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.quiz-info h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.quiz-info p {
    color: var(--gray-600);
    font-size: 1.125rem;
}

.quiz-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
    text-align: center;
}

.quiz-stat {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--gray-200);
}

.quiz-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #dc2626;
    margin-bottom: 0.5rem;
}

.quiz-stat .stat-label {
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.quiz-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
}

.quiz-section {
    background: var(--gray-50);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border-left: 4px solid #dc2626;
}

.quiz-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.quiz-section p {
    color: var(--gray-600);
    line-height: 1.5;
}

.quiz-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .module-hero {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .module-title {
        font-size: 2rem;
    }
    
    .module-description {
        font-size: 1.125rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .section-header {
        padding: 1rem 1.5rem;
        gap: 1rem;
    }
    
    .content-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .lab-steps {
        grid-template-columns: 1fr;
    }
    
    .quiz-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .quiz-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .module-hero {
        padding: 2rem 1rem;
    }
    
    .module-title {
        font-size: 1.75rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .objective-card {
        padding: 1.5rem;
    }
    
    .section-content {
        padding: 1.5rem;
    }
    
    .virtual-lab-container,
    .quiz-container {
        padding: 1.5rem;
    }
}
