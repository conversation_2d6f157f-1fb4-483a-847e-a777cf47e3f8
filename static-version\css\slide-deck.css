/* ===== SLIDE DECK STYLES ===== */

/* Slide Deck Container */
.slide-deck-container {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    margin: 2rem 0;
}

.slide-deck-header {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.deck-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.deck-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
}

.deck-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.slide-counter {
    font-size: 1.125rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.control-buttons {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Slide Container */
.slide-container {
    position: relative;
    min-height: 600px;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Slide Header */
.slide-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    text-align: center;
}

.slide-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.slide-icon {
    font-size: 3rem;
    opacity: 0.8;
}

/* Title Slide */
.title-slide {
    text-align: center;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 4rem 2rem;
}

.title-animation {
    margin-bottom: 3rem;
}

.animated-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    display: block;
}

.title-animation h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.title-animation h2 {
    font-size: 1.5rem;
    color: var(--gray-600);
    font-weight: 500;
}

.title-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    min-width: 150px;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.feature-item span {
    font-weight: 600;
    color: var(--gray-700);
}

.author-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 1.5rem;
    border-radius: 1rem;
    backdrop-filter: blur(10px);
}

.author-info p {
    margin: 0.25rem 0;
    color: var(--gray-700);
}

/* Objectives Grid */
.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.objective-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.objective-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.objective-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.objective-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Two Column Layout */
.content-layout.two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.definition-box {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    border-left: 4px solid #2563eb;
}

.definition-box h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 1rem;
}

.definition-box p {
    color: var(--gray-700);
    line-height: 1.6;
}

.key-areas h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.animated-list {
    list-style: none;
    padding: 0;
}

.list-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    transition: all var(--transition-fast);
}

.list-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateX(5px);
}

.list-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

/* Interactive Diagram */
.interactive-diagram {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.bme-circle {
    position: relative;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 3px solid #0ea5e9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.center-icon {
    font-size: 4rem;
    opacity: 0.8;
}

.orbit-item {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    transform-origin: 150px 150px;
}

.orbit-item[data-angle="0"] { transform: rotate(0deg) translateX(180px) rotate(0deg); }
.orbit-item[data-angle="90"] { transform: rotate(90deg) translateX(180px) rotate(-90deg); }
.orbit-item[data-angle="180"] { transform: rotate(180deg) translateX(180px) rotate(-180deg); }
.orbit-item[data-angle="270"] { transform: rotate(270deg) translateX(180px) rotate(90deg); }

.orbit-icon {
    font-size: 2rem;
    background: var(--white);
    padding: 1rem;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    border: 2px solid #0ea5e9;
}

.orbit-item span {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-align: center;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
}

/* Instrumentation Overview */
.system-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.flow-step {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    border: 1px solid var(--gray-200);
}

.step-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.flow-step h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.flow-step p {
    color: var(--gray-600);
    line-height: 1.5;
    font-size: 0.875rem;
}

.flow-arrow {
    font-size: 1.5rem;
    color: #2563eb;
    margin: 0 0.5rem;
}

/* Interactive Demo */
.interactive-demo {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.demo-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.virtual-patient {
    position: relative;
}

.patient-body {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    margin: 0 auto;
    border: 3px solid #d97706;
}

.heart-animation {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
}

.heart-icon {
    font-size: 3rem;
    position: relative;
    z-index: 2;
}

.electrical-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
}

.wave {
    position: absolute;
    border: 2px solid #dc2626;
    border-radius: 50%;
    opacity: 0;
    animation: wave-pulse 2s infinite;
}

.wave-1 { animation-delay: 0s; }
.wave-2 { animation-delay: 0.7s; }
.wave-3 { animation-delay: 1.4s; }

@keyframes wave-pulse {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

.electrode {
    position: absolute;
    z-index: 3;
}

.electrode-dot {
    width: 12px;
    height: 12px;
    background: #dc2626;
    border-radius: 50%;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-md);
}

.electrode-label {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    background: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.ecg-display {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
}

.ecg-screen {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
    height: 200px;
}

.ecg-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0, 255, 0, 0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 0, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
}

.ecg-svg {
    width: 100%;
    height: 100%;
}

.ecg-path {
    fill: none;
    stroke: #00ff00;
    stroke-width: 2;
    stroke-dasharray: 400;
    stroke-dashoffset: 400;
    animation: ecg-trace 3s ease-in-out infinite;
}

@keyframes ecg-trace {
    to {
        stroke-dashoffset: 0;
    }
}

.ecg-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.demo-btn {
    background: #2563eb;
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.demo-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

/* Lab Preview */
.lab-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

.lab-access {
    text-align: center;
}

.lab-btn {
    background: linear-gradient(135deg, #059669, #10b981);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.125rem;
    box-shadow: var(--shadow-lg);
}

.lab-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes wave {
    0%, 100% { transform: translateY(0); }
    25% { transform: translateY(-5px); }
    75% { transform: translateY(5px); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1); }
    75% { transform: scale(1.05); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes xray-flash {
    0% { background: linear-gradient(45deg, #1a1a1a, #2a2a2a); }
    50% { background: linear-gradient(45deg, #ffffff, #f0f0f0); }
    100% { background: linear-gradient(45deg, #1a1a1a, #2a2a2a); }
}

/* Animation Classes */
.pulse-animation { animation: pulse 2s ease-in-out infinite; }
.bounce-animation { animation: bounce 2s ease-in-out infinite; }
.rotate-animation { animation: rotate 4s linear infinite; }
.wave-animation { animation: wave 3s ease-in-out infinite; }
.heartbeat-animation { animation: heartbeat 1.5s ease-in-out infinite; }

.fade-in { animation: fadeIn 0.8s ease-out forwards; }
.fade-in-up { animation: fadeInUp 0.8s ease-out forwards; }
.fade-in-left { animation: fadeInLeft 0.8s ease-out forwards; }
.fade-in-right { animation: fadeInRight 0.8s ease-out forwards; }

.animated-card {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .demo-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .content-layout.two-column {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .system-flow {
        flex-direction: column;
    }
    
    .flow-arrow {
        transform: rotate(90deg);
    }
}

@media (max-width: 768px) {
    .slide {
        padding: 2rem 1rem;
    }
    
    .slide-header h2 {
        font-size: 2rem;
    }
    
    .title-animation h1 {
        font-size: 2rem;
    }
    
    .title-features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .lab-features {
        grid-template-columns: 1fr;
    }
    
    .deck-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .slide-deck-header {
        padding: 1.5rem;
    }
    
    .deck-title {
        font-size: 1.5rem;
    }
    
    .deck-subtitle {
        font-size: 1rem;
    }
    
    .slide-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .slide-icon {
        font-size: 2rem;
    }
    
    .patient-body {
        width: 250px;
        height: 350px;
    }
}

/* ===== ADDITIONAL SLIDE COMPONENTS ===== */

/* Biosignals Grid */
.biosignals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.signal-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.signal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.signal-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.signal-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.signal-details p {
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.signal-specs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.spec-item {
    background: var(--gray-50);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

/* Processing Pipeline */
.processing-pipeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.pipeline-step {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    width: 100%;
    display: flex;
    align-items: center;
    gap: 2rem;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.pipeline-step:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: #2563eb;
}

.step-number {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.step-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tool-tag {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #93c5fd;
}

.pipeline-arrow {
    font-size: 2rem;
    color: #2563eb;
    margin: 0.5rem 0;
}

/* Filter Demo */
.filter-demo {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.demo-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.demo-select {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.875rem;
    transition: border-color var(--transition-fast);
}

.demo-select:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.demo-slider {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    outline: none;
    transition: background var(--transition-fast);
}

.demo-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #2563eb;
    border-radius: 50%;
    cursor: pointer;
    transition: background var(--transition-fast);
}

.demo-slider::-webkit-slider-thumb:hover {
    background: #1d4ed8;
}

.demo-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #2563eb;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.signal-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.signal-panel {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.signal-panel h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    text-align: center;
}

.signal-canvas {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    height: 120px;
}

.signal-svg {
    width: 100%;
    height: 100%;
}

.signal-path {
    fill: none;
    stroke-width: 2;
    transition: all var(--transition-fast);
}

.signal-path.original {
    stroke: #00ff00;
}

.signal-path.filtered {
    stroke: #00aaff;
}

.noise-path {
    fill: none;
    stroke: #ff6b6b;
    stroke-width: 1;
    opacity: 0.6;
}

.demo-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Device Categories */
.device-categories {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.category-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--gray-200);
}

.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.device-item {
    background: var(--white);
    padding: 2rem 1rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.device-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.device-item span {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-align: center;
}

/* Summary Content */
.summary-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.key-takeaways h3,
.next-steps h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 2rem;
    text-align: center;
}

.takeaway-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.takeaway-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    border-left: 4px solid #10b981;
}

.takeaway-icon {
    font-size: 1.25rem;
    color: #10b981;
    margin-top: 0.125rem;
}

.takeaway-item p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.action-btn.primary {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: var(--white);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #059669, #10b981);
    color: var(--white);
}

.action-btn.tertiary {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn i {
    font-size: 1.25rem;
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
    .biosignals-grid {
        grid-template-columns: 1fr;
    }

    .processing-pipeline {
        gap: 0.5rem;
    }

    .pipeline-step {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pipeline-arrow {
        transform: rotate(90deg);
    }

    .demo-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .signal-display {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .device-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .summary-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .device-grid {
        grid-template-columns: 1fr;
    }

    .signal-card,
    .pipeline-step,
    .device-item {
        padding: 1.5rem;
    }

    .step-number {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .action-buttons {
        gap: 0.75rem;
    }

    .action-btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* ===== MEDICAL IMAGING SLIDES STYLES ===== */

/* Imaging Overview */
.imaging-overview {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.imaging-intro {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.intro-text {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 2rem;
    border-radius: 1rem;
    border-left: 4px solid #0ea5e9;
}

.intro-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0c4a6e;
    margin-bottom: 1rem;
}

.intro-text p {
    color: var(--gray-700);
    line-height: 1.6;
}

.imaging-benefits h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.benefit-list {
    list-style: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.benefit-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
}

.benefit-item i {
    color: #0ea5e9;
    font-size: 1.25rem;
}

.modality-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.modality-card {
    background: var(--white);
    padding: 2rem 1rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.modality-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.modality-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.modality-card p {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* X-Ray Demo Styles */
.xray-demo {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.xray-components {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.component-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    border: 1px solid var(--gray-200);
}

.component-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.component-item h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.component-item p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.component-specs {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.spec {
    background: var(--gray-50);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    color: var(--gray-700);
    font-weight: 500;
}

.xray-interactive {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    border-radius: 1rem;
}

.xray-controls h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.xray-image-display {
    margin-top: 2rem;
}

.image-container {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.xray-image {
    width: 100%;
    height: 200px;
    position: relative;
    background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
    border-radius: 0.25rem;
    overflow: hidden;
}

.skeleton-outline {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.8;
}

.bone-structure {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
}

.chest-bones {
    top: 20%;
    left: 30%;
    width: 40%;
    height: 60%;
    clip-path: polygon(20% 0%, 80% 0%, 90% 30%, 70% 50%, 80% 80%, 20% 80%, 30% 50%, 10% 30%);
}

.arm-bones {
    top: 10%;
    right: 10%;
    width: 15%;
    height: 40%;
    border-radius: 50px;
}

.soft-tissue {
    position: absolute;
    top: 10%;
    left: 20%;
    width: 60%;
    height: 80%;
    background: rgba(128, 128, 128, 0.3);
    border-radius: 50%;
}

.image-info {
    text-align: center;
}

.image-info h5 {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.image-info p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* MRI System Styles */
.mri-system {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.scanner-visualization {
    position: relative;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 2rem;
    border-radius: 1rem;
    height: 300px;
}

.magnet-bore {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 100px;
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.magnetic-field-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.field-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    animation: magnetic-field 3s ease-in-out infinite;
    opacity: 0.6;
}

@keyframes magnetic-field {
    0%, 100% { transform: translateY(0) scaleX(1); }
    50% { transform: translateY(-5px) scaleX(1.1); }
}

.patient-table {
    background: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.patient-icon {
    font-size: 1.5rem;
}

.gradient-coils,
.rf-coils {
    position: absolute;
}

.coil {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #ef4444;
    border-radius: 50%;
    animation: coil-pulse 2s ease-in-out infinite;
}

@keyframes coil-pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

.x-gradient { top: 10%; left: 10%; }
.y-gradient { top: 10%; right: 10%; }
.z-gradient { bottom: 10%; left: 50%; transform: translateX(-50%); }

.rf-coil {
    position: absolute;
    width: 15px;
    height: 15px;
    border: 2px solid #10b981;
    border-radius: 50%;
    animation: rf-pulse 1.5s ease-in-out infinite;
}

@keyframes rf-pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.3); opacity: 1; }
}

.transmit { top: 20%; left: 20%; }
.receive { bottom: 20%; right: 20%; }

.mri-controls {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.sequence-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.mri-image-display {
    text-align: center;
}

.mri-image {
    width: 150px;
    height: 150px;
    background: #000;
    border-radius: 50%;
    margin: 0 auto 1rem;
    position: relative;
    overflow: hidden;
}

.brain-outline {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.brain-tissue {
    position: absolute;
    border-radius: 50%;
}

.gray-matter {
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: rgba(128, 128, 128, 0.8);
}

.white-matter {
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: rgba(200, 200, 200, 0.9);
}

.csf {
    top: 5%;
    left: 5%;
    width: 90%;
    height: 90%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.scan-status {
    font-weight: 500;
    color: var(--gray-700);
}

/* Ultrasound Styles */
.ultrasound-demo {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.wave-propagation {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    padding: 2rem;
    border-radius: 1rem;
}

.wave-container {
    position: relative;
    height: 250px;
    background: linear-gradient(90deg, #e0f2fe, #f0f9ff);
    border-radius: 0.5rem;
    overflow: hidden;
}

.transducer {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    z-index: 3;
}

.transducer-icon {
    font-size: 2rem;
    background: var(--white);
    padding: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
}

.sound-waves {
    position: absolute;
    top: 50%;
    left: 60px;
    transform: translateY(-50%);
}

.wave-pulse {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #0ea5e9;
    border-radius: 50%;
    animation: wave-propagate 2s ease-out infinite;
    opacity: 0;
}

@keyframes wave-propagate {
    0% {
        width: 20px;
        height: 20px;
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
        transform: translateX(200px);
    }
}

.tissue-layers {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 60%;
    display: flex;
    flex-direction: column;
}

.tissue-layer {
    flex: 1;
    position: relative;
    border-left: 2px solid var(--gray-300);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
}

.tissue-layer.skin { background: rgba(255, 220, 177, 0.8); }
.tissue-layer.muscle { background: rgba(255, 182, 193, 0.8); }
.tissue-layer.organ { background: rgba(173, 216, 230, 0.8); }
.tissue-layer.bone { background: rgba(255, 255, 255, 0.9); }

.echo-returns {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.echo-line {
    position: absolute;
    top: 50%;
    left: 200px;
    width: 2px;
    height: 20px;
    background: #ef4444;
    transform: translateY(-50%);
    animation: echo-return 2s ease-in infinite;
    opacity: 0;
}

@keyframes echo-return {
    0% { opacity: 0; transform: translateY(-50%) translateX(0); }
    50% { opacity: 1; }
    100% { opacity: 0; transform: translateY(-50%) translateX(-180px); }
}

.ultrasound-controls {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.us-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.ultrasound-display {
    text-align: center;
}

.us-screen {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.us-image {
    width: 200px;
    height: 150px;
    background: radial-gradient(circle, #1a1a1a, #000);
    border-radius: 0.25rem;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.scan-lines {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 100%;
}

.scan-line {
    position: absolute;
    top: 0;
    left: 50%;
    width: 1px;
    height: 100%;
    background: linear-gradient(to bottom, #00ff00, transparent);
    transform-origin: bottom center;
    animation: scan-sweep 3s ease-in-out infinite;
    opacity: 0.7;
}

@keyframes scan-sweep {
    0%, 100% { transform: translateX(-50%) rotate(var(--angle)); opacity: 0.3; }
    50% { opacity: 0.8; }
}

.anatomical-structures {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.structure {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
}

.structure.heart {
    top: 30%;
    left: 40%;
    width: 20%;
    height: 25%;
    background: rgba(255, 100, 100, 0.8);
}

.structure.liver {
    top: 50%;
    left: 20%;
    width: 30%;
    height: 20%;
    background: rgba(139, 69, 19, 0.8);
}

.structure.kidney {
    top: 60%;
    right: 20%;
    width: 15%;
    height: 20%;
    background: rgba(128, 0, 128, 0.8);
}

.us-measurements {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Image Processing Styles */
.image-processing {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.processing-pipeline {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    border-radius: 1rem;
}

.pipeline-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.processing-step {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    flex: 1;
    min-width: 150px;
    max-width: 200px;
}

.processing-step .step-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.processing-step h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.processing-step p {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
}

.pipeline-arrow {
    font-size: 1.5rem;
    color: #2563eb;
    font-weight: bold;
}

.interactive-processing {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
}

.image-controls {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.enhancement-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.image-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.image-panel {
    text-align: center;
}

.image-panel h5 {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.medical-image {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
    border-radius: 0.5rem;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.image-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.anatomical-feature {
    position: absolute;
    opacity: 0.8;
}

.lung-left {
    top: 20%;
    left: 15%;
    width: 25%;
    height: 50%;
    background: rgba(100, 100, 100, 0.6);
    border-radius: 50%;
}

.lung-right {
    top: 20%;
    right: 15%;
    width: 25%;
    height: 50%;
    background: rgba(100, 100, 100, 0.6);
    border-radius: 50%;
}

.heart-shadow {
    top: 30%;
    left: 40%;
    width: 20%;
    height: 30%;
    background: rgba(150, 150, 150, 0.8);
    border-radius: 50%;
}

.ribs {
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 8px,
        rgba(200, 200, 200, 0.4) 8px,
        rgba(200, 200, 200, 0.4) 10px
    );
    border-radius: 50%;
}

/* Responsive Design for Medical Imaging */
@media (max-width: 1024px) {
    .imaging-intro,
    .xray-demo,
    .mri-system,
    .ultrasound-demo,
    .interactive-processing {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .pipeline-steps {
        flex-direction: column;
    }

    .pipeline-arrow {
        transform: rotate(90deg);
    }
}

@media (max-width: 768px) {
    .xray-components {
        grid-template-columns: 1fr;
    }

    .modality-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .image-comparison {
        grid-template-columns: 1fr;
    }

    .scanner-visualization {
        height: 200px;
    }

    .wave-container {
        height: 200px;
    }
}

@media (max-width: 480px) {
    .modality-grid {
        grid-template-columns: 1fr;
    }

    .benefit-list {
        gap: 0.5rem;
    }

    .benefit-item {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
