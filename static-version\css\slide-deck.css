/* ===== SLIDE DECK STYLES ===== */

/* Slide Deck Container */
.slide-deck-container {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    margin: 2rem 0;
}

.slide-deck-header {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.deck-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.deck-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
}

.deck-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.slide-counter {
    font-size: 1.125rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.control-buttons {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white);
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Slide Container */
.slide-container {
    position: relative;
    min-height: 600px;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease-in-out;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Slide Header */
.slide-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    text-align: center;
}

.slide-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.slide-icon {
    font-size: 3rem;
    opacity: 0.8;
}

/* Title Slide */
.title-slide {
    text-align: center;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 4rem 2rem;
}

.title-animation {
    margin-bottom: 3rem;
}

.animated-icon {
    font-size: 5rem;
    margin-bottom: 2rem;
    display: block;
}

.title-animation h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.title-animation h2 {
    font-size: 1.5rem;
    color: var(--gray-600);
    font-weight: 500;
}

.title-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    min-width: 150px;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.feature-item span {
    font-weight: 600;
    color: var(--gray-700);
}

.author-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 1.5rem;
    border-radius: 1rem;
    backdrop-filter: blur(10px);
}

.author-info p {
    margin: 0.25rem 0;
    color: var(--gray-700);
}

/* Objectives Grid */
.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.objective-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.objective-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.objective-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.objective-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Two Column Layout */
.content-layout.two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.definition-box {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    border-left: 4px solid #2563eb;
}

.definition-box h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 1rem;
}

.definition-box p {
    color: var(--gray-700);
    line-height: 1.6;
}

.key-areas h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.animated-list {
    list-style: none;
    padding: 0;
}

.list-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    transition: all var(--transition-fast);
}

.list-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateX(5px);
}

.list-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

/* Interactive Diagram */
.interactive-diagram {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.bme-circle {
    position: relative;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 3px solid #0ea5e9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.center-icon {
    font-size: 4rem;
    opacity: 0.8;
}

.orbit-item {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    transform-origin: 150px 150px;
}

.orbit-item[data-angle="0"] { transform: rotate(0deg) translateX(180px) rotate(0deg); }
.orbit-item[data-angle="90"] { transform: rotate(90deg) translateX(180px) rotate(-90deg); }
.orbit-item[data-angle="180"] { transform: rotate(180deg) translateX(180px) rotate(-180deg); }
.orbit-item[data-angle="270"] { transform: rotate(270deg) translateX(180px) rotate(90deg); }

.orbit-icon {
    font-size: 2rem;
    background: var(--white);
    padding: 1rem;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    border: 2px solid #0ea5e9;
}

.orbit-item span {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-align: center;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
}

/* Instrumentation Overview */
.system-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.flow-step {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    border: 1px solid var(--gray-200);
}

.step-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.flow-step h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.flow-step p {
    color: var(--gray-600);
    line-height: 1.5;
    font-size: 0.875rem;
}

.flow-arrow {
    font-size: 1.5rem;
    color: #2563eb;
    margin: 0 0.5rem;
}

/* Interactive Demo */
.interactive-demo {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.demo-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.virtual-patient {
    position: relative;
}

.patient-body {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    margin: 0 auto;
    border: 3px solid #d97706;
}

.heart-animation {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
}

.heart-icon {
    font-size: 3rem;
    position: relative;
    z-index: 2;
}

.electrical-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
}

.wave {
    position: absolute;
    border: 2px solid #dc2626;
    border-radius: 50%;
    opacity: 0;
    animation: wave-pulse 2s infinite;
}

.wave-1 { animation-delay: 0s; }
.wave-2 { animation-delay: 0.7s; }
.wave-3 { animation-delay: 1.4s; }

@keyframes wave-pulse {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

.electrode {
    position: absolute;
    z-index: 3;
}

.electrode-dot {
    width: 12px;
    height: 12px;
    background: #dc2626;
    border-radius: 50%;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-md);
}

.electrode-label {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    background: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.ecg-display {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
}

.ecg-screen {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
    height: 200px;
}

.ecg-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0, 255, 0, 0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 0, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
}

.ecg-svg {
    width: 100%;
    height: 100%;
}

.ecg-path {
    fill: none;
    stroke: #00ff00;
    stroke-width: 2;
    stroke-dasharray: 400;
    stroke-dashoffset: 400;
    animation: ecg-trace 3s ease-in-out infinite;
}

@keyframes ecg-trace {
    to {
        stroke-dashoffset: 0;
    }
}

.ecg-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.demo-btn {
    background: #2563eb;
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.demo-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

/* Lab Preview */
.lab-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

.lab-access {
    text-align: center;
}

.lab-btn {
    background: linear-gradient(135deg, #059669, #10b981);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.125rem;
    box-shadow: var(--shadow-lg);
}

.lab-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes wave {
    0%, 100% { transform: translateY(0); }
    25% { transform: translateY(-5px); }
    75% { transform: translateY(5px); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1); }
    75% { transform: scale(1.05); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Animation Classes */
.pulse-animation { animation: pulse 2s ease-in-out infinite; }
.bounce-animation { animation: bounce 2s ease-in-out infinite; }
.rotate-animation { animation: rotate 4s linear infinite; }
.wave-animation { animation: wave 3s ease-in-out infinite; }
.heartbeat-animation { animation: heartbeat 1.5s ease-in-out infinite; }

.fade-in { animation: fadeIn 0.8s ease-out forwards; }
.fade-in-up { animation: fadeInUp 0.8s ease-out forwards; }
.fade-in-left { animation: fadeInLeft 0.8s ease-out forwards; }
.fade-in-right { animation: fadeInRight 0.8s ease-out forwards; }

.animated-card {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .demo-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .content-layout.two-column {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .system-flow {
        flex-direction: column;
    }
    
    .flow-arrow {
        transform: rotate(90deg);
    }
}

@media (max-width: 768px) {
    .slide {
        padding: 2rem 1rem;
    }
    
    .slide-header h2 {
        font-size: 2rem;
    }
    
    .title-animation h1 {
        font-size: 2rem;
    }
    
    .title-features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .lab-features {
        grid-template-columns: 1fr;
    }
    
    .deck-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .slide-deck-header {
        padding: 1.5rem;
    }
    
    .deck-title {
        font-size: 1.5rem;
    }
    
    .deck-subtitle {
        font-size: 1rem;
    }
    
    .slide-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .slide-icon {
        font-size: 2rem;
    }
    
    .patient-body {
        width: 250px;
        height: 350px;
    }
}

/* ===== ADDITIONAL SLIDE COMPONENTS ===== */

/* Biosignals Grid */
.biosignals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.signal-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.signal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.signal-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.signal-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.signal-details p {
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.signal-specs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.spec-item {
    background: var(--gray-50);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    font-weight: 500;
}

/* Processing Pipeline */
.processing-pipeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.pipeline-step {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    width: 100%;
    display: flex;
    align-items: center;
    gap: 2rem;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.pipeline-step:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: #2563eb;
}

.step-number {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.5rem;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.step-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tool-tag {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #93c5fd;
}

.pipeline-arrow {
    font-size: 2rem;
    color: #2563eb;
    margin: 0.5rem 0;
}

/* Filter Demo */
.filter-demo {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.demo-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.demo-select {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.875rem;
    transition: border-color var(--transition-fast);
}

.demo-select:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.demo-slider {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    outline: none;
    transition: background var(--transition-fast);
}

.demo-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #2563eb;
    border-radius: 50%;
    cursor: pointer;
    transition: background var(--transition-fast);
}

.demo-slider::-webkit-slider-thumb:hover {
    background: #1d4ed8;
}

.demo-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #2563eb;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.signal-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.signal-panel {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.signal-panel h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    text-align: center;
}

.signal-canvas {
    background: #000;
    border-radius: 0.5rem;
    padding: 1rem;
    height: 120px;
}

.signal-svg {
    width: 100%;
    height: 100%;
}

.signal-path {
    fill: none;
    stroke-width: 2;
    transition: all var(--transition-fast);
}

.signal-path.original {
    stroke: #00ff00;
}

.signal-path.filtered {
    stroke: #00aaff;
}

.noise-path {
    fill: none;
    stroke: #ff6b6b;
    stroke-width: 1;
    opacity: 0.6;
}

.demo-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Device Categories */
.device-categories {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.category-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--gray-200);
}

.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.device-item {
    background: var(--white);
    padding: 2rem 1rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.device-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.device-item span {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-align: center;
}

/* Summary Content */
.summary-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.key-takeaways h3,
.next-steps h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 2rem;
    text-align: center;
}

.takeaway-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.takeaway-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    border-left: 4px solid #10b981;
}

.takeaway-icon {
    font-size: 1.25rem;
    color: #10b981;
    margin-top: 0.125rem;
}

.takeaway-item p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.action-btn.primary {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: var(--white);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #059669, #10b981);
    color: var(--white);
}

.action-btn.tertiary {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn i {
    font-size: 1.25rem;
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
    .biosignals-grid {
        grid-template-columns: 1fr;
    }

    .processing-pipeline {
        gap: 0.5rem;
    }

    .pipeline-step {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pipeline-arrow {
        transform: rotate(90deg);
    }

    .demo-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .signal-display {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .device-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .summary-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .device-grid {
        grid-template-columns: 1fr;
    }

    .signal-card,
    .pipeline-step,
    .device-item {
        padding: 1.5rem;
    }

    .step-number {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .action-buttons {
        gap: 0.75rem;
    }

    .action-btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}
