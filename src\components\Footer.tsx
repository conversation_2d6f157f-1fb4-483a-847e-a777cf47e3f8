
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      author: 'Author: Dr<PERSON><PERSON>, SUST -BME, @ 2025',
      copyright: 'Copy right',
      email: '<EMAIL>',
      phone: 'Phone: +249912867327, +966538076790',
    },
    ar: {
      author: 'المؤلف: د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - هندسة طبية حيوية، @ 2025',
      copyright: 'حقوق النشر',
      email: '<EMAIL>',
      phone: 'هاتف: +249912867327, +966538076790',
    },
  };

  return (
    <footer className="bg-gray-800 text-white p-4 mt-8">
      <div className="container mx-auto text-center">
        <p>{translations[language].author}</p>
        <p>{translations[language].copyright} &copy; {translations[language].email}, {translations[language].phone}</p>
      </div>
    </footer>
  );
};

export default Footer;
