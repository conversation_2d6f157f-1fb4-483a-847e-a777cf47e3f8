
import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'BioEngage',
      subtitle: 'Interactive Virtual Learning Management System',
      description: 'Advancing biomedical engineering education through innovative virtual learning experiences.',
      quickLinks: 'Quick Links',
      contact: 'Contact Information',
      author: 'Dr. <PERSON>smail',
      institution: 'SUST - Biomedical Engineering Department',
      copyright: 'All rights reserved',
      email: 'Email',
      phone: 'Phone',
      address: 'Sudan University of Science and Technology',
      year: '2025',
      home: 'Home',
      courses: 'Courses',
      training: 'Training',
      virtualLab: 'Virtual Lab',
      about: 'About',
      privacy: 'Privacy Policy',
      terms: 'Terms of Service',
      support: 'Support',
    },
    ar: {
      title: 'بايو إنجيج',
      subtitle: 'نظام إدارة التعلم الافتراضي التفاعلي',
      description: 'تطوير تعليم الهندسة الطبية الحيوية من خلال تجارب التعلم الافتراضي المبتكرة.',
      quickLinks: 'روابط سريعة',
      contact: 'معلومات الاتصال',
      author: 'د. محمد يعقوب إسماعيل',
      institution: 'جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية',
      copyright: 'جميع الحقوق محفوظة',
      email: 'البريد الإلكتروني',
      phone: 'الهاتف',
      address: 'جامعة السودان للعلوم والتكنولوجيا',
      year: '2025',
      home: 'الرئيسية',
      courses: 'الدورات',
      training: 'التدريب',
      virtualLab: 'المعمل الافتراضي',
      about: 'حول',
      privacy: 'سياسة الخصوصية',
      terms: 'شروط الخدمة',
      support: 'الدعم',
    },
  };

  const quickLinks = [
    { path: '/', label: translations[language].home },
    { path: '/courses', label: translations[language].courses },
    { path: '/training', label: translations[language].training },
    { path: '/virtual-lab', label: translations[language].virtualLab },
  ];

  const supportLinks = [
    { path: '/about', label: translations[language].about },
    { path: '/privacy', label: translations[language].privacy },
    { path: '/terms', label: translations[language].terms },
    { path: '/support', label: translations[language].support },
  ];

  return (
    <footer className={`bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 text-white mt-16 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-6 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="text-3xl">🧬</div>
              <div>
                <h3 className="text-2xl font-bold">{translations[language].title}</h3>
                <p className="text-sm opacity-75">{translations[language].subtitle}</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {translations[language].description}
            </p>

            {/* Author Information */}
            <div className="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
              <h4 className="font-semibold mb-2">{translations[language].author}</h4>
              <p className="text-sm text-gray-300 mb-2">{translations[language].institution}</p>
              <div className="space-y-1 text-sm">
                <div className="flex items-center space-x-2">
                  <span>📧</span>
                  <a href="mailto:<EMAIL>" className="text-blue-300 hover:text-blue-200">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center space-x-2">
                  <span>📱</span>
                  <span className="text-gray-300">+249912867327, +966538076790</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🏛️</span>
                  <span className="text-gray-300">{translations[language].address}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{translations[language].quickLinks}</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
                  >
                    <span>→</span>
                    <span>{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{translations[language].support}</h4>
            <ul className="space-y-2">
              {supportLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2"
                  >
                    <span>→</span>
                    <span>{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <p className="text-gray-300">
                © {translations[language].year} {translations[language].title} - {translations[language].copyright}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                {language === 'en'
                  ? 'Developed with ❤️ for biomedical engineering education'
                  : 'تم التطوير بـ ❤️ لتعليم الهندسة الطبية الحيوية'
                }
              </p>
            </div>

            {/* Social Links (placeholder) */}
            <div className="flex space-x-4">
              <div className="text-2xl opacity-50">📧</div>
              <div className="text-2xl opacity-50">🌐</div>
              <div className="text-2xl opacity-50">📱</div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Bottom Border */}
      <div className="h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"></div>
    </footer>
  );
};

export default Footer;
