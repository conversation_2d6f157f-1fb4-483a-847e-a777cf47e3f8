
import React, { useContext, useState } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

interface Lecture {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  duration: { en: string; ar: string };
  slides: number;
  interactiveElements: number;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  icon: string;
  completed: boolean;
}

interface Slide {
  id: number;
  title: { en: string; ar: string };
  content: { en: string; ar: string };
  type: 'text' | 'image' | 'video' | 'interactive' | 'quiz';
  media?: string;
  interactiveElement?: {
    type: 'drag-drop' | 'click-hotspot' | 'slider' | 'quiz';
    data: any;
  };
}

const InteractiveLectures: React.FC = () => {
  const { language } = useContext(LanguageContext);
  const [selectedLecture, setSelectedLecture] = useState<string | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showQuiz, setShowQuiz] = useState(false);

  const translations = {
    en: {
      title: 'Interactive Lectures & Presentations',
      subtitle: 'Dynamic Learning with Multimedia Content',
      description: 'Engage with interactive presentations featuring animations, videos, quizzes, and hands-on activities.',
      selectLecture: 'Select a Lecture',
      startLecture: 'Start Lecture',
      duration: 'Duration',
      slides: 'Slides',
      interactive: 'Interactive Elements',
      level: 'Level',
      category: 'Category',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      nextSlide: 'Next Slide',
      previousSlide: 'Previous Slide',
      backToLectures: 'Back to Lectures',
      slideOf: 'Slide {current} of {total}',
      completed: 'Completed',
      takeQuiz: 'Take Quiz',
      showAnswer: 'Show Answer',
      correct: 'Correct!',
      incorrect: 'Incorrect. Try again.',
      categories: {
        fundamentals: 'Fundamentals',
        instrumentation: 'Instrumentation',
        imaging: 'Medical Imaging',
        signals: 'Signal Processing'
      }
    },
    ar: {
      title: 'المحاضرات والعروض التفاعلية',
      subtitle: 'تعلم ديناميكي مع المحتوى متعدد الوسائط',
      description: 'تفاعل مع العروض التقديمية التفاعلية التي تتضمن الرسوم المتحركة والفيديوهات والاختبارات والأنشطة العملية.',
      selectLecture: 'اختر محاضرة',
      startLecture: 'ابدأ المحاضرة',
      duration: 'المدة',
      slides: 'الشرائح',
      interactive: 'العناصر التفاعلية',
      level: 'المستوى',
      category: 'الفئة',
      beginner: 'مبتدئ',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      nextSlide: 'الشريحة التالية',
      previousSlide: 'الشريحة السابقة',
      backToLectures: 'العودة إلى المحاضرات',
      slideOf: 'شريحة {current} من {total}',
      completed: 'مكتمل',
      takeQuiz: 'خذ الاختبار',
      showAnswer: 'أظهر الإجابة',
      correct: 'صحيح!',
      incorrect: 'خطأ. حاول مرة أخرى.',
      categories: {
        fundamentals: 'الأساسيات',
        instrumentation: 'الأجهزة',
        imaging: 'التصوير الطبي',
        signals: 'معالجة الإشارات'
      }
    },
  };

  const lectures: Lecture[] = [
    {
      id: 'intro-bme',
      title: {
        en: 'Introduction to Biomedical Engineering',
        ar: 'مقدمة في الهندسة الطبية الحيوية'
      },
      description: {
        en: 'Overview of biomedical engineering field, applications, and career opportunities.',
        ar: 'نظرة عامة على مجال الهندسة الطبية الحيوية والتطبيقات والفرص المهنية.'
      },
      duration: { en: '45 min', ar: '45 دقيقة' },
      slides: 25,
      interactiveElements: 5,
      category: 'fundamentals',
      level: 'beginner',
      icon: '🎓',
      completed: false
    },
    {
      id: 'ecg-principles',
      title: {
        en: 'ECG Principles and Instrumentation',
        ar: 'مبادئ وأجهزة تخطيط القلب'
      },
      description: {
        en: 'Comprehensive overview of ECG technology, from physiological basis to clinical applications.',
        ar: 'نظرة شاملة على تقنية تخطيط القلب، من الأساس الفسيولوجي إلى التطبيقات السريرية.'
      },
      duration: { en: '60 min', ar: '60 دقيقة' },
      slides: 35,
      interactiveElements: 8,
      category: 'instrumentation',
      level: 'intermediate',
      icon: '💓',
      completed: false
    },
    {
      id: 'medical-imaging-intro',
      title: {
        en: 'Medical Imaging Modalities',
        ar: 'طرق التصوير الطبي'
      },
      description: {
        en: 'Introduction to X-ray, CT, MRI, ultrasound, and nuclear medicine imaging techniques.',
        ar: 'مقدمة في تقنيات التصوير بالأشعة السينية والمقطعية والرنين المغناطيسي والموجات فوق الصوتية والطب النووي.'
      },
      duration: { en: '75 min', ar: '75 دقيقة' },
      slides: 40,
      interactiveElements: 10,
      category: 'imaging',
      level: 'intermediate',
      icon: '🏥',
      completed: false
    },
    {
      id: 'signal-processing-basics',
      title: {
        en: 'Digital Signal Processing for Biomedical Applications',
        ar: 'معالجة الإشارات الرقمية للتطبيقات الطبية الحيوية'
      },
      description: {
        en: 'Fundamentals of digital signal processing techniques applied to biomedical signals.',
        ar: 'أساسيات تقنيات معالجة الإشارات الرقمية المطبقة على الإشارات الطبية الحيوية.'
      },
      duration: { en: '90 min', ar: '90 دقيقة' },
      slides: 50,
      interactiveElements: 12,
      category: 'signals',
      level: 'advanced',
      icon: '📊',
      completed: false
    }
  ];

  // Sample slides for ECG lecture
  const ecgSlides: Slide[] = [
    {
      id: 1,
      title: {
        en: 'What is an ECG?',
        ar: 'ما هو تخطيط القلب؟'
      },
      content: {
        en: 'An electrocardiogram (ECG) is a medical test that records the electrical activity of the heart over time.',
        ar: 'تخطيط القلب الكهربائي هو فحص طبي يسجل النشاط الكهربائي للقلب عبر الزمن.'
      },
      type: 'text',
      media: '💓'
    },
    {
      id: 2,
      title: {
        en: 'Heart Electrical System',
        ar: 'النظام الكهربائي للقلب'
      },
      content: {
        en: 'The heart has its own electrical system that controls the heartbeat. This system generates electrical impulses that spread through the heart muscle.',
        ar: 'للقلب نظامه الكهربائي الخاص الذي يتحكم في نبضات القلب. ينتج هذا النظام نبضات كهربائية تنتشر عبر عضلة القلب.'
      },
      type: 'interactive',
      media: '🫀',
      interactiveElement: {
        type: 'click-hotspot',
        data: {
          hotspots: [
            { x: 50, y: 30, label: 'SA Node' },
            { x: 70, y: 60, label: 'AV Node' },
            { x: 40, y: 80, label: 'Bundle of His' }
          ]
        }
      }
    },
    {
      id: 3,
      title: {
        en: 'ECG Waveforms',
        ar: 'أشكال موجات تخطيط القلب'
      },
      content: {
        en: 'The ECG shows characteristic waveforms: P wave (atrial depolarization), QRS complex (ventricular depolarization), and T wave (ventricular repolarization).',
        ar: 'يُظهر تخطيط القلب أشكال موجات مميزة: موجة P (إزالة استقطاب الأذينين)، مجموعة QRS (إزالة استقطاب البطينين)، وموجة T (إعادة استقطاب البطينين).'
      },
      type: 'image',
      media: '📈'
    },
    {
      id: 4,
      title: {
        en: 'Quiz: ECG Components',
        ar: 'اختبار: مكونات تخطيط القلب'
      },
      content: {
        en: 'Which wave represents ventricular depolarization?',
        ar: 'أي موجة تمثل إزالة استقطاب البطينين؟'
      },
      type: 'quiz',
      interactiveElement: {
        type: 'quiz',
        data: {
          question: {
            en: 'Which wave represents ventricular depolarization?',
            ar: 'أي موجة تمثل إزالة استقطاب البطينين؟'
          },
          options: [
            { en: 'P wave', ar: 'موجة P' },
            { en: 'QRS complex', ar: 'مجموعة QRS' },
            { en: 'T wave', ar: 'موجة T' },
            { en: 'U wave', ar: 'موجة U' }
          ],
          correct: 1
        }
      }
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const selectedLectureData = lectures.find(lecture => lecture.id === selectedLecture);
  const slides = selectedLecture === 'ecg-principles' ? ecgSlides : [];

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-12 px-6 rounded-lg mb-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4">{translations[language].title}</h1>
          <h2 className="text-xl mb-4 opacity-90">{translations[language].subtitle}</h2>
          <p className="text-lg opacity-80">{translations[language].description}</p>
        </div>
      </div>

      {!selectedLecture ? (
        /* Lecture Selection */
        <div>
          <h2 className="text-2xl font-bold mb-6">{translations[language].selectLecture}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {lectures.map((lecture) => (
              <div key={lecture.id} className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-6 text-center">
                  <div className="text-4xl mb-2">{lecture.icon}</div>
                  <h3 className="text-xl font-bold text-white">{lecture.title[language]}</h3>
                  <div className="text-sm opacity-80 mt-1">
                    {translations[language].categories[lecture.category]}
                  </div>
                </div>

                <div className="p-6">
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {lecture.description[language]}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].level}:</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getLevelColor(lecture.level)}`}>
                        {translations[language][lecture.level]}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].duration}:</span>
                      <span className="text-sm font-medium">{lecture.duration[language]}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].slides}:</span>
                      <span className="text-sm font-medium">{lecture.slides}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].interactive}:</span>
                      <span className="text-sm font-medium">{lecture.interactiveElements}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => setSelectedLecture(lecture.id)}
                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200"
                  >
                    {translations[language].startLecture}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        /* Lecture Presentation */
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">{selectedLectureData?.title[language]}</h2>
            <button
              onClick={() => {
                setSelectedLecture(null);
                setCurrentSlide(0);
              }}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              {translations[language].backToLectures}
            </button>
          </div>

          {slides.length > 0 && (
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Slide Progress */}
              <div className="bg-gray-100 px-6 py-3 border-b">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {translations[language].slideOf
                      .replace('{current}', (currentSlide + 1).toString())
                      .replace('{total}', slides.length.toString())}
                  </span>
                  <div className="w-64 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Slide Content */}
              <div className="p-8">
                <div className="text-center mb-8">
                  <div className="text-6xl mb-4">{slides[currentSlide].media}</div>
                  <h3 className="text-3xl font-bold mb-6">{slides[currentSlide].title[language]}</h3>
                  <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
                    {slides[currentSlide].content[language]}
                  </p>
                </div>

                {/* Interactive Elements */}
                {slides[currentSlide].type === 'interactive' && (
                  <div className="bg-blue-50 rounded-lg p-6 mb-6">
                    <div className="text-center">
                      <h4 className="text-lg font-semibold mb-4">
                        {language === 'en' ? 'Interactive Element' : 'عنصر تفاعلي'}
                      </h4>
                      <div className="relative bg-white rounded-lg p-8 border-2 border-dashed border-blue-300">
                        <div className="text-4xl mb-4">🫀</div>
                        <p className="text-gray-600">
                          {language === 'en'
                            ? 'Click on the heart components to learn more'
                            : 'انقر على مكونات القلب لتتعلم المزيد'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Quiz */}
                {slides[currentSlide].type === 'quiz' && slides[currentSlide].interactiveElement && (
                  <div className="bg-yellow-50 rounded-lg p-6 mb-6">
                    <h4 className="text-lg font-semibold mb-4">
                      {language === 'en' ? 'Quick Quiz' : 'اختبار سريع'}
                    </h4>
                    <div className="space-y-3">
                      {slides[currentSlide].interactiveElement!.data.options.map((option: any, index: number) => (
                        <button
                          key={index}
                          className="w-full text-left p-3 bg-white rounded-lg border hover:bg-blue-50 transition-colors"
                          onClick={() => {
                            if (index === slides[currentSlide].interactiveElement!.data.correct) {
                              alert(translations[language].correct);
                            } else {
                              alert(translations[language].incorrect);
                            }
                          }}
                        >
                          {option[language]}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="bg-gray-50 px-6 py-4 flex justify-between">
                <button
                  onClick={() => setCurrentSlide(Math.max(0, currentSlide - 1))}
                  disabled={currentSlide === 0}
                  className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                >
                  {translations[language].previousSlide}
                </button>

                <button
                  onClick={() => setCurrentSlide(Math.min(slides.length - 1, currentSlide + 1))}
                  disabled={currentSlide === slides.length - 1}
                  className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-300 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                >
                  {translations[language].nextSlide}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InteractiveLectures;
