
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const InteractiveLectures: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'Interactive Lectures',
      description: 'Engage with our interactive lectures.',
    },
    ar: {
      title: 'محاضرات تفاعلية',
      description: 'تفاعل مع محاضراتنا التفاعلية.',
    },
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">{translations[language].title}</h1>
      <p>{translations[language].description}</p>
      {/* Interactive lecture content will be added here */}
    </div>
  );
};

export default InteractiveLectures;
