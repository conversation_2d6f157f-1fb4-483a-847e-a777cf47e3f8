<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MedEng Learn - Medical Engineering Learning Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .progress-ring__circle {
            transition: stroke-dashoffset 0.35s;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        .topic-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .topic-content.expanded {
            max-height: 1000px;
        }
        .quiz-option:hover {
            background-color: #f0f7ff;
        }
        .quiz-option.selected {
            background-color: #e1f0ff;
            border-color: #3b82f6;
        }
        .quiz-option.correct {
            background-color: #d1fae5;
            border-color: #10b981;
        }
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                top: 0;
                z-index: 50;
                height: 100vh;
            }
            .sidebar.active {
                left: 0;
            }
            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0,0,0,0.5);
                z-index: 40;
                display: none;
            }
            .overlay.active {
                display: block;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Mobile Menu Button -->
    <div class="md:hidden fixed top-4 left-4 z-50">
        <button id="mobileMenuBtn" class="p-2 rounded-md bg-white shadow-md text-blue-600">
            <i class="fas fa-bars text-xl"></i>
        </button>
    </div>

    <!-- Overlay for mobile menu -->
    <div id="overlay" class="overlay"></div>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar w-64 bg-white shadow-md fixed h-full overflow-y-auto">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                    ME
                </div>
                <div>
                    <h1 class="font-bold text-blue-600">MedEng Learn</h1>
                    <p class="text-xs text-gray-500">Medical Engineering Education</p>
                </div>
            </div>
        </div>
        
        <div class="p-4">
            <div class="relative mb-4">
                <input type="text" placeholder="Search courses..." class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
            
            <nav>
                <div>
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Main Menu</h3>
                    <ul>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg bg-blue-50 text-blue-600">
                                <i class="fas fa-home w-5 text-center"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-book w-5 text-center"></i>
                                <span>My Courses</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-tasks w-5 text-center"></i>
                                <span>Assignments</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-chart-line w-5 text-center"></i>
                                <span>Progress</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="mt-6">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Categories</h3>
                    <ul>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-heartbeat w-5 text-center"></i>
                                <span>Biomedical Devices</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-x-ray w-5 text-center"></i>
                                <span>Medical Imaging</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-dna w-5 text-center"></i>
                                <span>Biomaterials</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-procedures w-5 text-center"></i>
                                <span>Rehabilitation</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <div class="mt-6">
                    <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Education Level</h3>
                    <ul>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-certificate w-5 text-center"></i>
                                <span>Diploma</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 text-gray-700">
                                <i class="fas fa-graduation-cap w-5 text-center"></i>
                                <span>Bachelor's</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="md:ml-64">
        <!-- Top Navigation -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8 flex justify-between items-center">
                <h1 class="text-xl font-bold text-gray-900">Dashboard</h1>
                
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-full hover:bg-gray-100">
                        <i class="fas fa-bell text-gray-600"></i>
                    </button>
                    <div class="relative">
                        <button id="profileBtn" class="flex items-center space-x-2 focus:outline-none">
                            <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                                JD
                            </div>
                            <span class="hidden md:inline text-gray-700">John Doe</span>
                            <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                        </button>
                        
                        <div id="profileDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
            <!-- Welcome Banner -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white mb-8">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                        <h2 class="text-2xl font-bold mb-2">Welcome back, John!</h2>
                        <p class="mb-4">Continue your learning journey in Medical Engineering</p>
                        <button class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition">
                            View My Courses
                        </button>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <img src="https://cdn-icons-png.flaticon.com/512/2936/2936886.png" alt="Learning illustration" class="h-32">
                    </div>
                </div>
            </div>

            <!-- Progress Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-medium text-gray-700">Courses in Progress</h3>
                        <div class="w-12 h-12 flex items-center justify-center bg-blue-100 rounded-full text-blue-600">
                            <i class="fas fa-book-open text-xl"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold mb-2">3</p>
                    <p class="text-sm text-gray-500">Active courses you're currently taking</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-medium text-gray-700">Completion Rate</h3>
                        <div class="w-12 h-12 flex items-center justify-center bg-green-100 rounded-full text-green-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold mb-2">68%</p>
                    <p class="text-sm text-gray-500">Average completion across all courses</p>
                </div>
                
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-medium text-gray-700">Upcoming Deadlines</h3>
                        <div class="w-12 h-12 flex items-center justify-center bg-yellow-100 rounded-full text-yellow-600">
                            <i class="fas fa-calendar-alt text-xl"></i>
                        </div>
                    </div>
                    <p class="text-3xl font-bold mb-2">2</p>
                    <p class="text-sm text-gray-500">Assignments due this week</p>
                </div>
            </div>

            <!-- Active Courses -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Active Courses</h2>
                    <a href="#" class="text-blue-600 hover:underline">View All</a>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Course Card 1 -->
                    <div class="course-card bg-white rounded-xl shadow-sm overflow-hidden transition duration-300">
                        <div class="h-40 bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center">
                            <i class="fas fa-heartbeat text-white text-5xl"></i>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-bold text-lg">Medical Devices</h3>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Bachelor's</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Introduction to medical device classification and applications</p>
                            
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-500 mb-1">
                                    <span>Progress</span>
                                    <span>45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500"><i class="fas fa-book-open mr-1"></i> 8/15 Topics</span>
                                <span class="text-gray-500"><i class="fas fa-clock mr-1"></i> 12h left</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Card 2 -->
                    <div class="course-card bg-white rounded-xl shadow-sm overflow-hidden transition duration-300">
                        <div class="h-40 bg-gradient-to-r from-green-500 to-teal-600 flex items-center justify-center">
                            <i class="fas fa-x-ray text-white text-5xl"></i>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-bold text-lg">Medical Imaging</h3>
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Bachelor's</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Principles of X-ray, MRI, Ultrasound and other imaging techniques</p>
                            
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-500 mb-1">
                                    <span>Progress</span>
                                    <span>72%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 72%"></div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500"><i class="fas fa-book-open mr-1"></i> 11/15 Topics</span>
                                <span class="text-gray-500"><i class="fas fa-clock mr-1"></i> 6h left</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Card 3 -->
                    <div class="course-card bg-white rounded-xl shadow-sm overflow-hidden transition duration-300">
                        <div class="h-40 bg-gradient-to-r from-red-500 to-pink-600 flex items-center justify-center">
                            <i class="fas fa-dna text-white text-5xl"></i>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-bold text-lg">Biomaterials</h3>
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Diploma</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Introduction to materials used in medical applications</p>
                            
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-500 mb-1">
                                    <span>Progress</span>
                                    <span>30%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-red-600 h-2 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-500"><i class="fas fa-book-open mr-1"></i> 3/10 Topics</span>
                                <span class="text-gray-500"><i class="fas fa-clock mr-1"></i> 20h left</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommended Courses -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">Recommended For You</h2>
                    <a href="#" class="text-blue-600 hover:underline">Browse All</a>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Recommended Course 1 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                        <div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-procedures text-white text-4xl"></i>
                        </div>
                        <div class="p-4">
                            <h3 class="font-medium mb-1">Rehabilitation Eng.</h3>
                            <p class="text-gray-600 text-xs mb-2">Prosthetics, orthotics and assistive devices</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Bachelor's</span>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommended Course 2 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                        <div class="h-32 bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center">
                            <i class="fas fa-brain text-white text-4xl"></i>
                        </div>
                        <div class="p-4">
                            <h3 class="font-medium mb-1">Neural Engineering</h3>
                            <p class="text-gray-600 text-xs mb-2">Brain-computer interfaces and neuroprosthetics</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Bachelor's</span>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommended Course 3 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                        <div class="h-32 bg-gradient-to-r from-orange-500 to-amber-600 flex items-center justify-center">
                            <i class="fas fa-microscope text-white text-4xl"></i>
                        </div>
                        <div class="p-4">
                            <h3 class="font-medium mb-1">Lab Techniques</h3>
                            <p class="text-gray-600 text-xs mb-2">Essential lab skills for biomedical engineers</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Diploma</span>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommended Course 4 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                        <div class="h-32 bg-gradient-to-r from-emerald-500 to-teal-600 flex items-center justify-center">
                            <i class="fas fa-heart text-white text-4xl"></i>
                        </div>
                        <div class="p-4">
                            <h3 class="font-medium mb-1">Cardiovascular Eng.</h3>
                            <p class="text-gray-600 text-xs mb-2">Heart valves, stents and circulatory devices</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Bachelor's</span>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Page Example (Hidden by default, shown when navigating to a course) -->
            <div id="coursePage" class="hidden">
                <!-- Course Header -->
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Bachelor's Level</span>
                                <span class="text-xs text-gray-500">Biomedical Devices</span>
                            </div>
                            <h1 class="text-2xl font-bold mb-2">Introduction to Medical Devices</h1>
                            <p class="text-gray-600 mb-4">This course covers the fundamental principles of medical device classification, regulatory requirements, and applications in clinical settings.</p>
                            
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">15 Topics</span>
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">8 Hours</span>
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">5 Quizzes</span>
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">2 Assignments</span>
                            </div>
                        </div>
                        
                        <div class="mt-4 md:mt-0">
                            <div class="flex items-center">
                                <div class="relative w-20 h-20">
                                    <svg class="w-full h-full" viewBox="0 0 36 36">
                                        <circle cx="18" cy="18" r="16" fill="none" stroke="#e6e6e6" stroke-width="2"></circle>
                                        <circle cx="18" cy="18" r="16" fill="none" stroke="#3b82f6" stroke-width="2" stroke-dasharray="100, 100" stroke-dashoffset="55" class="progress-ring__circle"></circle>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-lg font-bold">45%</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-gray-500">Course Progress</p>
                                    <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition">
                                        Continue Learning
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Course Content -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Course Syllabus -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Course Syllabus</h2>
                            </div>
                            
                            <div class="divide-y divide-gray-200">
                                <!-- Module 1 -->
                                <div class="p-6">
                                    <div class="flex items-center justify-between cursor-pointer" onclick="toggleModule(1)">
                                        <h3 class="font-medium text-lg">Module 1: Fundamentals of Medical Devices</h3>
                                        <i class="fas fa-chevron-down transition-transform duration-300" id="module1Icon"></i>
                                    </div>
                                    
                                    <div class="topic-content mt-4" id="module1Content">
                                        <!-- Topic 1.1 -->
                                        <div class="pl-4 mb-4">
                                            <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                                                        <i class="fas fa-play text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium">1.1 Introduction to Medical Engineering</h4>
                                                        <p class="text-xs text-gray-500">15 min • Video</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Completed</span>
                                                    <button class="text-gray-400 hover:text-gray-600">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Topic 1.2 -->
                                        <div class="pl-4 mb-4">
                                            <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                                                        <i class="fas fa-file-alt text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium">1.2 Device Classification</h4>
                                                        <p class="text-xs text-gray-500">Reading • 20 min</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Completed</span>
                                                    <button class="text-gray-400 hover:text-gray-600">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Topic 1.3 -->
                                        <div class="pl-4 mb-4">
                                            <div class="flex items-center justify-between bg-white border border-blue-200 p-3 rounded-lg">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white mr-3">
                                                        <i class="fas fa-question text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium">1.3 Regulatory Requirements</h4>
                                                        <p class="text-xs text-gray-500">Quiz • 10 min</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Current</span>
                                                    <button class="text-gray-400 hover:text-gray-600">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Topic 1.4 -->
                                        <div class="pl-4">
                                            <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg opacity-50">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 mr-3">
                                                        <i class="fas fa-video text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-400">1.4 Case Studies</h4>
                                                        <p class="text-xs text-gray-400">Video • 25 min</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded-full">Locked</span>
                                                    <button class="text-gray-300 hover:text-gray-400">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Module 2 -->
                                <div class="p-6">
                                    <div class="flex items-center justify-between cursor-pointer" onclick="toggleModule(2)">
                                        <h3 class="font-medium text-lg">Module 2: Diagnostic Devices</h3>
                                        <i class="fas fa-chevron-down transition-transform duration-300" id="module2Icon"></i>
                                    </div>
                                    
                                    <div class="topic-content mt-4" id="module2Content" style="display: none;">
                                        <!-- Topics would be listed here similarly -->
                                        <div class="pl-4 mb-4">
                                            <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg opacity-50">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 mr-3">
                                                        <i class="fas fa-play text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-400">2.1 Imaging Devices</h4>
                                                        <p class="text-xs text-gray-400">Video • 30 min</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded-full">Locked</span>
                                                    <button class="text-gray-300 hover:text-gray-400">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Module 3 -->
                                <div class="p-6">
                                    <div class="flex items-center justify-between cursor-pointer" onclick="toggleModule(3)">
                                        <h3 class="font-medium text-lg">Module 3: Therapeutic Devices</h3>
                                        <i class="fas fa-chevron-down transition-transform duration-300" id="module3Icon"></i>
                                    </div>
                                    
                                    <div class="topic-content mt-4" id="module3Content" style="display: none;">
                                        <!-- Topics would be listed here similarly -->
                                        <div class="pl-4 mb-4">
                                            <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg opacity-50">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 mr-3">
                                                        <i class="fas fa-play text-xs"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-400">3.1 Surgical Devices</h4>
                                                        <p class="text-xs text-gray-400">Video • 25 min</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-xs bg-gray-200 text-gray-500 px-2 py-1 rounded-full">Locked</span>
                                                    <button class="text-gray-300 hover:text-gray-400">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Course Resources -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Course Resources</h2>
                            </div>
                            
                            <div class="p-6">
                                <div class="mb-4">
                                    <h3 class="font-medium mb-2">Downloadable Materials</h3>
                                    <ul class="space-y-2">
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-file-pdf mr-2 text-red-500"></i>
                                                <span>Course Syllabus (PDF)</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-file-powerpoint mr-2 text-orange-500"></i>
                                                <span>Lecture Slides (PPT)</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-file-word mr-2 text-blue-500"></i>
                                                <span>Reading Materials (DOC)</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h3 class="font-medium mb-2">Additional Resources</h3>
                                    <ul class="space-y-2">
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-link mr-2 text-gray-500"></i>
                                                <span>FDA Medical Device Guidelines</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-book mr-2 text-purple-500"></i>
                                                <span>Recommended Textbook</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-video mr-2 text-blue-400"></i>
                                                <span>Supplementary Videos</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Discussion Forum</h2>
                            </div>
                            
                            <div class="p-6">
                                <div class="mb-4">
                                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="Ask a question or share your thoughts..."></textarea>
                                    <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition">
                                        Post Question
                                    </button>
                                </div>
                                
                                <div class="space-y-4">
                                    <div class="border-b border-gray-200 pb-4">
                                        <div class="flex items-start mb-2">
                                            <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
                                                <i class="fas fa-user text-sm"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium">Sarah Johnson</h4>
                                                <p class="text-xs text-gray-500">2 hours ago</p>
                                            </div>
                                        </div>
                                        <p class="text-sm ml-11">Can someone explain the difference between Class II and Class III medical devices with examples?</p>
                                        <div class="flex items-center text-xs text-gray-500 mt-2 ml-11 space-x-3">
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-reply mr-1"></i> Reply</a>
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-thumbs-up mr-1"></i> Like (3)</a>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="flex items-start mb-2">
                                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                                                <i class="fas fa-user-tie text-sm"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium">Prof. Davis</h4>
                                                <p class="text-xs text-gray-500">1 hour ago</p>
                                            </div>
                                        </div>
                                        <p class="text-sm ml-11">Great question! Class II devices have moderate risk (e.g., infusion pumps) while Class III supports/sustains life (e.g., pacemakers). We'll cover this in detail next week.</p>
                                        <div class="flex items-center text-xs text-gray-500 mt-2 ml-11 space-x-3">
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-reply mr-1"></i> Reply</a>
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-thumbs-up mr-1"></i> Like (8)</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Topic Page Example (Hidden by default, shown when navigating to a topic) -->
            <div id="topicPage" class="hidden">
                <!-- Topic Header -->
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <div class="flex items-center space-x-2 mb-2">
                                <a href="#" class="text-blue-600 hover:underline">Introduction to Medical Devices</a>
                                <span class="text-gray-400">/</span>
                                <span>Module 1: Fundamentals</span>
                                <span class="text-gray-400">/</span>
                                <span class="font-medium">1.3 Regulatory Requirements</span>
                            </div>
                            <h1 class="text-2xl font-bold mb-4">Regulatory Requirements for Medical Devices</h1>
                            
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Quiz</span>
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">10 min</span>
                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">5 Questions</span>
                            </div>
                        </div>
                        
                        <div class="mt-4 md:mt-0">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition mr-2">
                                <i class="fas fa-arrow-left mr-1"></i> Previous
                            </button>
                            <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-300 transition" disabled>
                                Next <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Topic Content -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Main Content -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
                            <div class="p-6">
                                <h2 class="text-xl font-bold mb-4">Quiz: Regulatory Requirements</h2>
                                <p class="text-gray-600 mb-6">Test your understanding of medical device regulations with this short quiz. You need to score at least 80% to proceed to the next topic.</p>
                                
                                <!-- Quiz Question 1 -->
                                <div class="mb-8">
                                    <div class="flex items-center mb-4">
                                        <span class="w-8 h-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium mr-3">1</span>
                                        <h3 class="font-medium">Which regulatory body is primarily responsible for medical device approval in the United States?</h3>
                                    </div>
                                    
                                    <div class="space-y-3 ml-11">
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'correct')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>FDA (Food and Drug Administration)</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>CDC (Centers for Disease Control)</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>NIH (National Institutes of Health)</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>OSHA (Occupational Safety and Health Administration)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Quiz Question 2 -->
                                <div class="mb-8">
                                    <div class="flex items-center mb-4">
                                        <span class="w-8 h-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium mr-3">2</span>
                                        <h3 class="font-medium">Which class of medical devices represents the highest risk category?</h3>
                                    </div>
                                    
                                    <div class="space-y-3 ml-11">
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Class I</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Class II</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'correct')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Class III</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Class IV</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Quiz Question 3 -->
                                <div class="mb-8">
                                    <div class="flex items-center mb-4">
                                        <span class="w-8 h-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium mr-3">3</span>
                                        <h3 class="font-medium">What is the purpose of the CE marking on medical devices in Europe?</h3>
                                    </div>
                                    
                                    <div class="space-y-3 ml-11">
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Indicates the device was made in Europe</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'correct')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Shows compliance with EU health and safety requirements</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Identifies the manufacturer</span>
                                            </div>
                                        </div>
                                        <div class="quiz-option p-3 border border-gray-200 rounded-lg cursor-pointer" onclick="selectOption(this, 'incorrect')">
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 rounded-full border border-gray-300 mr-3 flex-shrink-0"></div>
                                                <span>Guarantees the highest quality standard</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex justify-end">
                                    <button id="submitQuizBtn" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition">
                                        Submit Quiz
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Discussion Section -->
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Questions & Discussion</h2>
                            </div>
                            
                            <div class="p-6">
                                <div class="mb-4">
                                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="Ask a question about this topic..."></textarea>
                                    <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition">
                                        Post Question
                                    </button>
                                </div>
                                
                                <div class="space-y-4">
                                    <div class="border-b border-gray-200 pb-4">
                                        <div class="flex items-start mb-2">
                                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
                                                <i class="fas fa-user text-sm"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium">Michael Chen</h4>
                                                <p class="text-xs text-gray-500">1 day ago</p>
                                            </div>
                                        </div>
                                        <p class="text-sm ml-11">Can someone explain the difference between 510(k) clearance and PMA approval for medical devices?</p>
                                        <div class="flex items-center text-xs text-gray-500 mt-2 ml-11 space-x-3">
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-reply mr-1"></i> Reply</a>
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-thumbs-up mr-1"></i> Like (5)</a>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="flex items-start mb-2">
                                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                                                <i class="fas fa-user-tie text-sm"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium">Prof. Davis</h4>
                                                <p class="text-xs text-gray-500">20 hours ago</p>
                                            </div>
                                        </div>
                                        <p class="text-sm ml-11">510(k) is for devices substantially equivalent to existing ones (lower risk), while PMA is for high-risk devices requiring clinical data. We'll cover this in Module 4.</p>
                                        <div class="flex items-center text-xs text-gray-500 mt-2 ml-11 space-x-3">
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-reply mr-1"></i> Reply</a>
                                            <a href="#" class="hover:text-blue-600"><i class="fas fa-thumbs-up mr-1"></i> Like (12)</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sidebar Resources -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Topic Resources</h2>
                            </div>
                            
                            <div class="p-6">
                                <div class="mb-4">
                                    <h3 class="font-medium mb-2">Related Materials</h3>
                                    <ul class="space-y-2">
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-file-pdf mr-2 text-red-500"></i>
                                                <span>FDA Classification Guide</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-link mr-2 text-gray-500"></i>
                                                <span>EU MDR Overview</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                                <i class="fas fa-video mr-2 text-blue-400"></i>
                                                <span>Regulatory Process Video</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h3 class="font-medium mb-2">Need Help?</h3>
                                    <p class="text-sm text-gray-600 mb-3">Stuck on this topic? Reach out to instructors or classmates for assistance.</p>
                                    <button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition">
                                        <i class="fas fa-question-circle mr-2"></i> Ask Instructor
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="border-b border-gray-200 px-6 py-4">
                                <h2 class="text-lg font-semibold">Your Progress</h2>
                            </div>
                            
                            <div class="p-6">
                                <div class="flex items-center justify-center mb-4">
                                    <div class="relative w-24 h-24">
                                        <svg class="w-full h-full" viewBox="0 0 36 36">
                                            <circle cx="18" cy="18" r="16" fill="none" stroke="#e6e6e6" stroke-width="2"></circle>
                                            <circle cx="18" cy="18" r="16" fill="none" stroke="#3b82f6" stroke-width="2" stroke-dasharray="100, 100" stroke-dashoffset="30" class="progress-ring__circle"></circle>
                                        </svg>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-xl font-bold">30%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3">
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium">Course Completion</span>
                                            <span>30%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium">Current Module</span>
                                            <span>3/5 Topics</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium">Quiz Performance</span>
                                            <span>85% Avg</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-purple-600 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Page Example (Hidden by default) -->
            <div id="profilePage" class="hidden">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-32"></div>
                    <div class="px-6 pb-6">
                        <div class="flex justify-center -mt-12 mb-4">
                            <div class="w-24 h-24 rounded-full border-4 border-white bg-blue-100 flex items-center justify-center text-blue-600 text-3xl font-bold">
                                JD
                            </div>
                        </div>
                        
                        <div class="text-center mb-6">
                            <h1 class="text-2xl font-bold">John Doe</h1>
                            <p class="text-gray-600">Medical Engineering Student</p>
                            <div class="flex justify-center mt-2">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2">Bachelor's</span>
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">3rd Year</span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-sm text-gray-500">Courses Taken</p>
                                <p class="text-xl font-bold">7</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-sm text-gray-500">Completion Rate</p>
                                <p class="text-xl font-bold">78%</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-sm text-gray-500">Quiz Average</p>
                                <p class="text-xl font-bold">86%</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg text-center">
                                <p class="text-sm text-gray-500">Active Courses</p>
                                <p class="text-xl font-bold">3</p>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4">
                            <h2 class="text-lg font-semibold mb-4">Personal Information</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                    <input type="text" value="John Doe" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Education Level</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>Diploma</option>
                                        <option selected>Bachelor's</option>
                                        <option>Master's</option>
                                        <option>PhD</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Year of Study</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>1st Year</option>
                                        <option>2nd Year</option>
                                        <option selected>3rd Year</option>
                                        <option>4th Year</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <h2 class="text-lg font-semibold mb-4">Change Password</h2>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                                        <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 flex justify-end space-x-3">
                                <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-300 transition">
                                    Cancel
                                </button>
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition">
                                    Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Active Courses -->
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="border-b border-gray-200 px-6 py-4">
                            <h2 class="text-lg font-semibold">Active Courses</h2>
                        </div>
                        
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center text-purple-600 mr-4">
                                        <i class="fas fa-heartbeat text-xl"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium">Medical Devices</h3>
                                        <p class="text-sm text-gray-500 mb-2">Introduction to medical device classification</p>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-purple-600 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-4">
                                        <i class="fas fa-x-ray text-xl"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium">Medical Imaging</h3>
                                        <p class="text-sm text-gray-500 mb-2">Principles of X-ray, MRI, Ultrasound</p>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-600 h-2 rounded-full" style="width: 72%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-lg bg-red-100 flex items-center justify-center text-red-600 mr-4">
                                        <i class="fas fa-dna text-xl"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium">Biomaterials</h3>
                                        <p class="text-sm text-gray-500 mb-2">Introduction to medical materials</p>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-600 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="mt-6 w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition">
                                View All Courses
                            </button>
                        </div>
                    </div>
                    
                    <!-- Recent Achievements -->
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                        <div class="border-b border-gray-200 px-6 py-4">
                            <h2 class="text-lg font-semibold">Recent Achievements</h2>
                        </div>
                        
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mr-4">
                                        <i class="fas fa-trophy text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Quiz Master</h3>
                                        <p class="text-sm text-gray-500">Scored 100% on 3 consecutive quizzes</p>
                                        <p class="text-xs text-gray-400 mt-1">Earned: 2 days ago</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
                                        <i class="fas fa-book text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Dedicated Learner</h3>
                                        <p class="text-sm text-gray-500">Completed 5 courses this semester</p>
                                        <p class="text-xs text-gray-400 mt-1">Earned: 1 week ago</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-4">
                                        <i class="fas fa-comments text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Community Helper</h3>
                                        <p class="text-sm text-gray-500">Answered 10 questions in forums</p>
                                        <p class="text-xs text-gray-400 mt-1">Earned: 2 weeks ago</p>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="mt-6 w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition">
                                View All Achievements
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        mobileMenuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        });
        
        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });
        
        // Profile dropdown toggle
        const profileBtn = document.getElementById('profileBtn');
        const profileDropdown = document.getElementById('profileDropdown');
        
        profileBtn.addEventListener('click', () => {
            profileDropdown.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!profileBtn.contains(e.target) && !profileDropdown.contains(e.target)) {
                profileDropdown.classList.add('hidden');
            }
        });
        
        // Toggle module content
        function toggleModule(moduleNum) {
            const content = document.getElementById(`module${moduleNum}Content`);
            const icon = document.getElementById(`module${moduleNum}Icon`);
            
            content.classList.toggle('expanded');
            icon.classList.toggle('rotate-180');
        }
        
        // Quiz option selection
        function selectOption(element, type) {
            // Remove selected class from all options in this question
            const questionDiv = element.closest('.space-y-3');
            questionDiv.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Add appropriate class to selected option
            element.classList.add('selected', type);
            
            // Update the radio button visual
            const radio = element.querySelector('.rounded-full');
            if (type === 'correct') {
                radio.innerHTML = '<i class="fas fa-check text-white text-xs"></i>';
                radio.classList.remove('border-gray-300');
                radio.classList.add('bg-green-500', 'border-green-500');
            } else if (type === 'incorrect') {
                radio.innerHTML = '<i class="fas fa-times text-white text-xs"></i>';
                radio.classList.remove('border-gray-300');
                radio.classList.add('bg-red-500', 'border-red-500');
            }
        }
        
        // Submit quiz button
        const submitQuizBtn = document.getElementById('submitQuizBtn');
        submitQuizBtn.addEventListener('click', () => {
            alert('Quiz submitted! Your results will be available shortly.');
        });
        
        // Navigation between pages (for demo purposes)
        function showPage(pageId) {
            document.querySelectorAll('main > div[id$="Page"]').forEach(page => {
                page.classList.add('hidden');
            });
            document.getElementById(pageId).classList.remove('hidden');
        }
        
        // Uncomment these to test different pages
        // showPage('coursePage');
        // showPage('topicPage');
        // showPage('profilePage');
    </script>
</body>
</html>