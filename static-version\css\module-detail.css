/* ===== MODULE DETAIL PAGE STYLES ===== */

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.breadcrumb a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.breadcrumb-separator {
    color: var(--gray-400);
    font-weight: 500;
}

/* Module Header Section */
.module-header-section {
    margin-bottom: 3rem;
}

.module-hero {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 3rem 2rem;
    border-radius: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    box-shadow: var(--shadow-xl);
}

.module-hero-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
}

.module-icon-large {
    font-size: 4rem;
    opacity: 0.9;
    animation: float 3s ease-in-out infinite;
}

.module-info {
    flex: 1;
}

.module-title-large {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.module-description-large {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.module-meta-large {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.meta-item i {
    font-size: 1rem;
}

.difficulty-badge-large {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty-badge-large.beginner {
    background: rgba(34, 197, 94, 0.2);
    color: #ffffff;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.difficulty-badge-large.intermediate {
    background: rgba(251, 191, 36, 0.2);
    color: #ffffff;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.difficulty-badge-large.advanced {
    background: rgba(239, 68, 68, 0.2);
    color: #ffffff;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.module-actions-large {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 200px;
}

.module-actions-large .btn {
    justify-content: center;
    white-space: nowrap;
}

/* Module Content Layout */
.module-content-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 3rem;
}

.module-main-content {
    min-width: 0; /* Prevent overflow */
}

/* Content Sections */
.content-section {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--gray-100);
}

/* Overview Content */
.overview-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.overview-text p {
    color: var(--gray-600);
    line-height: 1.7;
    font-size: 1rem;
}

.overview-stats {
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item-detail {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
}

.stat-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Learning Objectives */
.objectives-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.objective-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-blue);
}

.objective-item i {
    color: var(--secondary-green);
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.objective-item span {
    color: var(--gray-700);
    line-height: 1.5;
}

/* Prerequisites */
.prerequisites-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.prerequisite-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
}

.prerequisite-item i {
    color: var(--accent-orange);
    font-size: 1.25rem;
}

.prerequisite-item span {
    color: var(--gray-700);
    line-height: 1.5;
}

/* Curriculum Styles */
.curriculum-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.curriculum-section {
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    overflow: hidden;
}

.curriculum-header {
    background: var(--gray-50);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background var(--transition-fast);
}

.curriculum-header:hover {
    background: var(--gray-100);
}

.curriculum-title {
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.curriculum-meta {
    font-size: 0.875rem;
    color: var(--gray-500);
}

.curriculum-toggle {
    color: var(--gray-400);
    transition: transform var(--transition-fast);
}

.curriculum-section.expanded .curriculum-toggle {
    transform: rotate(180deg);
}

.curriculum-content {
    display: none;
    padding: 1.5rem;
    background: var(--white);
}

.curriculum-section.expanded .curriculum-content {
    display: block;
}

.curriculum-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.curriculum-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: background var(--transition-fast);
}

.curriculum-item:hover {
    background: var(--gray-50);
}

.curriculum-item-icon {
    font-size: 1.25rem;
    opacity: 0.7;
}

.curriculum-item-info {
    flex: 1;
}

.curriculum-item-title {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.curriculum-item-meta {
    font-size: 0.75rem;
    color: var(--gray-500);
    display: flex;
    gap: 1rem;
}

.curriculum-item-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.curriculum-item-status.completed {
    background: #dcfce7;
    color: #166534;
}

.curriculum-item-status.in-progress {
    background: #fef3c7;
    color: #92400e;
}

.curriculum-item-status.locked {
    background: var(--gray-100);
    color: var(--gray-500);
}

/* Sidebar Styles */
.module-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.sidebar-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-200);
}

/* Progress Card */
.progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.progress-circle {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.progress-text {
    position: absolute;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.progress-details {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.progress-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.progress-item:first-child {
    color: var(--gray-600);
}

.progress-item:last-child {
    color: var(--gray-600);
}

/* Instructor Card */
.instructor-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.instructor-avatar {
    font-size: 3rem;
    opacity: 0.8;
}

.instructor-details h4 {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.instructor-details p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.instructor-contact {
    margin-top: 0.75rem;
}

.contact-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Related Modules */
.related-modules {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.related-module-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--gray-200);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.related-module-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-blue);
    transform: translateY(-1px);
}

.related-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.related-info {
    flex: 1;
}

.related-info h5 {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.related-difficulty {
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.related-difficulty.beginner {
    background: #dcfce7;
    color: #166534;
}

.related-difficulty.intermediate {
    background: #fef3c7;
    color: #92400e;
}

.related-difficulty.advanced {
    background: #fee2e2;
    color: #991b1b;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .module-content-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .module-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .module-hero {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    
    .module-hero-content {
        flex-direction: column;
        text-align: center;
    }
    
    .module-title-large {
        font-size: 2rem;
    }
    
    .module-description-large {
        font-size: 1.125rem;
    }
    
    .module-meta-large {
        justify-content: center;
    }
    
    .module-actions-large {
        width: 100%;
        max-width: 300px;
    }
    
    .overview-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stat-grid {
        grid-template-columns: 1fr;
    }
    
    .content-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .breadcrumb {
        flex-wrap: wrap;
    }
    
    .module-hero {
        padding: 2rem 1rem;
    }
    
    .module-title-large {
        font-size: 1.75rem;
    }
    
    .module-icon-large {
        font-size: 3rem;
    }
    
    .module-meta-large {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .content-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .section-title {
        font-size: 1.25rem;
    }
    
    .sidebar-card {
        padding: 1rem;
    }
    
    .instructor-info {
        flex-direction: column;
        text-align: center;
    }
}
