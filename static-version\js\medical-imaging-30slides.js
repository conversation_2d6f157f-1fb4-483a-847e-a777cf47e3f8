/**
 * Medical Imaging 30-Slide Module JavaScript
 * BioEngage LMS - Medical Imaging Systems Interactive Presentation
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let selectedModality = 'xray';
let modalityData = {
    xray: {
        name: { en: 'X-Ray Imaging', ar: 'التصوير بالأشعة السينية' },
        description: { 
            en: 'Uses electromagnetic radiation to create images of internal structures',
            ar: 'يستخدم الإشعاع الكهرومغناطيسي لإنشاء صور للهياكل الداخلية'
        },
        specs: {
            energyRange: '10-150 keV',
            resolution: '0.1-0.5 mm',
            acquisitionTime: 'Seconds',
            penetration: 'High for soft tissue, Low for bone'
        },
        applications: [
            { en: 'Bone fractures and orthopedic conditions', ar: 'كسور العظام والحالات العظمية' },
            { en: 'Chest imaging for lung and heart conditions', ar: 'تصوير الصدر لحالات الرئة والقلب' },
            { en: 'Dental and maxillofacial imaging', ar: 'تصوير الأسنان والوجه والفكين' },
            { en: 'Mammography for breast cancer screening', ar: 'تصوير الثدي لفحص سرطان الثدي' }
        ]
    },
    ct: {
        name: { en: 'CT Scanning', ar: 'الأشعة المقطعية' },
        description: { 
            en: 'Combines multiple X-ray images to create cross-sectional views',
            ar: 'يجمع صور الأشعة السينية المتعددة لإنشاء مناظر مقطعية'
        },
        specs: {
            sliceThickness: '0.5-10 mm',
            resolution: '0.3-1.5 mm',
            acquisitionTime: 'Minutes',
            contrast: 'Excellent soft tissue contrast'
        },
        applications: [
            { en: 'Cross-sectional anatomy visualization', ar: 'تصور التشريح المقطعي' },
            { en: 'Trauma assessment and emergency diagnosis', ar: 'تقييم الصدمات والتشخيص الطارئ' },
            { en: 'Cancer detection and staging', ar: 'اكتشاف السرطان وتحديد مراحله' },
            { en: 'Vascular imaging with contrast agents', ar: 'تصوير الأوعية الدموية بعوامل التباين' }
        ]
    },
    mri: {
        name: { en: 'MRI Scanning', ar: 'التصوير بالرنين المغناطيسي' },
        description: { 
            en: 'Uses magnetic fields and radio waves to create detailed images',
            ar: 'يستخدم المجالات المغناطيسية والموجات الراديوية لإنشاء صور مفصلة'
        },
        specs: {
            magneticField: '1.5-3.0 Tesla',
            resolution: '0.5-2.0 mm',
            acquisitionTime: '15-60 minutes',
            contrast: 'Superior soft tissue contrast'
        },
        applications: [
            { en: 'Brain and neurological imaging', ar: 'تصوير الدماغ والجهاز العصبي' },
            { en: 'Musculoskeletal and joint imaging', ar: 'تصوير العضلات والهيكل العظمي والمفاصل' },
            { en: 'Cardiac and vascular imaging', ar: 'تصوير القلب والأوعية الدموية' },
            { en: 'Abdominal and pelvic organ imaging', ar: 'تصوير أعضاء البطن والحوض' }
        ]
    },
    ultrasound: {
        name: { en: 'Ultrasound Imaging', ar: 'التصوير بالموجات فوق الصوتية' },
        description: { 
            en: 'Uses high-frequency sound waves to create real-time images',
            ar: 'يستخدم الموجات الصوتية عالية التردد لإنشاء صور في الوقت الفعلي'
        },
        specs: {
            frequency: '2-15 MHz',
            resolution: '0.1-1.0 mm',
            acquisitionTime: 'Real-time',
            penetration: 'Limited by tissue depth'
        },
        applications: [
            { en: 'Obstetric and gynecological imaging', ar: 'تصوير التوليد وأمراض النساء' },
            { en: 'Cardiac echocardiography', ar: 'تخطيط صدى القلب' },
            { en: 'Abdominal organ assessment', ar: 'تقييم أعضاء البطن' },
            { en: 'Vascular Doppler studies', ar: 'دراسات دوبلر الوعائية' }
        ]
    },
    nuclear: {
        name: { en: 'Nuclear Medicine', ar: 'الطب النووي' },
        description: { 
            en: 'Uses radioactive tracers to visualize organ function and metabolism',
            ar: 'يستخدم المتتبعات المشعة لتصور وظائف الأعضاء والتمثيل الغذائي'
        },
        specs: {
            isotopes: 'Tc-99m, F-18, I-131',
            resolution: '3-15 mm',
            acquisitionTime: '30-90 minutes',
            sensitivity: 'Functional and molecular imaging'
        },
        applications: [
            { en: 'PET/CT for cancer imaging', ar: 'PET/CT لتصوير السرطان' },
            { en: 'Cardiac perfusion studies', ar: 'دراسات تروية القلب' },
            { en: 'Bone scintigraphy', ar: 'تصوير العظام بالنظائر المشعة' },
            { en: 'Thyroid and endocrine imaging', ar: 'تصوير الغدة الدرقية والغدد الصماء' }
        ]
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeMedicalImagingModule();
});

function initializeMedicalImagingModule() {
    // Initialize modality selection
    initializeModalitySelection();
    
    // Initialize interactive elements
    initializeImagingInteractions();
    
    // Set up slide-specific content
    setupImagingSlideContent();
    
    console.log('Medical imaging 30-slide module initialized successfully');
}

// ===== MODALITY SELECTION =====
function initializeModalitySelection() {
    // Set initial modality
    selectModality('xray');
    
    // Add event listeners to modality cards
    const modalityCards = document.querySelectorAll('.modality-card');
    modalityCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            if (!card.classList.contains('selected')) {
                card.style.transform = '';
            }
        });
    });
}

function selectModality(modalityName) {
    if (!modalityData[modalityName]) return;
    
    selectedModality = modalityName;
    
    // Update card states
    document.querySelectorAll('.modality-card').forEach(card => {
        card.classList.remove('selected', 'active');
    });
    
    const selectedCard = document.querySelector(`[data-modality="${modalityName}"]`);
    if (selectedCard) {
        selectedCard.classList.add('selected', 'active');
        selectedCard.style.transform = 'translateY(-8px)';
    }
    
    // Update modality information
    updateModalityInfo(modalityName);
    
    // Show notification
    const currentLang = document.documentElement.lang || 'en';
    const modalityTitle = modalityData[modalityName].name[currentLang];
    const message = currentLang === 'en' 
        ? `Selected ${modalityTitle} - Exploring imaging principles and applications`
        : `تم اختيار ${modalityTitle} - استكشاف مبادئ التصوير والتطبيقات`;
    
    showImagingNotification(message, 'info');
}

function updateModalityInfo(modalityName) {
    const modalityInfo = modalityData[modalityName];
    const currentLang = document.documentElement.lang || 'en';
    
    // Update detailed information display (if exists)
    const infoPanel = document.getElementById('modality-info-panel');
    if (infoPanel) {
        infoPanel.innerHTML = `
            <h3>${modalityInfo.name[currentLang]}</h3>
            <p>${modalityInfo.description[currentLang]}</p>
            <div class="detailed-specs">
                ${Object.entries(modalityInfo.specs).map(([key, value]) => `
                    <div class="spec-detail">
                        <span class="spec-key">${key}:</span>
                        <span class="spec-val">${value}</span>
                    </div>
                `).join('')}
            </div>
            <div class="clinical-applications">
                <h4>${currentLang === 'en' ? 'Clinical Applications:' : 'التطبيقات السريرية:'}</h4>
                <ul>
                    ${modalityInfo.applications.map(app => `
                        <li>${app[currentLang]}</li>
                    `).join('')}
                </ul>
            </div>
        `;
    }
}

// ===== INTERACTIVE ELEMENTS =====
function initializeImagingInteractions() {
    // Add click interactions to modality cards
    const modalityCards = document.querySelectorAll('.modality-card');
    modalityCards.forEach(card => {
        card.addEventListener('click', () => {
            const modality = card.dataset.modality;
            if (modality) {
                selectModality(modality);
                
                // Add loading effect
                card.classList.add('loading');
                setTimeout(() => {
                    card.classList.remove('loading');
                }, 1000);
            }
        });
    });
    
    // Add hover effects to spec rows
    const specRows = document.querySelectorAll('.spec-row');
    specRows.forEach(row => {
        row.addEventListener('click', () => {
            const specLabel = row.querySelector('.spec-label').textContent;
            const specValue = row.querySelector('.spec-value').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Technical specification: ${specLabel} ${specValue}`
                : `المواصفات التقنية: ${specLabel} ${specValue}`;
            
            showImagingNotification(message, 'info');
            
            // Highlight spec row
            row.style.background = 'rgba(5, 150, 105, 0.1)';
            setTimeout(() => {
                row.style.background = '';
            }, 1500);
        });
    });
    
    // Add interactions to application items
    const applicationItems = document.querySelectorAll('.modality-applications li');
    applicationItems.forEach(item => {
        item.addEventListener('click', () => {
            const application = item.textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Clinical application: ${application}`
                : `التطبيق السريري: ${application}`;
            
            showImagingNotification(message, 'info');
            
            // Animate application item
            item.style.transform = 'translateX(10px) scale(1.05)';
            item.style.background = 'rgba(16, 185, 129, 0.1)';
            setTimeout(() => {
                item.style.transform = '';
                item.style.background = '';
            }, 1000);
        });
    });
}

// ===== SLIDE-SPECIFIC CONTENT =====
function setupImagingSlideContent() {
    // Initialize content based on current slide
    switch(currentSlideNumber) {
        case 1:
            initializeImagingTitleSlide();
            break;
        case 2:
            initializeImagingObjectivesSlide();
            break;
        case 3:
            initializeModalitiesOverviewSlide();
            break;
        default:
            initializeStandardImagingSlide();
    }
}

function initializeImagingTitleSlide() {
    // Animate imaging modality icons
    const featureItems = document.querySelectorAll('.feature-item');
    featureItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = 'fadeInUp 0.8s ease-out forwards';
        }, 1000 + (index * 200));
    });
}

function initializeImagingObjectivesSlide() {
    // Stagger objective card animations
    const objectiveCards = document.querySelectorAll('.objective-card');
    objectiveCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.8s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 500 + (index * 200));
    });
}

function initializeModalitiesOverviewSlide() {
    // Animate modality cards
    const modalityCards = document.querySelectorAll('.modality-card');
    modalityCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(40px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.8s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 300 + (index * 150));
    });
}

function initializeStandardImagingSlide() {
    // Standard slide initialization
    console.log(`Initializing standard imaging slide ${currentSlideNumber}`);
}

// ===== UTILITY FUNCTIONS =====
function showImagingNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `imaging-notification imaging-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#059669',
        success: '#047857',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getSelectedModalityInfo() {
    return {
        modality: selectedModality,
        data: modalityData[selectedModality],
        specifications: modalityData[selectedModality].specs,
        applications: modalityData[selectedModality].applications
    };
}

// ===== GLOBAL FUNCTIONS FOR HTML ONCLICK =====
window.selectModality = selectModality;

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Medical imaging slides language changed to: ${newLang}`);
    
    // Update modality information with new language
    updateModalityInfo(selectedModality);
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        selectModality,
        updateModalityInfo,
        getSelectedModalityInfo,
        showImagingNotification,
        initializeModalitySelection,
        initializeImagingInteractions
    };
}
