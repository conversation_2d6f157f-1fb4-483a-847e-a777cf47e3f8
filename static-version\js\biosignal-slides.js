/**
 * Biosignal Processing Slides JavaScript
 * BioEngage LMS - Interactive Biosignal Processing Presentation
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let currentEEGBand = 'delta';
let eegBandData = {
    delta: {
        frequency: '0.5-4 Hz',
        amplitude: '100-200 μV',
        state: { en: 'Deep sleep', ar: 'النوم العميق' },
        description: {
            en: 'Associated with deep sleep and unconscious states. High amplitude, low frequency waves.',
            ar: 'مرتبطة بالنوم العميق والحالات اللاواعية. موجات عالية السعة ومنخفضة التردد.'
        },
        color: '#ff6b6b',
        waveform: 'M0,30 Q50,20 100,30 Q150,40 200,30 Q250,20 300,30 Q350,40 400,30'
    },
    theta: {
        frequency: '4-8 Hz',
        amplitude: '50-100 μV',
        state: { en: 'Light sleep, meditation', ar: 'النوم الخفيف، التأمل' },
        description: {
            en: 'Present during light sleep, meditation, and creative states. Associated with memory formation.',
            ar: 'موجودة أثناء النوم الخفيف والتأمل والحالات الإبداعية. مرتبطة بتكوين الذاكرة.'
        },
        color: '#ffa500',
        waveform: 'M0,30 Q25,25 50,30 Q75,35 100,30 Q125,25 150,30 Q175,35 200,30 Q225,25 250,30 Q275,35 300,30 Q325,25 350,30 Q375,35 400,30'
    },
    alpha: {
        frequency: '8-13 Hz',
        amplitude: '20-60 μV',
        state: { en: 'Relaxed wakefulness', ar: 'اليقظة المسترخية' },
        description: {
            en: 'Dominant during relaxed wakefulness with eyes closed. Indicates calm, peaceful mental state.',
            ar: 'مهيمنة أثناء اليقظة المسترخية مع إغلاق العينين. تشير إلى حالة ذهنية هادئة ومسالمة.'
        },
        color: '#00ff00',
        waveform: 'M0,30 Q20,25 40,30 Q60,35 80,30 Q100,25 120,30 Q140,35 160,30 Q180,25 200,30 Q220,35 240,30 Q260,25 280,30 Q300,35 320,30 Q340,25 360,30 Q380,35 400,30'
    },
    beta: {
        frequency: '13-30 Hz',
        amplitude: '10-30 μV',
        state: { en: 'Active concentration', ar: 'التركيز النشط' },
        description: {
            en: 'Present during active concentration, problem-solving, and focused mental activity.',
            ar: 'موجودة أثناء التركيز النشط وحل المشكلات والنشاط الذهني المركز.'
        },
        color: '#00aaff',
        waveform: 'M0,30 Q10,28 20,30 Q30,32 40,30 Q50,28 60,30 Q70,32 80,30 Q90,28 100,30 Q110,32 120,30 Q130,28 140,30 Q150,32 160,30 Q170,28 180,30 Q190,32 200,30 Q210,28 220,30 Q230,32 240,30 Q250,28 260,30 Q270,32 280,30 Q290,28 300,30 Q310,32 320,30 Q330,28 340,30 Q350,32 360,30 Q370,28 380,30 Q390,32 400,30'
    },
    gamma: {
        frequency: '30-100 Hz',
        amplitude: '5-15 μV',
        state: { en: 'High-level cognition', ar: 'الإدراك عالي المستوى' },
        description: {
            en: 'Associated with high-level cognitive functions, consciousness, and binding of information.',
            ar: 'مرتبطة بالوظائف المعرفية عالية المستوى والوعي وربط المعلومات.'
        },
        color: '#ff00ff',
        waveform: 'M0,30 Q5,29 10,30 Q15,31 20,30 Q25,29 30,30 Q35,31 40,30 Q45,29 50,30 Q55,31 60,30 Q65,29 70,30 Q75,31 80,30 Q85,29 90,30 Q95,31 100,30 Q105,29 110,30 Q115,31 120,30 Q125,29 130,30 Q135,31 140,30 Q145,29 150,30 Q155,31 160,30 Q165,29 170,30 Q175,31 180,30 Q185,29 190,30 Q195,31 200,30 Q205,29 210,30 Q215,31 220,30 Q225,29 230,30 Q235,31 240,30 Q245,29 250,30 Q255,31 260,30 Q265,29 270,30 Q275,31 280,30 Q285,29 290,30 Q295,31 300,30 Q305,29 310,30 Q315,31 320,30 Q325,29 330,30 Q335,31 340,30 Q345,29 350,30 Q355,31 360,30 Q365,29 370,30 Q375,31 380,30 Q385,29 390,30 Q395,31 400,30'
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeBiosignalSlides();
});

function initializeBiosignalSlides() {
    // Initialize EEG band selector
    initializeEEGBandSelector();
    
    // Initialize signal animations
    initializeSignalAnimations();
    
    // Set up interactive elements
    setupBiosignalInteractions();
    
    console.log('Biosignal slides initialized successfully');
}

// ===== EEG FREQUENCY BANDS =====
function initializeEEGBandSelector() {
    // Set initial band display
    updateEEGBandDisplay('delta');
    
    // Add event listeners to band buttons
    const bandButtons = document.querySelectorAll('.band-btn');
    bandButtons.forEach(button => {
        button.addEventListener('click', () => {
            const band = button.dataset.band;
            selectEEGBand(band);
        });
    });
}

function selectEEGBand(band) {
    if (!eegBandData[band]) return;
    
    currentEEGBand = band;
    
    // Update button states
    document.querySelectorAll('.band-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-band="${band}"]`).classList.add('active');
    
    // Update band display
    updateEEGBandDisplay(band);
    
    // Update EEG signals
    updateEEGSignals(band);
    
    // Show notification
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Displaying ${band.charAt(0).toUpperCase() + band.slice(1)} waves (${eegBandData[band].frequency})`
        : `عرض موجات ${band.charAt(0).toUpperCase() + band.slice(1)} (${eegBandData[band].frequency})`;
    
    showBiosignalNotification(message, 'info');
}

function updateEEGBandDisplay(band) {
    const bandData = eegBandData[band];
    const currentLang = document.documentElement.lang || 'en';
    
    // Update band title
    const bandTitle = document.getElementById('band-title');
    if (bandTitle) {
        bandTitle.textContent = `${band.charAt(0).toUpperCase() + band.slice(1)} Waves (${bandData.frequency})`;
    }
    
    // Update band description
    const bandDescription = document.getElementById('band-description');
    if (bandDescription) {
        bandDescription.textContent = bandData.description[currentLang];
    }
    
    // Update band state
    const bandState = document.getElementById('band-state');
    if (bandState) {
        bandState.textContent = bandData.state[currentLang];
    }
    
    // Update band amplitude
    const bandAmplitude = document.getElementById('band-amplitude');
    if (bandAmplitude) {
        bandAmplitude.textContent = bandData.amplitude;
    }
}

function updateEEGSignals(band) {
    const bandData = eegBandData[band];
    
    // Update all channel signals
    const channels = ['fp1-signal', 'c3-signal', 'o1-signal'];
    channels.forEach((channelId, index) => {
        const signal = document.getElementById(channelId);
        if (signal) {
            // Remove existing classes
            signal.classList.remove('delta-signal', 'theta-signal', 'alpha-signal', 'beta-signal', 'gamma-signal');
            
            // Add new class
            signal.classList.add(`${band}-signal`);
            
            // Update waveform path with slight variations for different channels
            let waveform = bandData.waveform;
            if (index === 1) {
                // Slightly different phase for C3
                waveform = waveform.replace(/Q(\d+),(\d+)/g, (match, x, y) => {
                    return `Q${x},${parseInt(y) + (index * 2)}`;
                });
            } else if (index === 2) {
                // Different amplitude for O1
                waveform = waveform.replace(/Q(\d+),(\d+)/g, (match, x, y) => {
                    return `Q${x},${parseInt(y) + (index * 3)}`;
                });
            }
            
            signal.setAttribute('d', waveform);
            
            // Trigger animation restart
            signal.style.animation = 'none';
            signal.offsetHeight; // Trigger reflow
            signal.style.animation = null;
        }
    });
}

// ===== SIGNAL ANIMATIONS =====
function initializeSignalAnimations() {
    // Start signal trace animations
    const signals = document.querySelectorAll('.ecg-waveform, .eeg-waveform, .emg-waveform');
    signals.forEach((signal, index) => {
        setTimeout(() => {
            signal.style.animationDelay = `${index * 0.5}s`;
        }, 100);
    });
}

function restartSignalAnimations() {
    const signals = document.querySelectorAll('.eeg-signal');
    signals.forEach(signal => {
        signal.style.animation = 'none';
        signal.offsetHeight; // Trigger reflow
        signal.style.animation = null;
    });
}

// ===== INTERACTIVE ELEMENTS =====
function setupBiosignalInteractions() {
    // Add hover effects to signal cards
    const signalCards = document.querySelectorAll('.signal-type-card');
    signalCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const waveform = card.querySelector('.ecg-waveform, .eeg-waveform, .emg-waveform');
            if (waveform) {
                waveform.style.animationDuration = '1s';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            const waveform = card.querySelector('.ecg-waveform, .eeg-waveform, .emg-waveform');
            if (waveform) {
                waveform.style.animationDuration = '';
            }
        });
    });
    
    // Add click interactions to channels
    const channels = document.querySelectorAll('.channel');
    channels.forEach(channel => {
        channel.addEventListener('click', () => {
            const channelLabel = channel.querySelector('.channel-label').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Analyzing channel ${channelLabel} - ${currentEEGBand} band activity`
                : `تحليل القناة ${channelLabel} - نشاط نطاق ${currentEEGBand}`;
            
            showBiosignalNotification(message, 'info');
            
            // Highlight channel temporarily
            channel.style.background = 'rgba(37, 99, 235, 0.1)';
            setTimeout(() => {
                channel.style.background = '#000';
            }, 1000);
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function showBiosignalNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `biosignal-notification biosignal-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#10b981',
        success: '#059669',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Biosignal slides language changed to: ${newLang}`);
    
    // Update EEG band display with new language
    updateEEGBandDisplay(currentEEGBand);
});

// ===== SLIDE-SPECIFIC FUNCTIONS =====
function startEEGAnalysis() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Starting real-time EEG analysis... Monitoring brain activity patterns!'
        : 'بدء تحليل تخطيط الدماغ في الوقت الفعلي... مراقبة أنماط نشاط الدماغ!';
    
    showBiosignalNotification(message, 'success');
    
    // Animate all EEG signals
    restartSignalAnimations();
    
    // Cycle through frequency bands
    const bands = ['delta', 'theta', 'alpha', 'beta', 'gamma'];
    let currentIndex = 0;
    
    const bandCycle = setInterval(() => {
        selectEEGBand(bands[currentIndex]);
        currentIndex = (currentIndex + 1) % bands.length;
        
        if (currentIndex === 0) {
            clearInterval(bandCycle);
            const completionMessage = currentLang === 'en' 
                ? 'EEG analysis complete! All frequency bands analyzed.'
                : 'اكتمل تحليل تخطيط الدماغ! تم تحليل جميع نطاقات التردد.';
            showBiosignalNotification(completionMessage, 'success');
        }
    }, 2000);
}

function analyzeEMGSignal() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Analyzing EMG signal... Detecting muscle activation patterns!'
        : 'تحليل إشارة تخطيط العضلات... اكتشاف أنماط تنشيط العضلات!';
    
    showBiosignalNotification(message, 'info');
    
    // Simulate EMG analysis
    setTimeout(() => {
        const resultMessage = currentLang === 'en' 
            ? 'EMG analysis complete: Normal muscle activity detected.'
            : 'اكتمل تحليل تخطيط العضلات: تم اكتشاف نشاط عضلي طبيعي.';
        showBiosignalNotification(resultMessage, 'success');
    }, 3000);
}

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        selectEEGBand,
        updateEEGBandDisplay,
        updateEEGSignals,
        startEEGAnalysis,
        analyzeEMGSignal,
        showBiosignalNotification
    };
}
