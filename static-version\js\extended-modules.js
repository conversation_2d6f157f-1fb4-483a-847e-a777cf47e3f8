/**
 * Extended Modules JavaScript
 * BioEngage LMS - 120+ Comprehensive Biomedical Engineering Modules
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let allModules = [];
let filteredModules = [];
let currentlyDisplayed = 0;
const modulesPerLoad = 20;
let currentFilter = 'all';
let currentCategory = 'all';
let searchQuery = '';

// ===== MODULE DATA STRUCTURE =====
const moduleCategories = {
    fundamentals: {
        icon: '🎓',
        color: '#3b82f6',
        modules: []
    },
    instrumentation: {
        icon: '🔧',
        color: '#059669',
        modules: []
    },
    imaging: {
        icon: '🏥',
        color: '#7c3aed',
        modules: []
    },
    biosignals: {
        icon: '📊',
        color: '#ea580c',
        modules: []
    },
    biomechanics: {
        icon: '🦴',
        color: '#dc2626',
        modules: []
    },
    biomaterials: {
        icon: '🧪',
        color: '#0891b2',
        modules: []
    },
    rehabilitation: {
        icon: '♿',
        color: '#7c2d12',
        modules: []
    },
    clinical: {
        icon: '🏥',
        color: '#166534',
        modules: []
    },
    regulatory: {
        icon: '📋',
        color: '#92400e',
        modules: []
    },
    research: {
        icon: '🔬',
        color: '#581c87',
        modules: []
    },
    entrepreneurship: {
        icon: '💼',
        color: '#be185d',
        modules: []
    },
    ethics: {
        icon: '⚖️',
        color: '#374151',
        modules: []
    }
};

// ===== COMPREHENSIVE MODULE DATABASE =====
const moduleDatabase = [
    // FUNDAMENTALS (15 modules)
    {
        id: 'fund-001',
        title: { en: 'Introduction to Biomedical Engineering', ar: 'مقدمة في الهندسة الطبية الحيوية' },
        description: { en: 'Overview of BME field, career paths, and interdisciplinary nature', ar: 'نظرة عامة على مجال الهندسة الطبية الحيوية والمسارات المهنية والطبيعة متعددة التخصصات' },
        category: 'fundamentals',
        difficulty: 'beginner',
        duration: { en: '3 hours', ar: '3 ساعات' },
        topics: 8,
        labs: 1,
        quizzes: 2,
        projects: 1,
        features: ['interactive', 'quiz', 'project'],
        icon: '🎓'
    },
    {
        id: 'fund-002',
        title: { en: 'Human Anatomy & Physiology for Engineers', ar: 'تشريح ووظائف الأعضاء البشرية للمهندسين' },
        description: { en: 'Essential anatomical and physiological knowledge for BME applications', ar: 'المعرفة الأساسية في التشريح ووظائف الأعضاء لتطبيقات الهندسة الطبية الحيوية' },
        category: 'fundamentals',
        difficulty: 'beginner',
        duration: { en: '6 hours', ar: '6 ساعات' },
        topics: 12,
        labs: 2,
        quizzes: 4,
        projects: 1,
        features: ['interactive', 'lab', 'quiz'],
        icon: '🫀'
    },
    {
        id: 'fund-003',
        title: { en: 'Biomedical Engineering Mathematics', ar: 'رياضيات الهندسة الطبية الحيوية' },
        description: { en: 'Mathematical foundations for biomedical engineering analysis', ar: 'الأسس الرياضية لتحليل الهندسة الطبية الحيوية' },
        category: 'fundamentals',
        difficulty: 'intermediate',
        duration: { en: '8 hours', ar: '8 ساعات' },
        topics: 15,
        labs: 3,
        quizzes: 5,
        projects: 2,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '📐'
    },
    {
        id: 'fund-004',
        title: { en: 'Biomedical Engineering Physics', ar: 'فيزياء الهندسة الطبية الحيوية' },
        description: { en: 'Physics principles applied to biological systems and medical devices', ar: 'مبادئ الفيزياء المطبقة على الأنظمة البيولوجية والأجهزة الطبية' },
        category: 'fundamentals',
        difficulty: 'intermediate',
        duration: { en: '7 hours', ar: '7 ساعات' },
        topics: 14,
        labs: 3,
        quizzes: 4,
        projects: 2,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '⚛️'
    },
    {
        id: 'fund-005',
        title: { en: 'Cell and Molecular Biology for Engineers', ar: 'بيولوجيا الخلية والجزيئات للمهندسين' },
        description: { en: 'Cellular and molecular mechanisms relevant to biomedical engineering', ar: 'الآليات الخلوية والجزيئية ذات الصلة بالهندسة الطبية الحيوية' },
        category: 'fundamentals',
        difficulty: 'intermediate',
        duration: { en: '6 hours', ar: '6 ساعات' },
        topics: 11,
        labs: 2,
        quizzes: 3,
        projects: 1,
        features: ['interactive', 'lab', 'quiz'],
        icon: '🧬'
    },

    // MEDICAL INSTRUMENTATION (18 modules)
    {
        id: 'inst-001',
        title: { en: 'ECG Signal Acquisition and Analysis', ar: 'اكتساب وتحليل إشارات تخطيط القلب' },
        description: { en: 'Comprehensive ECG technology, signal processing, and clinical interpretation', ar: 'تقنية تخطيط القلب الشاملة ومعالجة الإشارات والتفسير السريري' },
        category: 'instrumentation',
        difficulty: 'intermediate',
        duration: { en: '5 hours', ar: '5 ساعات' },
        topics: 9,
        labs: 2,
        quizzes: 3,
        projects: 1,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '💓'
    },
    {
        id: 'inst-002',
        title: { en: 'Blood Pressure Monitoring Systems', ar: 'أنظمة مراقبة ضغط الدم' },
        description: { en: 'Non-invasive and invasive blood pressure measurement techniques', ar: 'تقنيات قياس ضغط الدم غير الغازية والغازية' },
        category: 'instrumentation',
        difficulty: 'intermediate',
        duration: { en: '4 hours', ar: '4 ساعات' },
        topics: 8,
        labs: 2,
        quizzes: 2,
        projects: 1,
        features: ['interactive', 'lab', 'quiz'],
        icon: '🩺'
    },
    {
        id: 'inst-003',
        title: { en: 'Pulse Oximetry and SpO2 Monitoring', ar: 'قياس النبض وأكسجة الدم' },
        description: { en: 'Optical principles and technology for oxygen saturation monitoring', ar: 'المبادئ البصرية والتكنولوجيا لمراقبة تشبع الأكسجين' },
        category: 'instrumentation',
        difficulty: 'intermediate',
        duration: { en: '3 hours', ar: '3 ساعات' },
        topics: 6,
        labs: 1,
        quizzes: 2,
        projects: 1,
        features: ['interactive', 'lab', 'quiz'],
        icon: '🫁'
    },
    {
        id: 'inst-004',
        title: { en: 'Temperature Monitoring in Healthcare', ar: 'مراقبة درجة الحرارة في الرعاية الصحية' },
        description: { en: 'Various temperature measurement methods and clinical applications', ar: 'طرق قياس درجة الحرارة المختلفة والتطبيقات السريرية' },
        category: 'instrumentation',
        difficulty: 'beginner',
        duration: { en: '2 hours', ar: '2 ساعات' },
        topics: 5,
        labs: 1,
        quizzes: 2,
        projects: 0,
        features: ['interactive', 'lab', 'quiz'],
        icon: '🌡️'
    },
    {
        id: 'inst-005',
        title: { en: 'Defibrillators and Cardiac Pacing', ar: 'أجهزة إزالة الرجفان وتنظيم ضربات القلب' },
        description: { en: 'Electrical therapy devices for cardiac arrhythmias and heart failure', ar: 'أجهزة العلاج الكهربائي لاضطرابات النظم القلبية وفشل القلب' },
        category: 'instrumentation',
        difficulty: 'advanced',
        duration: { en: '6 hours', ar: '6 ساعات' },
        topics: 10,
        labs: 2,
        quizzes: 3,
        projects: 1,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '⚡'
    },

    // MEDICAL IMAGING (16 modules)
    {
        id: 'img-001',
        title: { en: 'X-ray Imaging Principles', ar: 'مبادئ التصوير بالأشعة السينية' },
        description: { en: 'Physics of X-ray generation, interaction with matter, and image formation', ar: 'فيزياء توليد الأشعة السينية والتفاعل مع المادة وتكوين الصور' },
        category: 'imaging',
        difficulty: 'intermediate',
        duration: { en: '5 hours', ar: '5 ساعات' },
        topics: 9,
        labs: 2,
        quizzes: 3,
        projects: 1,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '🦴'
    },
    {
        id: 'img-002',
        title: { en: 'Computed Tomography (CT) Systems', ar: 'أنظمة التصوير المقطعي المحوسب' },
        description: { en: 'CT scanner technology, reconstruction algorithms, and clinical applications', ar: 'تقنية الماسح المقطعي وخوارزميات إعادة البناء والتطبيقات السريرية' },
        category: 'imaging',
        difficulty: 'advanced',
        duration: { en: '7 hours', ar: '7 ساعات' },
        topics: 12,
        labs: 3,
        quizzes: 4,
        projects: 2,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '🔄'
    },
    {
        id: 'img-003',
        title: { en: 'Magnetic Resonance Imaging (MRI)', ar: 'التصوير بالرنين المغناطيسي' },
        description: { en: 'MRI physics, pulse sequences, and advanced imaging techniques', ar: 'فيزياء الرنين المغناطيسي وتسلسل النبضات وتقنيات التصوير المتقدمة' },
        category: 'imaging',
        difficulty: 'advanced',
        duration: { en: '8 hours', ar: '8 ساعات' },
        topics: 15,
        labs: 3,
        quizzes: 5,
        projects: 2,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '🧲'
    },
    {
        id: 'img-004',
        title: { en: 'Ultrasound Imaging Technology', ar: 'تقنية التصوير بالموجات فوق الصوتية' },
        description: { en: 'Acoustic principles, transducer technology, and Doppler imaging', ar: 'المبادئ الصوتية وتقنية المحولات والتصوير بالدوبلر' },
        category: 'imaging',
        difficulty: 'intermediate',
        duration: { en: '6 hours', ar: '6 ساعات' },
        topics: 11,
        labs: 2,
        quizzes: 3,
        projects: 1,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '🔊'
    },
    {
        id: 'img-005',
        title: { en: 'Nuclear Medicine Imaging', ar: 'التصوير في الطب النووي' },
        description: { en: 'Radiotracer imaging, SPECT, PET, and molecular imaging principles', ar: 'تصوير المتتبعات المشعة والتصوير المقطعي والتصوير الجزيئي' },
        category: 'imaging',
        difficulty: 'advanced',
        duration: { en: '7 hours', ar: '7 ساعات' },
        topics: 13,
        labs: 3,
        quizzes: 4,
        projects: 2,
        features: ['interactive', 'lab', 'quiz', 'project'],
        icon: '☢️'
    }
];

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeExtendedModules();
});

function initializeExtendedModules() {
    // Generate complete module database
    generateCompleteModuleDatabase();

    // Initialize filter controls
    initializeFilterControls();

    // Initialize search functionality
    initializeSearch();

    // Load initial modules
    loadModules();

    // Initialize load more functionality
    initializeLoadMore();

    console.log(`Extended Modules initialized with ${allModules.length} modules`);
}

// ===== MODULE DATABASE GENERATION =====
function generateCompleteModuleDatabase() {
    // Start with the predefined modules
    allModules = [...moduleDatabase];

    // Generate additional modules for each category to reach 120+
    generateAdditionalModules();

    // Shuffle modules for variety
    allModules = shuffleArray(allModules);

    // Set initial filtered modules
    filteredModules = [...allModules];
}

function generateAdditionalModules() {
    const additionalModules = [
        // More Fundamentals
        ...generateCategoryModules('fundamentals', [
            'Biostatistics and Data Analysis',
            'Engineering Ethics in Healthcare',
            'Biomedical Engineering Design Process',
            'Systems Biology and Modeling',
            'Biomedical Informatics',
            'Quality Assurance in Medical Devices',
            'Regulatory Affairs Fundamentals',
            'Healthcare Economics',
            'Patient Safety Engineering',
            'Biomedical Engineering History'
        ]),

        // More Instrumentation
        ...generateCategoryModules('instrumentation', [
            'Ventilator Technology',
            'Dialysis Machine Systems',
            'Anesthesia Delivery Systems',
            'Surgical Instruments and Robotics',
            'Patient Monitoring Networks',
            'Infusion Pump Technology',
            'Electrosurgical Units',
            'Laser Systems in Medicine',
            'Endoscopic Equipment',
            'Laboratory Automation',
            'Point-of-Care Testing Devices',
            'Wearable Health Monitors',
            'Implantable Device Technology'
        ]),

        // More Imaging
        ...generateCategoryModules('imaging', [
            'Digital Radiography',
            'Mammography Systems',
            'Fluoroscopy and Real-time Imaging',
            'Optical Coherence Tomography',
            'Photoacoustic Imaging',
            'Molecular Imaging Techniques',
            'Image-Guided Therapy',
            'Medical Image Processing',
            'PACS and Medical Informatics',
            'Radiation Safety in Imaging',
            'Quality Control in Medical Imaging'
        ]),

        // Biosignals
        ...generateCategoryModules('biosignals', [
            'EEG Signal Processing',
            'EMG and Neuromuscular Analysis',
            'EOG and Eye Movement Tracking',
            'Heart Rate Variability Analysis',
            'Respiratory Signal Processing',
            'Blood Flow Signal Analysis',
            'Neural Signal Processing',
            'Brain-Computer Interfaces',
            'Wearable Sensor Data Processing',
            'Machine Learning in Biosignals',
            'Real-time Signal Processing',
            'Artifact Removal Techniques',
            'Feature Extraction Methods',
            'Signal Quality Assessment'
        ]),

        // Biomechanics
        ...generateCategoryModules('biomechanics', [
            'Gait Analysis and Walking Biomechanics',
            'Sports Biomechanics',
            'Orthopedic Biomechanics',
            'Cardiovascular Biomechanics',
            'Respiratory Mechanics',
            'Muscle Mechanics and Modeling',
            'Joint Kinematics and Kinetics',
            'Finite Element Analysis in Biomechanics',
            'Prosthetics and Orthotics Design',
            'Balance and Postural Control',
            'Impact Biomechanics and Injury',
            'Computational Biomechanics'
        ]),

        // Biomaterials
        ...generateCategoryModules('biomaterials', [
            'Biocompatibility Testing',
            'Metallic Biomaterials',
            'Ceramic Biomaterials',
            'Polymer Biomaterials',
            'Composite Biomaterials',
            'Smart and Responsive Materials',
            'Drug Delivery Systems',
            'Tissue Engineering Scaffolds',
            'Surface Modification Techniques',
            'Sterilization and Bioburden',
            'Biodegradable Materials'
        ]),

        // Rehabilitation Engineering
        ...generateCategoryModules('rehabilitation', [
            'Assistive Technology Design',
            'Wheelchair Technology',
            'Prosthetic Limb Design',
            'Orthotic Device Development',
            'Sensory Aids and Devices',
            'Communication Aids',
            'Environmental Control Systems',
            'Cognitive Rehabilitation Technology',
            'Virtual Reality in Rehabilitation',
            'Robotics in Rehabilitation',
            'Accessibility Standards'
        ]),

        // Clinical Engineering
        ...generateCategoryModules('clinical', [
            'Hospital Equipment Management',
            'Medical Device Maintenance',
            'Technology Assessment',
            'Risk Management in Healthcare',
            'Infection Control Engineering',
            'Healthcare Facility Design',
            'Emergency Preparedness',
            'Staff Training and Education',
            'Vendor Management',
            'Cost-Benefit Analysis'
        ]),

        // Regulatory Affairs
        ...generateCategoryModules('regulatory', [
            'FDA Medical Device Regulations',
            'ISO Standards for Medical Devices',
            'Clinical Trial Design',
            'Risk Management (ISO 14971)',
            'Quality Management Systems',
            'Post-Market Surveillance',
            'International Regulatory Harmonization',
            'Software as Medical Device (SaMD)',
            'Cybersecurity in Medical Devices'
        ]),

        // Research Methods
        ...generateCategoryModules('research', [
            'Research Design and Methodology',
            'Statistical Analysis in BME',
            'Grant Writing and Funding',
            'Scientific Writing and Publication',
            'Literature Review Techniques',
            'Experimental Design',
            'Data Management and Analysis',
            'Research Ethics and IRB',
            'Intellectual Property',
            'Technology Transfer'
        ]),

        // Entrepreneurship
        ...generateCategoryModules('entrepreneurship', [
            'Medical Device Startup Fundamentals',
            'Business Plan Development',
            'Funding and Investment',
            'Market Analysis for Medical Devices',
            'Intellectual Property Strategy',
            'Regulatory Strategy for Startups',
            'Manufacturing and Scale-up',
            'Sales and Marketing in Healthcare',
            'Partnership and Licensing'
        ]),

        // Ethics and Safety
        ...generateCategoryModules('ethics', [
            'Medical Device Ethics',
            'Patient Privacy and HIPAA',
            'Informed Consent in Research',
            'Ethical Issues in AI/ML',
            'Global Health Ethics',
            'Environmental Impact of Medical Devices',
            'Social Responsibility in Engineering',
            'Professional Ethics and Conduct'
        ])
    ];

    allModules.push(...additionalModules);
}

function generateCategoryModules(category, titles) {
    return titles.map((title, index) => {
        const difficulties = ['beginner', 'intermediate', 'advanced'];
        const difficulty = difficulties[index % 3];
        const duration = difficulty === 'beginner' ? '2-3 hours' :
                        difficulty === 'intermediate' ? '4-5 hours' : '6-8 hours';

        return {
            id: `${category}-${String(index + 100).padStart(3, '0')}`,
            title: {
                en: title,
                ar: translateToArabic(title)
            },
            description: {
                en: `Comprehensive study of ${title.toLowerCase()} in biomedical engineering context`,
                ar: `دراسة شاملة لـ ${translateToArabic(title)} في سياق الهندسة الطبية الحيوية`
            },
            category: category,
            difficulty: difficulty,
            duration: { en: duration, ar: translateDuration(duration) },
            topics: Math.floor(Math.random() * 10) + 5,
            labs: Math.floor(Math.random() * 3) + 1,
            quizzes: Math.floor(Math.random() * 4) + 1,
            projects: Math.floor(Math.random() * 2),
            features: generateRandomFeatures(),
            icon: moduleCategories[category].icon
        };
    });
}

function generateRandomFeatures() {
    const allFeatures = ['interactive', 'lab', 'quiz', 'project'];
    const numFeatures = Math.floor(Math.random() * 3) + 2;
    return shuffleArray(allFeatures).slice(0, numFeatures);
}

function translateToArabic(englishText) {
    // Simple translation mapping - in a real application, use proper translation service
    const translations = {
        'Biostatistics and Data Analysis': 'الإحصاء الحيوي وتحليل البيانات',
        'Engineering Ethics in Healthcare': 'أخلاقيات الهندسة في الرعاية الصحية',
        'Biomedical Engineering Design Process': 'عملية تصميم الهندسة الطبية الحيوية',
        'Systems Biology and Modeling': 'بيولوجيا الأنظمة والنمذجة',
        'Biomedical Informatics': 'المعلوماتية الطبية الحيوية',
        'Quality Assurance in Medical Devices': 'ضمان الجودة في الأجهزة الطبية',
        'Regulatory Affairs Fundamentals': 'أساسيات الشؤون التنظيمية',
        'Healthcare Economics': 'اقتصاديات الرعاية الصحية',
        'Patient Safety Engineering': 'هندسة سلامة المرضى',
        'Biomedical Engineering History': 'تاريخ الهندسة الطبية الحيوية'
    };

    return translations[englishText] || englishText;
}

function translateDuration(duration) {
    return duration.replace('hours', 'ساعات').replace('-', '-');
}

function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// ===== FILTER CONTROLS =====
function initializeFilterControls() {
    // Difficulty filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // Update active state
            filterButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            // Update filter
            currentFilter = btn.dataset.filter;
            applyFilters();
        });
    });

    // Category filter dropdown
    const categorySelect = document.getElementById('category-select');
    if (categorySelect) {
        categorySelect.addEventListener('change', (e) => {
            currentCategory = e.target.value;
            applyFilters();
        });
    }
}

function initializeSearch() {
    const searchInput = document.getElementById('module-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce((e) => {
            searchQuery = e.target.value.toLowerCase().trim();
            applyFilters();
        }, 300));
    }
}

function applyFilters() {
    // Reset display counter
    currentlyDisplayed = 0;

    // Apply all filters
    filteredModules = allModules.filter(module => {
        // Difficulty filter
        if (currentFilter !== 'all' && module.difficulty !== currentFilter) {
            return false;
        }

        // Category filter
        if (currentCategory !== 'all' && module.category !== currentCategory) {
            return false;
        }

        // Search filter
        if (searchQuery) {
            const searchableText = `${module.title.en} ${module.title.ar} ${module.description.en} ${module.description.ar}`.toLowerCase();
            if (!searchableText.includes(searchQuery)) {
                return false;
            }
        }

        return true;
    });

    // Clear existing modules
    clearAllModuleContainers();

    // Load filtered modules
    loadModules();

    // Update stats
    updateModuleStats();
}

// ===== MODULE LOADING =====
function loadModules() {
    const modulesToLoad = filteredModules.slice(currentlyDisplayed, currentlyDisplayed + modulesPerLoad);

    if (modulesToLoad.length === 0) {
        showEmptyState();
        return;
    }

    // Group modules by category
    const modulesByCategory = {};
    modulesToLoad.forEach(module => {
        if (!modulesByCategory[module.category]) {
            modulesByCategory[module.category] = [];
        }
        modulesByCategory[module.category].push(module);
    });

    // Render modules by category
    Object.keys(modulesByCategory).forEach(category => {
        renderCategoryModules(category, modulesByCategory[category]);
    });

    currentlyDisplayed += modulesToLoad.length;

    // Update load more button
    updateLoadMoreButton();

    // Animate new modules
    animateNewModules();
}

function renderCategoryModules(category, modules) {
    let container = document.getElementById(`${category}-modules`);

    // Create category section if it doesn't exist
    if (!container) {
        createCategorySection(category);
        container = document.getElementById(`${category}-modules`);
    }

    // Show category section
    const categorySection = container.closest('.category-section');
    if (categorySection) {
        categorySection.style.display = 'block';
    }

    // Render modules
    modules.forEach(module => {
        const moduleCard = createModuleCard(module);
        container.appendChild(moduleCard);
    });
}

function createCategorySection(category) {
    const categoryInfo = moduleCategories[category];
    if (!categoryInfo) return;

    const catalogContainer = document.querySelector('.modules-catalog');

    const categorySection = document.createElement('div');
    categorySection.className = 'category-section';
    categorySection.dataset.category = category;

    categorySection.innerHTML = `
        <div class="category-header">
            <div class="category-icon">${categoryInfo.icon}</div>
            <div class="category-info">
                <h3 class="category-title" data-lang-en="${getCategoryTitle(category, 'en')}" data-lang-ar="${getCategoryTitle(category, 'ar')}">${getCategoryTitle(category, 'en')}</h3>
                <p class="category-description" data-lang-en="${getCategoryDescription(category, 'en')}" data-lang-ar="${getCategoryDescription(category, 'ar')}">${getCategoryDescription(category, 'en')}</p>
                <div class="category-stats">
                    <span class="module-count">0 modules</span>
                    <span class="difficulty-range" data-lang-en="Various levels" data-lang-ar="مستويات متنوعة">Various levels</span>
                </div>
            </div>
        </div>
        <div class="modules-grid" id="${category}-modules"></div>
    `;

    catalogContainer.appendChild(categorySection);
}

function createModuleCard(module) {
    const card = document.createElement('div');
    card.className = 'module-card-extended fade-in';
    card.dataset.moduleId = module.id;
    card.dataset.difficulty = module.difficulty;
    card.dataset.category = module.category;

    const currentLang = document.documentElement.lang || 'en';

    card.innerHTML = `
        <div class="module-header-extended">
            <div class="module-title-row">
                <div class="module-icon-extended">${module.icon}</div>
                <h4 class="module-title-extended">${module.title[currentLang]}</h4>
            </div>
            <div class="module-meta">
                <div class="module-duration">
                    <i class="fas fa-clock"></i>
                    ${module.duration[currentLang]}
                </div>
                <span class="difficulty-badge-extended ${module.difficulty}">${getDifficultyText(module.difficulty, currentLang)}</span>
            </div>
        </div>

        <div class="module-content-extended">
            <p class="module-description-extended">${module.description[currentLang]}</p>

            <div class="module-features">
                ${module.features.map(feature => `<span class="feature-tag ${feature}">${getFeatureText(feature, currentLang)}</span>`).join('')}
            </div>

            <div class="module-stats-extended">
                <div class="stat-item-extended">
                    <span class="stat-value-extended">${module.topics}</span>
                    <span class="stat-label-extended" data-lang-en="Topics" data-lang-ar="مواضيع">Topics</span>
                </div>
                <div class="stat-item-extended">
                    <span class="stat-value-extended">${module.labs}</span>
                    <span class="stat-label-extended" data-lang-en="Labs" data-lang-ar="معامل">Labs</span>
                </div>
                <div class="stat-item-extended">
                    <span class="stat-value-extended">${module.quizzes}</span>
                    <span class="stat-label-extended" data-lang-en="Quizzes" data-lang-ar="اختبارات">Quizzes</span>
                </div>
            </div>

            <div class="module-actions">
                <a href="module_detail.html?module=${module.id}" class="btn-module btn-primary-module" data-lang-en="Start Module" data-lang-ar="ابدأ الوحدة">Start Module</a>
                <button class="btn-module btn-secondary-module" onclick="previewModule('${module.id}')" data-lang-en="Preview" data-lang-ar="معاينة">Preview</button>
            </div>
        </div>
    `;

    return card;
}

// ===== UTILITY FUNCTIONS =====
function getCategoryTitle(category, lang) {
    const titles = {
        fundamentals: { en: 'Biomedical Engineering Fundamentals', ar: 'أساسيات الهندسة الطبية الحيوية' },
        instrumentation: { en: 'Medical Instrumentation & Devices', ar: 'الأجهزة والمعدات الطبية' },
        imaging: { en: 'Medical Imaging Systems', ar: 'أنظمة التصوير الطبي' },
        biosignals: { en: 'Biosignal Processing & Analysis', ar: 'معالجة وتحليل الإشارات الحيوية' },
        biomechanics: { en: 'Biomechanics & Movement Analysis', ar: 'الميكانيكا الحيوية وتحليل الحركة' },
        biomaterials: { en: 'Biomaterials & Tissue Engineering', ar: 'المواد الحيوية وهندسة الأنسجة' },
        rehabilitation: { en: 'Rehabilitation Engineering', ar: 'هندسة التأهيل' },
        clinical: { en: 'Clinical Engineering', ar: 'الهندسة السريرية' },
        regulatory: { en: 'Regulatory Affairs', ar: 'الشؤون التنظيمية' },
        research: { en: 'Research Methods', ar: 'طرق البحث' },
        entrepreneurship: { en: 'Entrepreneurship', ar: 'ريادة الأعمال' },
        ethics: { en: 'Ethics & Safety', ar: 'الأخلاق والسلامة' }
    };

    return titles[category] ? titles[category][lang] : category;
}

function getCategoryDescription(category, lang) {
    const descriptions = {
        fundamentals: {
            en: 'Essential foundation courses for biomedical engineering students',
            ar: 'الدورات الأساسية الضرورية لطلاب الهندسة الطبية الحيوية'
        },
        instrumentation: {
            en: 'Comprehensive study of medical devices, sensors, and instrumentation systems',
            ar: 'دراسة شاملة للأجهزة الطبية وأجهزة الاستشعار وأنظمة الأجهزة'
        },
        imaging: {
            en: 'Advanced imaging modalities and image processing techniques',
            ar: 'طرق التصوير المتقدمة وتقنيات معالجة الصور'
        },
        biosignals: {
            en: 'Digital signal processing for biomedical applications and physiological signals',
            ar: 'معالجة الإشارات الرقمية للتطبيقات الطبية الحيوية والإشارات الفسيولوجية'
        },
        biomechanics: {
            en: 'Mechanical principles applied to biological systems and human movement',
            ar: 'المبادئ الميكانيكية المطبقة على الأنظمة البيولوجية والحركة البشرية'
        },
        biomaterials: {
            en: 'Materials science for biomedical applications and regenerative medicine',
            ar: 'علوم المواد للتطبيقات الطبية الحيوية والطب التجديدي'
        },
        rehabilitation: {
            en: 'Assistive technologies and rehabilitation engineering solutions',
            ar: 'التقنيات المساعدة وحلول هندسة التأهيل'
        },
        clinical: {
            en: 'Healthcare technology management and clinical engineering practices',
            ar: 'إدارة تكنولوجيا الرعاية الصحية وممارسات الهندسة السريرية'
        },
        regulatory: {
            en: 'Medical device regulations, standards, and compliance requirements',
            ar: 'لوائح الأجهزة الطبية والمعايير ومتطلبات الامتثال'
        },
        research: {
            en: 'Research methodologies and scientific practices in biomedical engineering',
            ar: 'منهجيات البحث والممارسات العلمية في الهندسة الطبية الحيوية'
        },
        entrepreneurship: {
            en: 'Business development and innovation in medical technology',
            ar: 'تطوير الأعمال والابتكار في التكنولوجيا الطبية'
        },
        ethics: {
            en: 'Ethical considerations and safety practices in biomedical engineering',
            ar: 'الاعتبارات الأخلاقية وممارسات السلامة في الهندسة الطبية الحيوية'
        }
    };

    return descriptions[category] ? descriptions[category][lang] : '';
}

function getDifficultyText(difficulty, lang) {
    const texts = {
        beginner: { en: 'Beginner', ar: 'مبتدئ' },
        intermediate: { en: 'Intermediate', ar: 'متوسط' },
        advanced: { en: 'Advanced', ar: 'متقدم' }
    };

    return texts[difficulty] ? texts[difficulty][lang] : difficulty;
}

function getFeatureText(feature, lang) {
    const texts = {
        interactive: { en: 'Interactive', ar: 'تفاعلي' },
        lab: { en: 'Virtual Lab', ar: 'معمل افتراضي' },
        quiz: { en: 'Quiz', ar: 'اختبار' },
        project: { en: 'Project', ar: 'مشروع' }
    };

    return texts[feature] ? texts[feature][lang] : feature;
}

// ===== LOAD MORE FUNCTIONALITY =====
function initializeLoadMore() {
    const loadMoreBtn = document.getElementById('load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            showLoadingSpinner(loadMoreBtn);

            setTimeout(() => {
                loadModules();
                hideLoadingSpinner(loadMoreBtn);
            }, 500); // Simulate loading delay
        });
    }
}

function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('load-more-btn');
    const modulesShown = document.getElementById('modules-shown');
    const totalModules = document.getElementById('total-modules');

    if (loadMoreBtn && modulesShown && totalModules) {
        modulesShown.textContent = currentlyDisplayed;
        totalModules.textContent = filteredModules.length;

        if (currentlyDisplayed >= filteredModules.length) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'inline-flex';
        }
    }
}

function showLoadingSpinner(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading-spinner"></span> Loading...';
    button.disabled = true;
    button.dataset.originalText = originalText;
}

function hideLoadingSpinner(button) {
    button.innerHTML = button.dataset.originalText || 'Load More Modules';
    button.disabled = false;
}

// ===== UI HELPER FUNCTIONS =====
function clearAllModuleContainers() {
    const containers = document.querySelectorAll('.modules-grid');
    containers.forEach(container => {
        container.innerHTML = '';
    });

    // Hide all category sections
    const categorySections = document.querySelectorAll('.category-section');
    categorySections.forEach(section => {
        section.style.display = 'none';
    });
}

function showEmptyState() {
    const catalogContainer = document.querySelector('.modules-catalog');
    catalogContainer.innerHTML = `
        <div class="empty-state">
            <div class="empty-state-icon">🔍</div>
            <h3 class="empty-state-title" data-lang-en="No modules found" data-lang-ar="لم يتم العثور على وحدات">No modules found</h3>
            <p class="empty-state-description" data-lang-en="Try adjusting your search criteria or filters to find more modules." data-lang-ar="حاول تعديل معايير البحث أو المرشحات للعثور على المزيد من الوحدات.">Try adjusting your search criteria or filters to find more modules.</p>
        </div>
    `;

    const loadMoreSection = document.querySelector('.load-more-section');
    if (loadMoreSection) {
        loadMoreSection.style.display = 'none';
    }
}

function animateNewModules() {
    const newCards = document.querySelectorAll('.module-card-extended:not(.animated)');
    newCards.forEach((card, index) => {
        card.classList.add('animated');
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

function updateModuleStats() {
    // Update category stats
    const categorySections = document.querySelectorAll('.category-section');
    categorySections.forEach(section => {
        const category = section.dataset.category;
        const moduleCount = section.querySelectorAll('.module-card-extended').length;
        const countElement = section.querySelector('.module-count');

        if (countElement) {
            const currentLang = document.documentElement.lang || 'en';
            const moduleText = currentLang === 'en' ? 'modules' : 'وحدات';
            countElement.textContent = `${moduleCount} ${moduleText}`;
        }
    });

    // Update overall stats
    updateOverallStats();
}

function updateOverallStats() {
    const totalModulesElement = document.querySelector('.stat-card .stat-number');
    if (totalModulesElement) {
        totalModulesElement.textContent = `${allModules.length}+`;
    }
}

// ===== MODULE PREVIEW FUNCTIONALITY =====
function previewModule(moduleId) {
    const module = allModules.find(m => m.id === moduleId);
    if (!module) return;

    const currentLang = document.documentElement.lang || 'en';

    // Create modal for preview
    const modal = document.createElement('div');
    modal.className = 'module-preview-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closePreview()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>${module.title[currentLang]}</h3>
                <button onclick="closePreview()" class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="preview-icon">${module.icon}</div>
                <p class="preview-description">${module.description[currentLang]}</p>
                <div class="preview-stats">
                    <div class="preview-stat">
                        <strong>${module.topics}</strong>
                        <span data-lang-en="Topics" data-lang-ar="مواضيع">Topics</span>
                    </div>
                    <div class="preview-stat">
                        <strong>${module.labs}</strong>
                        <span data-lang-en="Virtual Labs" data-lang-ar="معامل افتراضية">Virtual Labs</span>
                    </div>
                    <div class="preview-stat">
                        <strong>${module.quizzes}</strong>
                        <span data-lang-en="Quizzes" data-lang-ar="اختبارات">Quizzes</span>
                    </div>
                    <div class="preview-stat">
                        <strong>${module.duration[currentLang]}</strong>
                        <span data-lang-en="Duration" data-lang-ar="المدة">Duration</span>
                    </div>
                </div>
                <div class="preview-features">
                    ${module.features.map(feature => `<span class="feature-tag ${feature}">${getFeatureText(feature, currentLang)}</span>`).join('')}
                </div>
            </div>
            <div class="modal-footer">
                <button onclick="closePreview()" class="btn btn-secondary" data-lang-en="Close" data-lang-ar="إغلاق">Close</button>
                <a href="module_detail.html?module=${module.id}" class="btn btn-primary" data-lang-en="Start Module" data-lang-ar="ابدأ الوحدة">Start Module</a>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add modal styles if not exists
    if (!document.querySelector('#modal-styles')) {
        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .module-preview-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease;
            }

            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
            }

            .modal-content {
                background: white;
                border-radius: 1rem;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                z-index: 1;
                animation: slideUp 0.3s ease;
            }

            .modal-header {
                padding: 1.5rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #6b7280;
                padding: 0.5rem;
                border-radius: 0.5rem;
                transition: background 0.2s;
            }

            .modal-close:hover {
                background: #f3f4f6;
            }

            .modal-body {
                padding: 1.5rem;
                text-align: center;
            }

            .preview-icon {
                font-size: 4rem;
                margin-bottom: 1rem;
            }

            .preview-description {
                color: #6b7280;
                margin-bottom: 1.5rem;
                line-height: 1.6;
            }

            .preview-stats {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
                margin-bottom: 1.5rem;
            }

            .preview-stat {
                text-align: center;
                padding: 1rem;
                background: #f9fafb;
                border-radius: 0.5rem;
            }

            .preview-stat strong {
                display: block;
                font-size: 1.25rem;
                color: #2563eb;
                margin-bottom: 0.25rem;
            }

            .preview-stat span {
                font-size: 0.75rem;
                color: #6b7280;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .preview-features {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                justify-content: center;
            }

            .modal-footer {
                padding: 1.5rem;
                border-top: 1px solid #e5e7eb;
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closePreview() {
    const modal = document.querySelector('.module-preview-modal');
    if (modal) {
        modal.remove();
    }

    // Restore body scroll
    document.body.style.overflow = '';
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;

    // Update all module cards
    const moduleCards = document.querySelectorAll('.module-card-extended');
    moduleCards.forEach(card => {
        const moduleId = card.dataset.moduleId;
        const module = allModules.find(m => m.id === moduleId);

        if (module) {
            // Update title
            const titleElement = card.querySelector('.module-title-extended');
            if (titleElement) {
                titleElement.textContent = module.title[newLang];
            }

            // Update description
            const descElement = card.querySelector('.module-description-extended');
            if (descElement) {
                descElement.textContent = module.description[newLang];
            }

            // Update duration
            const durationElement = card.querySelector('.module-duration');
            if (durationElement) {
                durationElement.innerHTML = `<i class="fas fa-clock"></i> ${module.duration[newLang]}`;
            }

            // Update difficulty badge
            const difficultyElement = card.querySelector('.difficulty-badge-extended');
            if (difficultyElement) {
                difficultyElement.textContent = getDifficultyText(module.difficulty, newLang);
            }

            // Update feature tags
            const featureTags = card.querySelectorAll('.feature-tag');
            featureTags.forEach((tag, index) => {
                if (module.features[index]) {
                    tag.textContent = getFeatureText(module.features[index], newLang);
                }
            });
        }
    });

    // Update search placeholder
    const searchInput = document.getElementById('module-search');
    if (searchInput) {
        const placeholder = newLang === 'en' ? 'Search modules...' : 'البحث في الوحدات...';
        searchInput.placeholder = placeholder;
    }

    // Update category stats
    updateModuleStats();
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        allModules,
        filteredModules,
        applyFilters,
        loadModules,
        previewModule,
        closePreview
    };
}