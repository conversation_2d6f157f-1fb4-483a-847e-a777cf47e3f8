<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VR BioMed | Virtual Reality for Biomedical Engineering Training</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .vr-headset {
            transform-style: preserve-3d;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0) rotate(-5deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #6e8efb, #a777e3);
        }
        
        .module-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .vr-viewport {
            perspective: 1000px;
        }
        
        .vr-content {
            transform: rotateX(10deg);
            transform-style: preserve-3d;
            box-shadow: 0 0 50px rgba(0, 0, 255, 0.2);
        }
    </style>
</head>
<body class="font-sans bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-10">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-indigo-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">VR BioMed</span>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-center space-x-4">
                        <a href="#home" class="text-gray-900 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Home</a>
                        <a href="#features" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Features</a>
                        <a href="#modules" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Modules</a>
                        <a href="#testimonials" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Testimonials</a>
                        <a href="#contact" class="text-gray-500 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                    </div>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-gray-500 hover:text-indigo-600 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home" class="text-gray-900 hover:text-indigo-600 block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="#features" class="text-gray-500 hover:text-indigo-600 block px-3 py-2 rounded-md text-base font-medium">Features</a>
                <a href="#modules" class="text-gray-500 hover:text-indigo-600 block px-3 py-2 rounded-md text-base font-medium">Modules</a>
                <a href="#testimonials" class="text-gray-500 hover:text-indigo-600 block px-3 py-2 rounded-md text-base font-medium">Testimonials</a>
                <a href="#contact" class="text-gray-500 hover:text-indigo-600 block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg text-white pt-24 pb-16 md:pt-32 md:pb-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex items-center justify-between">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-6">Revolutionizing Biomedical Engineering Education</h1>
                    <p class="text-xl mb-8 opacity-90">Immerse yourself in cutting-edge virtual reality training for biomedical applications. Master complex procedures in a risk-free environment.</p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <button class="bg-white text-indigo-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold text-lg shadow-lg transition duration-300">Start Free Trial</button>
                        <button class="bg-transparent border-2 border-white hover:bg-white hover:text-indigo-600 px-6 py-3 rounded-lg font-semibold text-lg shadow-lg transition duration-300">Watch Demo</button>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative vr-headset">
                        <img src="https://cdn-icons-png.flaticon.com/512/2966/2966297.png" alt="VR Headset" class="w-64 h-64 md:w-80 md:h-80">
                        <div class="absolute -bottom-6 -right-6 bg-indigo-100 rounded-full p-4 shadow-xl">
                            <i class="fas fa-heartbeat text-indigo-600 text-3xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="p-4">
                    <div class="text-indigo-600 text-4xl font-bold mb-2">98%</div>
                    <div class="text-gray-600">Retention Rate</div>
                </div>
                <div class="p-4">
                    <div class="text-indigo-600 text-4xl font-bold mb-2">250+</div>
                    <div class="text-gray-600">Training Modules</div>
                </div>
                <div class="p-4">
                    <div class="text-indigo-600 text-4xl font-bold mb-2">50K+</div>
                    <div class="text-gray-600">Students Trained</div>
                </div>
                <div class="p-4">
                    <div class="text-indigo-600 text-4xl font-bold mb-2">24/7</div>
                    <div class="text-gray-600">Accessibility</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Transformative Learning Experience</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Our VR platform provides unparalleled training opportunities for biomedical engineering students and professionals.</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition duration-300">
                    <div class="text-indigo-600 mb-4">
                        <i class="fas fa-procedures text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Surgical Simulations</h3>
                    <p class="text-gray-600">Practice complex surgical procedures with realistic haptic feedback and anatomical accuracy in a completely safe environment.</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition duration-300">
                    <div class="text-indigo-600 mb-4">
                        <i class="fas fa-microscope text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Device Prototyping</h3>
                    <p class="text-gray-600">Design, test and iterate medical devices in virtual environments before physical prototyping, saving time and resources.</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition duration-300">
                    <div class="text-indigo-600 mb-4">
                        <i class="fas fa-dna text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Cellular Visualization</h3>
                    <p class="text-gray-600">Explore biological systems at microscopic scales, manipulating molecules and cells with intuitive VR controls.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- VR Experience Section -->
    <section class="py-16 gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex items-center">
                <div class="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                    <h2 class="text-3xl font-bold mb-6">Immersive Biomedical Training</h2>
                    <p class="text-xl mb-6 opacity-90">Step into our hyper-realistic virtual environments where you can practice, make mistakes, and learn without real-world consequences.</p>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-300 mt-1 mr-3"></i>
                            <span>Real-time physiological feedback during procedures</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-300 mt-1 mr-3"></i>
                            <span>Multiplayer collaborative training sessions</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-300 mt-1 mr-3"></i>
                            <span>Detailed performance analytics and improvement suggestions</span>
                        </li>
                    </ul>
                </div>
                <div class="md:w-1/2 vr-viewport">
                    <div class="bg-black rounded-xl overflow-hidden vr-content">
                        <div class="aspect-w-16 aspect-h-9">
                            <img src="https://images.unsplash.com/photo-1581595219315-a187dd40c322?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" alt="VR Biomedical Training" class="w-full h-full object-cover">
                        </div>
                        <div class="p-4 bg-gray-900">
                            <div class="flex items-center">
                                <div class="flex-1">
                                    <h4 class="font-medium">Cardiac Surgery Simulation</h4>
                                    <p class="text-sm text-gray-400">Difficulty: Advanced</p>
                                </div>
                                <button class="bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded-lg">Enter VR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modules Section -->
    <section id="modules" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Comprehensive Training Modules</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Our curriculum covers all major areas of biomedical engineering with progressive difficulty levels.</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Module 1 -->
                <div class="module-card bg-white rounded-xl shadow-lg overflow-hidden transition duration-300">
                    <div class="h-48 bg-indigo-100 flex items-center justify-center">
                        <i class="fas fa-heart text-indigo-600 text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Cardiovascular Systems</h3>
                            <span class="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">Advanced</span>
                        </div>
                        <p class="text-gray-600 mb-4">Master the complexities of heart-lung machines, pacemakers, and vascular grafts through interactive simulations.</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <button class="text-indigo-600 hover:text-indigo-800 font-medium">Explore →</button>
                        </div>
                    </div>
                </div>
                
                <!-- Module 2 -->
                <div class="module-card bg-white rounded-xl shadow-lg overflow-hidden transition duration-300">
                    <div class="h-48 bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-brain text-blue-600 text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Neural Engineering</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">Intermediate</span>
                        </div>
                        <p class="text-gray-600 mb-4">Design and test neural interfaces, deep brain stimulators, and prosthetic control systems in virtual labs.</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 font-medium">Explore →</button>
                        </div>
                    </div>
                </div>
                
                <!-- Module 3 -->
                <div class="module-card bg-white rounded-xl shadow-lg overflow-hidden transition duration-300">
                    <div class="h-48 bg-green-100 flex items-center justify-center">
                        <i class="fas fa-bone text-green-600 text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Biomechanics</h3>
                            <span class="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded">Beginner</span>
                        </div>
                        <p class="text-gray-600 mb-4">Understand joint mechanics, gait analysis, and orthopedic implant design through interactive 3D models.</p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <button class="text-green-600 hover:text-green-800 font-medium">Explore →</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-semibold text-lg shadow-lg transition duration-300">View All Modules</button>
            </div>
        </div>
    </section>

    <!-- Training Section -->
    <section id="training" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Interactive Training Labs</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Explore our comprehensive biomedical engineering and instrumentation interactive modules.</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Biomedical Instrumentation Lab -->
                <div class="bg-indigo-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="h-40 bg-white rounded-lg flex items-center justify-center mb-4">
                        <img src="https://cdn-icons-png.flaticon.com/512/3048/3048127.png" alt="Biomedical Instruments" class="h-24">
                    </div>
                    <h3 class="text-xl font-bold mb-2">Biomedical Instrumentation Lab</h3>
                    <p class="text-gray-600 mb-4">Interactive virtual lab for ECG, EEG, EMG, and other medical instrumentation training.</p>
                    <button class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition duration-300">Start Lab</button>
                </div>
                
                <!-- Biomedical Imaging Lab -->
                <div class="bg-blue-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="h-40 bg-white rounded-lg flex items-center justify-center mb-4">
                        <img src="https://cdn-icons-png.flaticon.com/512/2966/2966321.png" alt="Medical Imaging" class="h-24">
                    </div>
                    <h3 class="text-xl font-bold mb-2">Biomedical Imaging Lab</h3>
                    <p class="text-gray-600 mb-4">Virtual training on X-ray, MRI, CT, Ultrasound imaging systems.</p>
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition duration-300">Start Lab</button>
                </div>
                
                <!-- Interactive Lectures -->
                <div class="bg-green-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="h-40 bg-white rounded-lg flex items-center justify-center mb-4">
                        <img src="https://cdn-icons-png.flaticon.com/512/3771/3771272.png" alt="Interactive Lectures" class="h-24">
                    </div>
                    <h3 class="text-xl font-bold mb-2">Interactive Lectures</h3>
                    <p class="text-gray-600 mb-4">3D animated presentations with quizzes and practical exercises.</p>
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition duration-300">View Lectures</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">What Our Users Say</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Hear from students, educators, and professionals who have transformed their biomedical engineering skills with VR.</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center text-yellow-400 mr-2">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"The VR surgical simulations gave me confidence I couldn't gain from textbooks alone. Performing my first real procedure felt familiar thanks to the hundreds of virtual repetitions."</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold">Dr. Sarah Chen</h4>
                            <p class="text-gray-500 text-sm">Cardiac Surgeon, Mayo Clinic</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center text-yellow-400 mr-2">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"As an educator, I've seen student engagement and comprehension skyrocket since implementing VR BioMed in our curriculum. Complex concepts become tangible experiences."</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold">Prof. James Wilson</h4>
                            <p class="text-gray-500 text-sm">Biomedical Engineering Chair, Stanford</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center text-yellow-400 mr-2">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"The device prototyping module saved our startup months of development time. We identified design flaws in VR that would have been costly to discover with physical prototypes."</p>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="User" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold">Lisa Rodriguez</h4>
                            <p class="text-gray-500 text-sm">CEO, NeuroTech Innovations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-6">Ready to Transform Your Biomedical Training?</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto opacity-90">Join thousands of students and professionals who are advancing their skills with immersive VR technology.</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <button class="bg-white text-indigo-600 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg shadow-lg transition duration-300">Start Free Trial</button>
                <button class="bg-transparent border-2 border-white hover:bg-white hover:text-indigo-600 px-8 py-4 rounded-lg font-semibold text-lg shadow-lg transition duration-300">Schedule Demo</button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-2 lg:gap-8">
                <div class="mb-12 lg:mb-0">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Get In Touch</h2>
                    <p class="text-gray-600 mb-8">Have questions about our VR biomedical engineering training platform? Our team is here to help.</p>
                    
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 bg-indigo-100 rounded-lg p-3">
                                <i class="fas fa-envelope text-indigo-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Email Us</h3>
                                <p class="text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 bg-indigo-100 rounded-lg p-3">
                                <i class="fas fa-phone-alt text-indigo-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Call Us</h3>
                                <p class="text-gray-600">+****************</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 bg-indigo-100 rounded-lg p-3">
                                <i class="fas fa-map-marker-alt text-indigo-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Visit Us</h3>
                                <p class="text-gray-600">123 Innovation Drive, San Francisco, CA 94107</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-8 shadow-md">
                    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Send Us a Message</h3>
                    <form>
                        <div class="mb-6">
                            <label for="name" class="block text-gray-700 font-medium mb-2">Your Name</label>
                            <input type="text" id="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="John Doe">
                        </div>
                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
                            <input type="email" id="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-6">
                            <label for="subject" class="block text-gray-700 font-medium mb-2">Subject</label>
                            <input type="text" id="subject" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="How can we help?">
                        </div>
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                            <textarea id="message" rows="4" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Your message here..."></textarea>
                        </div>
                        <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-lg transition duration-300">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">VR BioMed</h3>
                    <p class="text-gray-400">Leading the revolution in biomedical engineering education through immersive virtual reality technology.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-white">Home</a></li>
                        <li><a href="#features" class="text-gray-400 hover:text-white">Features</a></li>
                        <li><a href="#modules" class="text-gray-400 hover:text-white">Modules</a></li>
                        <li><a href="#testimonials" class="text-gray-400 hover:text-white">Testimonials</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Documentation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Tutorials</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Case Studies</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Research Papers</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Connect</h3>
                    <div class="flex space-x-4 mb-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-youtube"></i></a>
                    </div>
                    <p class="text-gray-400">Subscribe to our newsletter</p>
                    <div class="mt-2 flex">
                        <input type="email" placeholder="Your email" class="px-4 py-2 rounded-l-lg bg-gray-800 text-white focus:outline-none w-full">
                        <button class="bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded-r-lg"><i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 mb-4 md:mb-0">© 2023 VR BioMed. All rights reserved.</p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white">Terms of Service</a>
                    <a href="#" class="text-gray-400 hover:text-white">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Language toggle functionality
        document.getElementById('language-toggle').addEventListener('click', function() {
            // This would trigger your actual language switching logic
            alert('Language switching functionality would be implemented here');
            // In a real implementation, you would:
            // 1. Toggle between English/Arabic content
            // 2. Switch text-direction for RTL/LTR
            // 3. Update all text content via translation JSON
        });
        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
        
        // Add animation to module cards when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        document.querySelectorAll('.module-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>