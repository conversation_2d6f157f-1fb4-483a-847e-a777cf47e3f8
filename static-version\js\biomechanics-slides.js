/**
 * Biomechanics & Rehabilitation Slides JavaScript
 * BioEngage LMS - Interactive Biomechanics and Rehabilitation Presentation
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let gaitAnalysisActive = false;
let gaitAnimationInterval = null;
let currentGaitPhase = 'stance';
let gaitParameters = {
    stepLength: 65, // cm
    cadence: 110,   // steps/min
    walkingSpeed: 1.2, // m/s
    strideTime: 1.1    // seconds
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeBiomechanicsSlides();
});

function initializeBiomechanicsSlides() {
    // Initialize motion plane interactions
    initializeMotionPlanes();
    
    // Initialize gait analysis
    initializeGaitAnalysis();
    
    // Set up interactive elements
    setupBiomechanicsInteractions();
    
    console.log('Biomechanics slides initialized successfully');
}

// ===== MOTION PLANES INTERACTION =====
function initializeMotionPlanes() {
    const planes = document.querySelectorAll('.plane');
    planes.forEach(plane => {
        plane.addEventListener('mouseenter', () => {
            highlightMotionPlane(plane.dataset.plane);
        });
        
        plane.addEventListener('mouseleave', () => {
            resetMotionPlanes();
        });
        
        plane.addEventListener('click', () => {
            demonstrateMotionPlane(plane.dataset.plane);
        });
    });
}

function highlightMotionPlane(planeType) {
    const plane = document.querySelector(`[data-plane="${planeType}"]`);
    if (plane) {
        plane.style.opacity = '1';
        plane.style.borderWidth = '3px';
        plane.style.transform = plane.classList.contains('sagittal') ? 'translateX(-50%) scale(1.1)' :
                               plane.classList.contains('frontal') ? 'translateY(-50%) scale(1.1)' :
                               'scale(1.1)';
    }
}

function resetMotionPlanes() {
    const planes = document.querySelectorAll('.plane');
    planes.forEach(plane => {
        plane.style.opacity = '0.7';
        plane.style.borderWidth = '2px';
        plane.style.transform = plane.classList.contains('sagittal') ? 'translateX(-50%)' :
                               plane.classList.contains('frontal') ? 'translateY(-50%)' :
                               'scale(1)';
    });
}

function demonstrateMotionPlane(planeType) {
    const currentLang = document.documentElement.lang || 'en';
    const planeNames = {
        sagittal: { en: 'Sagittal Plane', ar: 'المستوى السهمي' },
        frontal: { en: 'Frontal Plane', ar: 'المستوى الجبهي' },
        transverse: { en: 'Transverse Plane', ar: 'المستوى المستعرض' }
    };
    
    const motionTypes = {
        sagittal: { en: 'Flexion/Extension movements', ar: 'حركات الثني والبسط' },
        frontal: { en: 'Abduction/Adduction movements', ar: 'حركات التبعيد والتقريب' },
        transverse: { en: 'Rotational movements', ar: 'الحركات الدورانية' }
    };
    
    const planeName = planeNames[planeType][currentLang];
    const motionType = motionTypes[planeType][currentLang];
    
    const message = currentLang === 'en' 
        ? `${planeName}: Demonstrates ${motionType} in human body`
        : `${planeName}: يوضح ${motionType} في جسم الإنسان`;
    
    showBiomechanicsNotification(message, 'info');
    
    // Highlight the corresponding motion card
    const motionCards = document.querySelectorAll('.motion-card');
    motionCards.forEach((card, index) => {
        if ((planeType === 'sagittal' && index === 0) ||
            (planeType === 'frontal' && index === 1) ||
            (planeType === 'transverse' && index === 2)) {
            card.style.background = 'rgba(59, 130, 246, 0.1)';
            card.style.borderLeftColor = '#1d4ed8';
            setTimeout(() => {
                card.style.background = '';
                card.style.borderLeftColor = '';
            }, 2000);
        }
    });
}

// ===== GAIT ANALYSIS =====
function initializeGaitAnalysis() {
    // Set initial gait parameters display
    updateGaitParametersDisplay();
    
    // Initialize gait figure
    const gaitFigure = document.getElementById('gait-figure');
    if (gaitFigure) {
        gaitFigure.style.animation = 'none';
    }
}

function startGaitAnalysis() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Starting gait analysis... Monitoring walking pattern and biomechanical parameters!'
        : 'بدء تحليل المشي... مراقبة نمط المشي والمعاملات الميكانيكية الحيوية!';
    
    showBiomechanicsNotification(message, 'success');
    
    if (!gaitAnalysisActive) {
        gaitAnalysisActive = true;
        
        // Start gait animation
        const gaitFigure = document.getElementById('gait-figure');
        if (gaitFigure) {
            gaitFigure.style.animation = 'walk-cycle 1.5s ease-in-out infinite';
        }
        
        // Start parameter updates
        startGaitParameterUpdates();
        
        // Update phase indicators
        startGaitPhaseAnimation();
    }
}

function pauseGaitAnalysis() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Gait analysis paused. Current parameters preserved.'
        : 'تم إيقاف تحليل المشي مؤقتاً. تم الحفاظ على المعاملات الحالية.';
    
    showBiomechanicsNotification(message, 'info');
    
    gaitAnalysisActive = false;
    
    // Pause animations
    const gaitFigure = document.getElementById('gait-figure');
    if (gaitFigure) {
        gaitFigure.style.animationPlayState = 'paused';
    }
    
    // Clear intervals
    if (gaitAnimationInterval) {
        clearInterval(gaitAnimationInterval);
        gaitAnimationInterval = null;
    }
}

function resetGaitAnalysis() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Gait analysis reset to initial parameters.'
        : 'تم إعادة تعيين تحليل المشي إلى المعاملات الأولية.';
    
    showBiomechanicsNotification(message, 'info');
    
    gaitAnalysisActive = false;
    currentGaitPhase = 'stance';
    
    // Reset animations
    const gaitFigure = document.getElementById('gait-figure');
    if (gaitFigure) {
        gaitFigure.style.animation = 'none';
        gaitFigure.style.animationPlayState = 'running';
    }
    
    // Clear intervals
    if (gaitAnimationInterval) {
        clearInterval(gaitAnimationInterval);
        gaitAnimationInterval = null;
    }
    
    // Reset parameters to default
    gaitParameters = {
        stepLength: 65,
        cadence: 110,
        walkingSpeed: 1.2,
        strideTime: 1.1
    };
    
    updateGaitParametersDisplay();
}

function startGaitParameterUpdates() {
    gaitAnimationInterval = setInterval(() => {
        if (gaitAnalysisActive) {
            // Simulate realistic parameter variations
            gaitParameters.stepLength = 65 + (Math.random() - 0.5) * 10;
            gaitParameters.cadence = 110 + (Math.random() - 0.5) * 20;
            gaitParameters.walkingSpeed = 1.2 + (Math.random() - 0.5) * 0.4;
            gaitParameters.strideTime = 1.1 + (Math.random() - 0.5) * 0.2;
            
            updateGaitParametersDisplay();
        }
    }, 500);
}

function startGaitPhaseAnimation() {
    const phaseInterval = setInterval(() => {
        if (!gaitAnalysisActive) {
            clearInterval(phaseInterval);
            return;
        }
        
        // Toggle between stance and swing phases
        currentGaitPhase = currentGaitPhase === 'stance' ? 'swing' : 'stance';
        
        // Update phase highlighting
        const stanceBar = document.querySelector('.stance-bar');
        const swingBar = document.querySelector('.swing-bar');
        
        if (currentGaitPhase === 'stance') {
            if (stanceBar) stanceBar.style.opacity = '1';
            if (swingBar) swingBar.style.opacity = '0.3';
        } else {
            if (stanceBar) stanceBar.style.opacity = '0.3';
            if (swingBar) swingBar.style.opacity = '1';
        }
    }, 900); // 60% stance, 40% swing timing
}

function updateGaitParametersDisplay() {
    const stepLengthEl = document.getElementById('step-length');
    const cadenceEl = document.getElementById('cadence');
    const walkingSpeedEl = document.getElementById('walking-speed');
    const strideTimeEl = document.getElementById('stride-time');
    
    if (stepLengthEl) stepLengthEl.textContent = `${gaitParameters.stepLength.toFixed(1)} cm`;
    if (cadenceEl) cadenceEl.textContent = `${gaitParameters.cadence.toFixed(0)} steps/min`;
    if (walkingSpeedEl) walkingSpeedEl.textContent = `${gaitParameters.walkingSpeed.toFixed(1)} m/s`;
    if (strideTimeEl) strideTimeEl.textContent = `${gaitParameters.strideTime.toFixed(1)} s`;
}

// ===== INTERACTIVE ELEMENTS =====
function setupBiomechanicsInteractions() {
    // Add hover effects to motion cards
    const motionCards = document.querySelectorAll('.motion-card');
    motionCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const jointDemo = card.querySelector('.joint-demo');
            if (jointDemo) {
                jointDemo.style.animationDuration = '0.5s';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            const jointDemo = card.querySelector('.joint-demo');
            if (jointDemo) {
                jointDemo.style.animationDuration = '';
            }
        });
        
        card.addEventListener('click', () => {
            const motionType = card.querySelector('h5').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Demonstrating ${motionType} - observe the joint movement pattern`
                : `عرض ${motionType} - لاحظ نمط حركة المفصل`;
            
            showBiomechanicsNotification(message, 'info');
        });
    });
    
    // Add click interaction to gait parameters
    const parameterItems = document.querySelectorAll('.parameter-item');
    parameterItems.forEach(item => {
        item.addEventListener('click', () => {
            const paramLabel = item.querySelector('.param-label').textContent;
            const paramValue = item.querySelector('.param-value').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `${paramLabel} ${paramValue} - This parameter indicates gait efficiency and biomechanical health`
                : `${paramLabel} ${paramValue} - يشير هذا المعامل إلى كفاءة المشي والصحة الميكانيكية الحيوية`;
            
            showBiomechanicsNotification(message, 'info');
            
            // Highlight parameter temporarily
            item.style.background = 'rgba(16, 185, 129, 0.1)';
            item.style.borderLeftColor = '#059669';
            setTimeout(() => {
                item.style.background = '';
                item.style.borderLeftColor = '';
            }, 1500);
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function showBiomechanicsNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `biomechanics-notification biomechanics-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#7c3aed',
        success: '#059669',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Biomechanics slides language changed to: ${newLang}`);
    
    // Update any dynamic content if needed
    updateGaitParametersDisplay();
});

// ===== SLIDE-SPECIFIC FUNCTIONS =====
function analyzeGaitPattern() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Analyzing gait pattern... Detecting asymmetries and biomechanical abnormalities!'
        : 'تحليل نمط المشي... اكتشاف عدم التماثل والشذوذات الميكانيكية الحيوية!';
    
    showBiomechanicsNotification(message, 'success');
    
    // Simulate analysis process
    setTimeout(() => {
        const resultMessage = currentLang === 'en' 
            ? 'Gait analysis complete: Normal walking pattern detected with optimal biomechanical efficiency.'
            : 'اكتمل تحليل المشي: تم اكتشاف نمط مشي طبيعي مع كفاءة ميكانيكية حيوية مثلى.';
        showBiomechanicsNotification(resultMessage, 'success');
    }, 3000);
}

function demonstrateRehabExercise() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Demonstrating rehabilitation exercise... Focus on proper biomechanical form and movement patterns!'
        : 'عرض تمرين التأهيل... ركز على الشكل الميكانيكي الحيوي الصحيح وأنماط الحركة!';
    
    showBiomechanicsNotification(message, 'info');
    
    // Enhance motion animations temporarily
    const motionCards = document.querySelectorAll('.motion-card');
    motionCards.forEach(card => {
        const jointDemo = card.querySelector('.joint-demo');
        if (jointDemo) {
            jointDemo.style.animationDuration = '0.3s';
            jointDemo.style.transform = 'scale(1.2)';
        }
    });
    
    setTimeout(() => {
        motionCards.forEach(card => {
            const jointDemo = card.querySelector('.joint-demo');
            if (jointDemo) {
                jointDemo.style.animationDuration = '';
                jointDemo.style.transform = '';
            }
        });
    }, 5000);
}

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        startGaitAnalysis,
        pauseGaitAnalysis,
        resetGaitAnalysis,
        demonstrateMotionPlane,
        analyzeGaitPattern,
        demonstrateRehabExercise,
        showBiomechanicsNotification
    };
}
