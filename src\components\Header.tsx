
import React, { useContext } from 'react';
import { <PERSON> } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

const Header: React.FC = () => {
  const { language, toggleLanguage } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'VR BioMed',
      home: 'Home',
      courses: 'Courses',
      interactiveLectures: 'Interactive Lectures',
      training: 'Training',
      virtualLab: 'Virtual Lab',
    },
    ar: {
      title: 'الواقع الافتراضي في الطب الحيوي',
      home: 'الرئيسية',
      courses: 'الدورات',
      interactiveLectures: 'محاضرات تفاعلية',
      training: 'التدريب',
      virtualLab: 'المعمل الافتراضي',
    },
  };

  return (
    <header className="bg-gray-800 text-white p-4">
      <div className="container mx-auto flex justify-between items-center">
        <h1 className="text-2xl font-bold">
          <Link to="/">{translations[language].title}</Link>
        </h1>
        <nav>
          <ul className="flex space-x-4">
            <li><Link to="/" className="hover:text-gray-300">{translations[language].home}</Link></li>
            <li><Link to="/courses" className="hover:text-gray-300">{translations[language].courses}</Link></li>
            <li><Link to="/interactive-lectures" className="hover:text-gray-300">{translations[language].interactiveLectures}</Link></li>
            <li><Link to="/training" className="hover:text-gray-300">{translations[language].training}</Link></li>
            <li><Link to="/virtual-lab" className="hover:text-gray-300">{translations[language].virtualLab}</Link></li>
          </ul>
        </nav>
        <button onClick={toggleLanguage} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          {language === 'en' ? 'العربية' : 'English'}
        </button>
      </div>
    </header>
  );
};

export default Header;
