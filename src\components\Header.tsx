
import React, { useContext, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

const Header: React.FC = () => {
  const { language, toggleLanguage } = useContext(LanguageContext);
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const translations = {
    en: {
      title: 'BioEngage',
      subtitle: 'Interactive Virtual LMS',
      home: 'Home',
      courses: 'Courses',
      interactiveLectures: 'Interactive Lectures',
      training: 'Training',
      virtualLab: 'Virtual Lab',
      menu: 'Menu',
    },
    ar: {
      title: 'بايو إنجيج',
      subtitle: 'نظام إدارة التعلم الافتراضي التفاعلي',
      home: 'الرئيسية',
      courses: 'الدورات',
      interactiveLectures: 'محاضرات تفاعلية',
      training: 'التدريب',
      virtualLab: 'المعمل الافتراضي',
      menu: 'القائمة',
    },
  };

  const navItems = [
    { path: '/', label: translations[language].home, icon: '🏠' },
    { path: '/courses', label: translations[language].courses, icon: '📚' },
    { path: '/interactive-lectures', label: translations[language].interactiveLectures, icon: '🎯' },
    { path: '/training', label: translations[language].training, icon: '🎓' },
    { path: '/virtual-lab', label: translations[language].virtualLab, icon: '🔬' },
  ];

  const isActivePath = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className={`bg-gradient-to-r from-blue-800 via-purple-800 to-blue-900 text-white shadow-lg ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-4">
        {/* Main Header */}
        <div className="flex justify-between items-center py-4">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <div className="text-3xl">🧬</div>
              <div>
                <h1 className="text-2xl font-bold">{translations[language].title}</h1>
                <p className="text-xs opacity-75">{translations[language].subtitle}</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  isActivePath(item.path)
                    ? 'bg-white bg-opacity-20 text-white font-semibold'
                    : 'hover:bg-white hover:bg-opacity-10'
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span className="text-sm font-medium">{item.label}</span>
              </Link>
            ))}
          </nav>

          {/* Language Toggle and Mobile Menu */}
          <div className="flex items-center space-x-3">
            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-bold py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span className="text-lg">{language === 'en' ? '🇸🇦' : '🇺🇸'}</span>
              <span className="text-sm">{language === 'en' ? 'العربية' : 'English'}</span>
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-lg transition-all duration-200"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center">
                <span className={`bg-white block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMobileMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'}`}></span>
                <span className={`bg-white block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}></span>
                <span className={`bg-white block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMobileMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'}`}></span>
              </div>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`lg:hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
          <nav className="py-4 border-t border-white border-opacity-20">
            <div className="space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    isActivePath(item.path)
                      ? 'bg-white bg-opacity-20 text-white font-semibold'
                      : 'hover:bg-white hover:bg-opacity-10'
                  }`}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </div>
          </nav>
        </div>
      </div>

      {/* Progress Indicator (optional) */}
      <div className="h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"></div>
    </header>
  );
};

export default Header;
