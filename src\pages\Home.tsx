
import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

const Home: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      welcome: 'Welcome to BioEngage',
      subtitle: 'Interactive Virtual Learning Management System',
      description: 'Master Biomedical Engineering & Instrumentation through immersive virtual laboratories, step-by-step training modules, and interactive learning experiences.',
      features: 'Key Features',
      getStarted: 'Get Started',
      exploreCourses: 'Explore Courses',
      virtualLabs: 'Virtual Labs',
      interactiveLectures: 'Interactive Lectures',
      stepByStep: 'Step-by-Step Training',
      bilingualSupport: 'Bilingual Support',
      featuresDesc: {
        virtualLabs: 'Practice with realistic simulations of biomedical equipment and procedures in a safe virtual environment.',
        interactiveLectures: 'Engage with dynamic presentations, embedded videos, and interactive elements for enhanced learning.',
        stepByStep: 'Follow structured learning paths with clear progression through complex biomedical engineering concepts.',
        bilingualSupport: 'Full support for English and Arabic languages with proper RTL text direction.'
      },
      stats: {
        modules: 'Learning Modules',
        labs: 'Virtual Labs',
        hours: 'Learning Hours',
        topics: 'Topics Covered'
      },
      author: 'Developed by Dr. <PERSON>goub Esmail, SUST - BME'
    },
    ar: {
      welcome: 'مرحبًا بكم في بايو إنجيج',
      subtitle: 'نظام إدارة التعلم الافتراضي التفاعلي',
      description: 'إتقان الهندسة الطبية الحيوية والأجهزة الطبية من خلال المعامل الافتراضية الغامرة ووحدات التدريب التدريجية وتجارب التعلم التفاعلية.',
      features: 'الميزات الرئيسية',
      getStarted: 'ابدأ الآن',
      exploreCourses: 'استكشف الدورات',
      virtualLabs: 'المعامل الافتراضية',
      interactiveLectures: 'المحاضرات التفاعلية',
      stepByStep: 'التدريب التدريجي',
      bilingualSupport: 'الدعم ثنائي اللغة',
      featuresDesc: {
        virtualLabs: 'تدرب مع محاكاة واقعية للمعدات والإجراءات الطبية الحيوية في بيئة افتراضية آمنة.',
        interactiveLectures: 'تفاعل مع العروض التقديمية الديناميكية والفيديوهات المدمجة والعناصر التفاعلية لتعزيز التعلم.',
        stepByStep: 'اتبع مسارات التعلم المنظمة مع التقدم الواضح عبر مفاهيم الهندسة الطبية الحيوية المعقدة.',
        bilingualSupport: 'دعم كامل للغتين الإنجليزية والعربية مع اتجاه النص الصحيح من اليمين إلى اليسار.'
      },
      stats: {
        modules: 'وحدات التعلم',
        labs: 'المعامل الافتراضية',
        hours: 'ساعات التعلم',
        topics: 'المواضيع المغطاة'
      },
      author: 'تطوير د. محمد يعقوب إسماعيل، جامعة السودان للعلوم والتكنولوجيا - الهندسة الطبية الحيوية'
    },
  };

  const features = [
    {
      icon: '🔬',
      title: translations[language].virtualLabs,
      description: translations[language].featuresDesc.virtualLabs
    },
    {
      icon: '🎯',
      title: translations[language].interactiveLectures,
      description: translations[language].featuresDesc.interactiveLectures
    },
    {
      icon: '📚',
      title: translations[language].stepByStep,
      description: translations[language].featuresDesc.stepByStep
    },
    {
      icon: '🌐',
      title: translations[language].bilingualSupport,
      description: translations[language].featuresDesc.bilingualSupport
    }
  ];

  const stats = [
    { number: '6+', label: translations[language].stats.modules },
    { number: '15+', label: translations[language].stats.labs },
    { number: '40+', label: translations[language].stats.hours },
    { number: '50+', label: translations[language].stats.topics }
  ];

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white py-20 px-6 rounded-lg mb-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl font-bold mb-4 animate-fade-in">{translations[language].welcome}</h1>
          <h2 className="text-2xl mb-6 opacity-90">{translations[language].subtitle}</h2>
          <p className="text-xl mb-8 opacity-80 leading-relaxed max-w-3xl mx-auto">
            {translations[language].description}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/courses"
              className="bg-white text-blue-600 font-bold py-4 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg"
            >
              {translations[language].getStarted}
            </Link>
            <Link
              to="/virtual-lab"
              className="border-2 border-white text-white font-bold py-4 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              {translations[language].exploreCourses}
            </Link>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
            <div className="text-gray-600 font-medium">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Features Section */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-center mb-8">{translations[language].features}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-start">
                <div className="text-4xl mr-4 mb-4">{feature.icon}</div>
                <div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-bold mb-4">
          {language === 'en' ? 'Ready to Start Learning?' : 'هل أنت مستعد لبدء التعلم؟'}
        </h3>
        <p className="text-gray-600 mb-6">
          {language === 'en'
            ? 'Join thousands of students and professionals advancing their biomedical engineering skills.'
            : 'انضم إلى آلاف الطلاب والمهنيين الذين يطورون مهاراتهم في الهندسة الطبية الحيوية.'
          }
        </p>
        <Link
          to="/courses"
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200"
        >
          {translations[language].getStarted}
        </Link>
      </div>

      {/* Author Section */}
      <div className="mt-12 text-center text-gray-600">
        <p className="text-sm">{translations[language].author}</p>
        <p className="text-xs mt-1">© 2025 - <EMAIL></p>
      </div>
    </div>
  );
};

export default Home;
