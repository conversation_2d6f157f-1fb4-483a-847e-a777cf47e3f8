
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const Home: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      welcome: 'Welcome to the Virtual Reality Biomedical Engineering Training LMS',
      description: 'An innovative platform for learning and training in biomedical engineering using virtual reality.',
    },
    ar: {
      welcome: 'مرحبًا بكم في نظام إدارة التعلم للتدريب على الهندسة الطبية الحيوية بالواقع الافتراضي',
      description: 'منصة مبتكرة للتعلم والتدريب في مجال الهندسة الطبية الحيوية باستخدام الواقع الافتراضي.',
    },
  };

  return (
    <div className="text-center">
      <h1 className="text-4xl font-bold mb-4">{translations[language].welcome}</h1>
      <p className="text-lg">{translations[language].description}</p>
    </div>
  );
};

export default Home;
