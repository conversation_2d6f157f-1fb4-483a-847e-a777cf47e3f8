<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BME LMS - Biomedical Engineering Learning Management System</title>
    <meta name="description" content="Comprehensive Learning Management System for Biomedical Engineering and Instrumentation with virtual labs, interactive lectures, and professional training programs." />
    <meta name="keywords" content="biomedical engineering, LMS, virtual lab, medical instrumentation, training, education, Dr. <PERSON>smail" />
    <meta name="author" content="Dr. <PERSON>smail" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="BME LMS - Biomedical Engineering Learning Management System" />
    <meta property="og:description" content="Master biomedical engineering through interactive virtual labs and comprehensive training programs." />
    <meta property="og:type" content="website" />
    
    <!-- Arabic Language Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      body {
        font-family: 'Inter', 'Noto Sans Arabic', sans-serif;
      }
      
      [dir="rtl"] {
        font-family: 'Noto Sans Arabic', 'Inter', sans-serif;
      }
      
      /* RTL Support */
      [dir="rtl"] .space-x-2 > * + * {
        margin-left: 0;
        margin-right: 0.5rem;
      }
      
      [dir="rtl"] .space-x-3 > * + * {
        margin-left: 0;
        margin-right: 0.75rem;
      }
      
      [dir="rtl"] .space-x-4 > * + * {
        margin-left: 0;
        margin-right: 1rem;
      }
      
      /* Animation Enhancements */
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
      
      .animate-float {
        animation: float 3s ease-in-out infinite;
      }
      
      @keyframes glow {
        0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
        50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
      }
      
      .animate-glow {
        animation: glow 2s ease-in-out infinite;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-Dwv1LCbL.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-6qNNk6xr.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>