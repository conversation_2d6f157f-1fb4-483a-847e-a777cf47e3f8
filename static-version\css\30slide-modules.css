/* ===== 30-SLIDE MODULE STYLES ===== */

/* Progress Bar and Section Markers */
.slide-progress-container {
    background: var(--white);
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.slide-progress-bar {
    position: relative;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2563eb, #3b82f6, #10b981);
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 3.33%; /* 1/30 slides */
}

.progress-sections {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}

.section-marker {
    text-align: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 2px solid transparent;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.section-marker:hover {
    background: var(--gray-100);
    border-color: #3b82f6;
    transform: translateY(-2px);
}

.section-marker.active {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    border-color: #1d4ed8;
}

.section-marker span {
    font-weight: 600;
    font-size: 0.875rem;
}

/* Enhanced Title Slide */
.title-slide .course-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
}

.stat-item i {
    color: #fbbf24;
}

/* Objectives Container */
.objectives-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.objectives-intro {
    text-align: center;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 2rem;
    border-radius: 1rem;
    border-left: 4px solid #2563eb;
}

.objectives-intro p {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--gray-700);
    margin: 0;
}

.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.objective-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.objective-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.objective-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: #2563eb;
}

.objective-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.objective-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.objective-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.objective-card li {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--gray-600);
    line-height: 1.5;
}

.objective-card li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    font-size: 1.125rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

/* Course Structure Timeline */
.course-structure {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.journey-timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.timeline-section {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border-left: 4px solid #3b82f6;
    transition: all var(--transition-fast);
}

.timeline-section:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-lg);
    border-left-color: #1d4ed8;
}

.timeline-section[data-section="introduction"] {
    border-left-color: #ef4444;
}

.timeline-section[data-section="fundamentals"] {
    border-left-color: #10b981;
}

.timeline-section[data-section="instrumentation"] {
    border-left-color: #f59e0b;
}

.timeline-section[data-section="applications"] {
    border-left-color: #8b5cf6;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.section-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.section-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    flex: 1;
}

.slide-range {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.section-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.section-content li {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    color: var(--gray-600);
    line-height: 1.4;
    transition: all var(--transition-fast);
}

.section-content li:hover {
    background: var(--gray-100);
    transform: translateX(3px);
}

.section-content li::before {
    content: '▶';
    color: #3b82f6;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

/* Module-Specific Color Schemes */
.bme-fundamentals-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.medical-imaging-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #059669, #10b981);
}

.biosignal-processing-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

.biomechanics-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

.tissue-engineering-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #ea580c, #f97316);
}

.medical-devices-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #0891b2, #06b6d4);
}

.bioinformatics-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #be185d, #ec4899);
}

.neural-engineering-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #4338ca, #6366f1);
}

/* Enhanced Animations */
@keyframes section-highlight {
    0%, 100% { 
        background: var(--white);
        transform: scale(1);
    }
    50% { 
        background: rgba(59, 130, 246, 0.05);
        transform: scale(1.02);
    }
}

.timeline-section.active {
    animation: section-highlight 2s ease-in-out infinite;
}

/* Interactive Elements */
.interactive-element {
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.interactive-element::after {
    content: '🔗';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.75rem;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.interactive-element:hover::after {
    opacity: 1;
}

.interactive-element:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Slide Navigation Enhancement */
.slide[data-section="introduction"] {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(248, 250, 252, 1));
}

.slide[data-section="fundamentals"] {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(248, 250, 252, 1));
}

.slide[data-section="instrumentation"] {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(248, 250, 252, 1));
}

.slide[data-section="applications"] {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(248, 250, 252, 1));
}

/* Responsive Design */
@media (max-width: 1024px) {
    .objectives-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .progress-sections {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .section-content ul {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .slide-progress-container {
        padding: 1rem;
    }
    
    .progress-sections {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .section-marker {
        padding: 0.5rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .objective-card {
        padding: 1.5rem;
    }
    
    .course-stats {
        gap: 1rem;
    }
    
    .stat-item {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .timeline-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .objectives-intro {
        padding: 1.5rem;
    }
    
    .objectives-intro p {
        font-size: 1rem;
    }
    
    .objective-card {
        padding: 1rem;
    }
    
    .objective-icon {
        font-size: 2rem;
    }
    
    .course-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .journey-timeline {
        gap: 1rem;
    }
    
    .timeline-section {
        padding: 1rem;
    }
    
    .section-icon {
        font-size: 1.5rem;
    }
}

/* ===== ADDITIONAL SLIDE COMPONENTS ===== */

/* History Timeline */
.history-timeline {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
}

.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #2563eb, #3b82f6, #10b981);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
}

.timeline-item:nth-child(odd) {
    text-align: right;
    padding-right: 3rem;
}

.timeline-item:nth-child(even) {
    text-align: left;
    padding-left: 3rem;
}

.timeline-marker {
    position: absolute;
    top: 0;
    width: 20px;
    height: 20px;
    background: #2563eb;
    border: 4px solid var(--white);
    border-radius: 50%;
    box-shadow: var(--shadow-md);
}

.timeline-item:nth-child(odd) .timeline-marker {
    right: -10px;
}

.timeline-item:nth-child(even) .timeline-marker {
    left: -10px;
}

.timeline-content {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    position: relative;
    transition: all var(--transition-fast);
}

.timeline-content:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 20px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -20px;
    border-left-color: var(--white);
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -20px;
    border-right-color: var(--white);
}

.timeline-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.75rem;
}

.timeline-content p {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.timeline-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.timeline-item[data-year]::after {
    content: attr(data-year);
    position: absolute;
    top: -10px;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: var(--shadow-md);
}

.timeline-item:nth-child(odd)[data-year]::after {
    right: 0;
}

.timeline-item:nth-child(even)[data-year]::after {
    left: 0;
}

/* Career Pathways */
.career-pathways {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.career-overview {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 1rem;
    padding: 2rem;
}

.career-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: #2563eb;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.career-sectors h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 2rem;
    text-align: center;
}

.sectors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.sector-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.sector-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: #10b981;
}

.sector-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.sector-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.sector-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sector-card li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--gray-600);
    line-height: 1.4;
}

.sector-card li::before {
    content: '▶';
    color: #10b981;
    font-size: 0.75rem;
    flex-shrink: 0;
}

/* Human Anatomy System */
.anatomy-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.body-systems {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border-radius: 1rem;
    padding: 2rem;
}

.human-body-diagram {
    position: relative;
    width: 300px;
    height: 400px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-radius: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.body-outline {
    position: relative;
    width: 200px;
    height: 350px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 50px 50px 20px 20px;
    opacity: 0.3;
}

.system-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transition: all var(--transition-fast);
    pointer-events: none;
}

.system-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.heart-icon {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3rem;
}

.lungs-icon {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.5rem;
}

.brain-icon {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.5rem;
}

.skeleton-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

.system-controls h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    text-align: center;
}

.system-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.system-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: 600;
    color: var(--gray-700);
}

.system-btn:hover {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.05);
    transform: translateY(-2px);
}

.system-btn.active {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #047857;
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.system-btn i {
    font-size: 1.25rem;
}

.system-info {
    background: var(--white);
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.system-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.system-info p {
    color: var(--gray-600);
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.system-specs {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
}

.spec-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.spec-value {
    font-weight: 500;
    color: #10b981;
    font-size: 0.875rem;
    background: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--gray-300);
}

/* Responsive Design for New Components */
@media (max-width: 1024px) {
    .timeline-container::before {
        left: 30px;
    }

    .timeline-item {
        text-align: left;
        padding-left: 4rem;
        padding-right: 0;
    }

    .timeline-item .timeline-marker {
        left: 20px;
        right: auto;
    }

    .timeline-content::before {
        left: -20px;
        right: auto;
        border-right-color: var(--white);
        border-left-color: transparent;
    }

    .timeline-item[data-year]::after {
        left: 0;
        right: auto;
    }

    .anatomy-overview {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .career-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .sectors-grid {
        grid-template-columns: 1fr;
    }

    .system-buttons {
        grid-template-columns: 1fr;
    }

    .human-body-diagram {
        width: 250px;
        height: 320px;
    }

    .body-outline {
        width: 150px;
        height: 280px;
    }

    .timeline-item {
        padding-left: 3rem;
    }

    .timeline-container::before {
        left: 20px;
    }

    .timeline-item .timeline-marker {
        left: 10px;
    }
}
