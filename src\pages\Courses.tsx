
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const Courses: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'Courses',
      description: 'Browse our available courses below.',
    },
    ar: {
      title: 'الدورات',
      description: 'تصفح الدورات المتاحة لدينا أدناه.',
    },
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">{translations[language].title}</h1>
      <p>{translations[language].description}</p>
      {/* Course list will be added here */}
    </div>
  );
};

export default Courses;
