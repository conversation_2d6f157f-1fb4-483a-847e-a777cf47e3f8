
import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { LanguageContext } from '../contexts/LanguageContext';

interface Module {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  duration: { en: string; ar: string };
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  topics: number;
  labs: number;
  icon: string;
}

const Courses: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'BioEngage Learning Modules',
      subtitle: 'Comprehensive Biomedical Engineering & Instrumentation Training',
      description: 'Explore our interactive modules designed for step-by-step learning in biomedical engineering.',
      duration: 'Duration',
      difficulty: 'Difficulty',
      topics: 'Topics',
      labs: 'Virtual Labs',
      startModule: 'Start Module',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
    },
    ar: {
      title: 'وحدات التعلم التفاعلية',
      subtitle: 'تدريب شامل في الهندسة الطبية الحيوية والأجهزة الطبية',
      description: 'استكشف وحداتنا التفاعلية المصممة للتعلم التدريجي في الهندسة الطبية الحيوية.',
      duration: 'المدة',
      difficulty: 'مستوى الصعوبة',
      topics: 'المواضيع',
      labs: 'المعامل الافتراضية',
      startModule: 'ابدأ الوحدة',
      beginner: 'مبتدئ',
      intermediate: 'متوسط',
      advanced: 'متقدم',
    },
  };

  const modules: Module[] = [
    {
      id: 'ecg-analysis',
      title: {
        en: 'ECG Signal Acquisition and Analysis',
        ar: 'اكتساب وتحليل إشارات تخطيط القلب'
      },
      description: {
        en: 'Learn ECG fundamentals, signal acquisition, and basic interpretation techniques.',
        ar: 'تعلم أساسيات تخطيط القلب واكتساب الإشارات وتقنيات التفسير الأساسية.'
      },
      duration: { en: '4-6 hours', ar: '4-6 ساعات' },
      difficulty: 'intermediate',
      topics: 9,
      labs: 2,
      icon: '💓'
    },
    {
      id: 'medical-imaging',
      title: {
        en: 'Medical Imaging Systems',
        ar: 'أنظمة التصوير الطبي'
      },
      description: {
        en: 'Explore X-ray, CT, MRI, and ultrasound imaging principles and instrumentation.',
        ar: 'استكشف مبادئ وأجهزة التصوير بالأشعة السينية والمقطعية والرنين المغناطيسي والموجات فوق الصوتية.'
      },
      duration: { en: '6-8 hours', ar: '6-8 ساعات' },
      difficulty: 'advanced',
      topics: 12,
      labs: 4,
      icon: '🏥'
    },
    {
      id: 'biosignals',
      title: {
        en: 'Biosignal Processing',
        ar: 'معالجة الإشارات الحيوية'
      },
      description: {
        en: 'Master digital signal processing techniques for biomedical applications.',
        ar: 'إتقان تقنيات معالجة الإشارات الرقمية للتطبيقات الطبية الحيوية.'
      },
      duration: { en: '5-7 hours', ar: '5-7 ساعات' },
      difficulty: 'intermediate',
      topics: 10,
      labs: 3,
      icon: '📊'
    },
    {
      id: 'medical-devices',
      title: {
        en: 'Medical Device Design',
        ar: 'تصميم الأجهزة الطبية'
      },
      description: {
        en: 'Learn the principles of designing safe and effective medical devices.',
        ar: 'تعلم مبادئ تصميم الأجهزة الطبية الآمنة والفعالة.'
      },
      duration: { en: '8-10 hours', ar: '8-10 ساعات' },
      difficulty: 'advanced',
      topics: 15,
      labs: 5,
      icon: '🔬'
    },
    {
      id: 'biomechanics',
      title: {
        en: 'Biomechanics and Rehabilitation',
        ar: 'الميكانيكا الحيوية والتأهيل'
      },
      description: {
        en: 'Study human movement analysis and rehabilitation engineering.',
        ar: 'دراسة تحليل الحركة البشرية وهندسة التأهيل.'
      },
      duration: { en: '6-8 hours', ar: '6-8 ساعات' },
      difficulty: 'intermediate',
      topics: 11,
      labs: 3,
      icon: '🦴'
    },
    {
      id: 'telemedicine',
      title: {
        en: 'Telemedicine and Digital Health',
        ar: 'الطب عن بُعد والصحة الرقمية'
      },
      description: {
        en: 'Explore remote healthcare technologies and digital health solutions.',
        ar: 'استكشف تقنيات الرعاية الصحية عن بُعد وحلول الصحة الرقمية.'
      },
      duration: { en: '4-5 hours', ar: '4-5 ساعات' },
      difficulty: 'beginner',
      topics: 8,
      labs: 2,
      icon: '📱'
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-12 px-6 rounded-lg mb-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4">{translations[language].title}</h1>
          <h2 className="text-xl mb-4 opacity-90">{translations[language].subtitle}</h2>
          <p className="text-lg opacity-80">{translations[language].description}</p>
        </div>
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((module) => (
          <div key={module.id} className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
            {/* Module Icon */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-center">
              <div className="text-4xl mb-2">{module.icon}</div>
              <h3 className="text-xl font-bold text-white">{module.title[language]}</h3>
            </div>

            {/* Module Content */}
            <div className="p-6">
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                {module.description[language]}
              </p>

              {/* Module Stats */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{translations[language].duration}:</span>
                  <span className="text-sm font-medium">{module.duration[language]}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{translations[language].difficulty}:</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(module.difficulty)}`}>
                    {translations[language][module.difficulty]}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{translations[language].topics}:</span>
                  <span className="text-sm font-medium">{module.topics}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{translations[language].labs}:</span>
                  <span className="text-sm font-medium">{module.labs}</span>
                </div>
              </div>

              {/* Start Module Button */}
              <Link
                to={`/module/${module.id}`}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200 text-center block"
              >
                {translations[language].startModule}
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Author Information */}
      <div className="mt-12 bg-gray-50 rounded-lg p-6 text-center">
        <h3 className="text-lg font-semibold mb-2">
          {language === 'en' ? 'Course Author' : 'مؤلف الدورة'}
        </h3>
        <p className="text-gray-700">
          <strong>Dr. Mohammed Yagoub Esmail</strong><br />
          SUST - BME, © 2025<br />
          <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
            <EMAIL>
          </a><br />
          <span className="text-sm text-gray-600">
            Phone: +249912867327, +966538076790
          </span>
        </p>
      </div>
    </div>
  );
};

export default Courses;
