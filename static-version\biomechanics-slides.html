<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-en="Biomechanics & Rehabilitation - Interactive Slides" data-lang-ar="الميكانيكا الحيوية والتأهيل - شرائح تفاعلية">Biomechanics & Rehabilitation - Interactive Slides</title>
    <meta name="description" content="Interactive slide deck for biomechanics and rehabilitation engineering education">
    <meta name="keywords" content="biomechanics, rehabilitation, gait analysis, motion capture, biomedical engineering">
    <meta name="author" content="Dr<PERSON>, SUST - BME">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦴</text></svg>">
    
    <!-- Fonts for bilingual support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slide-deck.css">
    <link rel="stylesheet" href="css/biomechanics-slides.css">
</head>
<body class="biomechanics-slides-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <!-- Logo and Title -->
                <div class="logo-section">
                    <div class="logo-icon">🧬</div>
                    <div class="logo-text">
                        <h1 data-lang-en="BioEngage" data-lang-ar="بايو إنجيج">BioEngage</h1>
                        <p class="subtitle" data-lang-en="Interactive Virtual LMS" data-lang-ar="نظام إدارة التعلم الافتراضي التفاعلي">Interactive Virtual LMS</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="nav-desktop">
                    <ul class="nav-list">
                        <li><a href="index.html" class="nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                        <li><a href="modules.html" class="nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                        <li><a href="extended-modules.html" class="nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                        <li><a href="interactive_lectures.html" class="nav-link active" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                        <li><a href="training.html" class="nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                        <li><a href="virtual_lab.html" class="nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                    </ul>
                </nav>

                <!-- Language Toggle and Mobile Menu -->
                <div class="header-controls">
                    <button id="lang-toggle" class="lang-toggle" data-lang="en">
                        <span class="flag">🇸🇦</span>
                        <span class="lang-text" data-lang-en="العربية" data-lang-ar="English">العربية</span>
                    </button>
                    <button id="mobile-menu-toggle" class="mobile-menu-toggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <nav id="mobile-nav" class="nav-mobile">
                <ul class="mobile-nav-list">
                    <li><a href="index.html" class="mobile-nav-link" data-lang-en="🏠 Home" data-lang-ar="🏠 الرئيسية">🏠 Home</a></li>
                    <li><a href="modules.html" class="mobile-nav-link" data-lang-en="📚 Modules" data-lang-ar="📚 الوحدات">📚 Modules</a></li>
                    <li><a href="extended-modules.html" class="mobile-nav-link" data-lang-en="📖 All Modules" data-lang-ar="📖 جميع الوحدات">📖 All Modules</a></li>
                    <li><a href="interactive_lectures.html" class="mobile-nav-link" data-lang-en="🎯 Interactive Lectures" data-lang-ar="🎯 محاضرات تفاعلية">🎯 Interactive Lectures</a></li>
                    <li><a href="training.html" class="mobile-nav-link" data-lang-en="🎓 Training" data-lang-ar="🎓 التدريب">🎓 Training</a></li>
                    <li><a href="virtual_lab.html" class="mobile-nav-link" data-lang-en="🔬 Virtual Lab" data-lang-ar="🔬 المعمل الافتراضي">🔬 Virtual Lab</a></li>
                </ul>
            </nav>
        </div>
        <div class="header-progress"></div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Slide Deck Container -->
            <div class="slide-deck-container">
                <!-- Slide Deck Header -->
                <div class="slide-deck-header">
                    <div class="deck-info">
                        <h1 class="deck-title" data-lang-en="Biomechanics & Rehabilitation Engineering" data-lang-ar="الميكانيكا الحيوية وهندسة التأهيل">Biomechanics & Rehabilitation Engineering</h1>
                        <p class="deck-subtitle" data-lang-en="Motion Analysis, Gait Studies, and Assistive Technology Design" data-lang-ar="تحليل الحركة ودراسات المشي وتصميم التكنولوجيا المساعدة">Motion Analysis, Gait Studies, and Assistive Technology Design</p>
                    </div>
                    
                    <div class="deck-controls">
                        <div class="slide-counter">
                            <span id="current-slide">1</span> / <span id="total-slides">10</span>
                        </div>
                        <div class="control-buttons">
                            <button id="prev-slide" class="control-btn" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="play-pause" class="control-btn">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="next-slide" class="control-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        <button id="fullscreen-btn" class="control-btn">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>

                <!-- Slide Container -->
                <div class="slide-container" id="slide-container">
                    <!-- Slide 1: Title Slide -->
                    <div class="slide active" data-slide="1">
                        <div class="slide-content title-slide">
                            <div class="title-animation">
                                <div class="animated-icon bounce-animation">🦴</div>
                                <h1 data-lang-en="Biomechanics & Rehabilitation Engineering" data-lang-ar="الميكانيكا الحيوية وهندسة التأهيل">Biomechanics & Rehabilitation Engineering</h1>
                                <h2 data-lang-en="Motion Analysis, Gait Studies, and Assistive Technology" data-lang-ar="تحليل الحركة ودراسات المشي والتكنولوجيا المساعدة">Motion Analysis, Gait Studies, and Assistive Technology</h2>
                            </div>
                            <div class="title-features">
                                <div class="feature-item fade-in-up" data-delay="0.2s">
                                    <div class="feature-icon walk-animation">🚶</div>
                                    <span data-lang-en="Gait Analysis" data-lang-ar="تحليل المشي">Gait Analysis</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.4s">
                                    <div class="feature-icon rotate-animation">⚙️</div>
                                    <span data-lang-en="Motion Capture" data-lang-ar="التقاط الحركة">Motion Capture</span>
                                </div>
                                <div class="feature-item fade-in-up" data-delay="0.6s">
                                    <div class="feature-icon pulse-animation">🦾</div>
                                    <span data-lang-en="Prosthetics Design" data-lang-ar="تصميم الأطراف الصناعية">Prosthetics Design</span>
                                </div>
                            </div>
                            <div class="author-info fade-in" data-delay="0.8s">
                                <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                                <p data-lang-en="SUST - Biomedical Engineering Department" data-lang-ar="جامعة السودان للعلوم والتكنولوجيا - قسم الهندسة الطبية الحيوية">SUST - Biomedical Engineering Department</p>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2: Human Motion Fundamentals -->
                    <div class="slide" data-slide="2">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Human Motion Fundamentals" data-lang-ar="أساسيات الحركة البشرية">Human Motion Fundamentals</h2>
                                <div class="slide-icon walk-animation">🚶</div>
                            </div>
                            <div class="motion-fundamentals">
                                <div class="motion-overview">
                                    <div class="motion-planes">
                                        <h4 data-lang-en="Anatomical Planes of Motion" data-lang-ar="المستويات التشريحية للحركة">Anatomical Planes of Motion</h4>
                                        <div class="planes-visualization">
                                            <div class="human-figure">
                                                <div class="body-outline">
                                                    <div class="head"></div>
                                                    <div class="torso"></div>
                                                    <div class="arm left-arm"></div>
                                                    <div class="arm right-arm"></div>
                                                    <div class="leg left-leg"></div>
                                                    <div class="leg right-leg"></div>
                                                </div>
                                                <div class="motion-planes-overlay">
                                                    <div class="plane sagittal" data-plane="sagittal">
                                                        <span class="plane-label" data-lang-en="Sagittal" data-lang-ar="السهمي">Sagittal</span>
                                                    </div>
                                                    <div class="plane frontal" data-plane="frontal">
                                                        <span class="plane-label" data-lang-en="Frontal" data-lang-ar="الجبهي">Frontal</span>
                                                    </div>
                                                    <div class="plane transverse" data-plane="transverse">
                                                        <span class="plane-label" data-lang-en="Transverse" data-lang-ar="المستعرض">Transverse</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="motion-types">
                                        <h4 data-lang-en="Types of Motion" data-lang-ar="أنواع الحركة">Types of Motion</h4>
                                        <div class="motion-cards">
                                            <div class="motion-card animated-card hover-lift" data-delay="0.1s">
                                                <div class="motion-icon">↕️</div>
                                                <h5 data-lang-en="Flexion/Extension" data-lang-ar="الثني/البسط">Flexion/Extension</h5>
                                                <p data-lang-en="Movement in sagittal plane" data-lang-ar="الحركة في المستوى السهمي">Movement in sagittal plane</p>
                                                <div class="motion-demo">
                                                    <div class="joint-demo flexion-extension"></div>
                                                </div>
                                            </div>
                                            <div class="motion-card animated-card hover-lift" data-delay="0.2s">
                                                <div class="motion-icon">↔️</div>
                                                <h5 data-lang-en="Abduction/Adduction" data-lang-ar="التبعيد/التقريب">Abduction/Adduction</h5>
                                                <p data-lang-en="Movement in frontal plane" data-lang-ar="الحركة في المستوى الجبهي">Movement in frontal plane</p>
                                                <div class="motion-demo">
                                                    <div class="joint-demo abduction-adduction"></div>
                                                </div>
                                            </div>
                                            <div class="motion-card animated-card hover-lift" data-delay="0.3s">
                                                <div class="motion-icon">🔄</div>
                                                <h5 data-lang-en="Rotation" data-lang-ar="الدوران">Rotation</h5>
                                                <p data-lang-en="Movement in transverse plane" data-lang-ar="الحركة في المستوى المستعرض">Movement in transverse plane</p>
                                                <div class="motion-demo">
                                                    <div class="joint-demo rotation"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3: Gait Analysis Interactive -->
                    <div class="slide" data-slide="3">
                        <div class="slide-content">
                            <div class="slide-header">
                                <h2 data-lang-en="Interactive Gait Analysis" data-lang-ar="تحليل المشي التفاعلي">Interactive Gait Analysis</h2>
                                <div class="slide-icon walk-animation">👣</div>
                            </div>
                            <div class="gait-analysis">
                                <div class="gait-cycle">
                                    <h4 data-lang-en="Gait Cycle Phases" data-lang-ar="مراحل دورة المشي">Gait Cycle Phases</h4>
                                    <div class="gait-visualization">
                                        <div class="gait-timeline">
                                            <div class="timeline-track">
                                                <div class="phase-marker stance" data-phase="stance">
                                                    <span class="phase-label" data-lang-en="Stance Phase (60%)" data-lang-ar="مرحلة الوقوف (60%)">Stance Phase (60%)</span>
                                                    <div class="phase-bar stance-bar"></div>
                                                </div>
                                                <div class="phase-marker swing" data-phase="swing">
                                                    <span class="phase-label" data-lang-en="Swing Phase (40%)" data-lang-ar="مرحلة التأرجح (40%)">Swing Phase (40%)</span>
                                                    <div class="phase-bar swing-bar"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="walking-figure">
                                            <div class="figure-container">
                                                <div class="stick-figure" id="gait-figure">
                                                    <div class="head"></div>
                                                    <div class="body"></div>
                                                    <div class="arm left-arm"></div>
                                                    <div class="arm right-arm"></div>
                                                    <div class="leg left-leg">
                                                        <div class="thigh"></div>
                                                        <div class="shin"></div>
                                                        <div class="foot"></div>
                                                    </div>
                                                    <div class="leg right-leg">
                                                        <div class="thigh"></div>
                                                        <div class="shin"></div>
                                                        <div class="foot"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="gait-controls">
                                        <button class="gait-btn" onclick="startGaitAnalysis()">
                                            <i class="fas fa-play"></i>
                                            <span data-lang-en="Start Gait Analysis" data-lang-ar="ابدأ تحليل المشي">Start Gait Analysis</span>
                                        </button>
                                        <button class="gait-btn" onclick="pauseGaitAnalysis()">
                                            <i class="fas fa-pause"></i>
                                            <span data-lang-en="Pause" data-lang-ar="إيقاف مؤقت">Pause</span>
                                        </button>
                                        <button class="gait-btn" onclick="resetGaitAnalysis()">
                                            <i class="fas fa-redo"></i>
                                            <span data-lang-en="Reset" data-lang-ar="إعادة تعيين">Reset</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="gait-parameters">
                                    <h4 data-lang-en="Gait Parameters" data-lang-ar="معاملات المشي">Gait Parameters</h4>
                                    <div class="parameter-display">
                                        <div class="parameter-item">
                                            <span class="param-label" data-lang-en="Step Length:" data-lang-ar="طول الخطوة:">Step Length:</span>
                                            <span class="param-value" id="step-length">65 cm</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label" data-lang-en="Cadence:" data-lang-ar="الإيقاع:">Cadence:</span>
                                            <span class="param-value" id="cadence">110 steps/min</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label" data-lang-en="Walking Speed:" data-lang-ar="سرعة المشي:">Walking Speed:</span>
                                            <span class="param-value" id="walking-speed">1.2 m/s</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label" data-lang-en="Stride Time:" data-lang-ar="زمن الخطوة:">Stride Time:</span>
                                            <span class="param-value" id="stride-time">1.1 s</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script src="js/slide-deck.js"></script>
    <script src="js/biomechanics-slides.js"></script>
</body>
</html>
