/**
 * ECG Module JavaScript
 * BioEngage LMS - ECG Signal Acquisition and Analysis Module
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let currentSection = null;
let currentTab = 'sections';
let moduleProgress = {
    sectionsCompleted: 0,
    totalSections: 5,
    labCompleted: false,
    quizCompleted: false,
    currentStep: 1
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeECGModule();
});

function initializeECGModule() {
    // Initialize tab navigation
    initializeTabNavigation();
    
    // Initialize section toggles
    initializeSectionToggles();
    
    // Initialize progress tracking
    initializeProgressTracking();
    
    // Set up interactive elements
    setupInteractiveElements();
    
    console.log('ECG Module initialized successfully');
}

// ===== TAB NAVIGATION =====
function initializeTabNavigation() {
    const tabButtons = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetSection = button.dataset.section;
            switchTab(targetSection);
        });
    });
}

function switchTab(targetSection) {
    // Update active tab button
    document.querySelectorAll('.nav-tab').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-section="${targetSection}"]`).classList.add('active');
    
    // Update active tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${targetSection}-content`).classList.add('active');
    
    currentTab = targetSection;
    
    // Track tab switching
    trackUserActivity('tab_switch', { tab: targetSection });
}

// ===== SECTION MANAGEMENT =====
function initializeSectionToggles() {
    // Expand first section by default
    const firstSection = document.querySelector('.section-card[data-section="1"]');
    if (firstSection) {
        firstSection.classList.add('expanded');
    }
}

function toggleSection(sectionNumber) {
    const sectionCard = document.querySelector(`.section-card[data-section="${sectionNumber}"]`);
    if (!sectionCard) return;
    
    const isExpanded = sectionCard.classList.contains('expanded');
    
    if (isExpanded) {
        // Collapse section
        sectionCard.classList.remove('expanded');
        currentSection = null;
    } else {
        // Collapse all other sections
        document.querySelectorAll('.section-card').forEach(card => {
            card.classList.remove('expanded');
        });
        
        // Expand clicked section
        sectionCard.classList.add('expanded');
        currentSection = sectionNumber;
        
        // Scroll to section
        sectionCard.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start',
            inline: 'nearest'
        });
    }
    
    // Track section interaction
    trackUserActivity('section_toggle', { 
        section: sectionNumber, 
        action: isExpanded ? 'collapse' : 'expand' 
    });
}

// ===== INTERACTIVE CONTENT HANDLERS =====
function startPresentation(presentationId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Starting presentation: ${presentationId}. This would launch an interactive slide presentation.`
        : `بدء العرض التقديمي: ${presentationId}. سيؤدي هذا إلى تشغيل عرض تقديمي تفاعلي.`;
    
    showNotification(message, 'info');
    trackUserActivity('presentation_start', { presentation: presentationId });
    
    // In a real implementation, this would launch the presentation interface
    setTimeout(() => {
        markContentCompleted(presentationId);
    }, 2000);
}

function startAnimation(animationId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Loading animation: ${animationId}. This would display an interactive 3D animation.`
        : `تحميل الرسم المتحرك: ${animationId}. سيؤدي هذا إلى عرض رسم متحرك ثلاثي الأبعاد تفاعلي.`;
    
    showNotification(message, 'info');
    trackUserActivity('animation_start', { animation: animationId });
    
    // Simulate animation completion
    setTimeout(() => {
        markContentCompleted(animationId);
    }, 3000);
}

function startLecture(lectureId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Starting interactive lecture: ${lectureId}. This would launch the lecture interface.`
        : `بدء المحاضرة التفاعلية: ${lectureId}. سيؤدي هذا إلى تشغيل واجهة المحاضرة.`;
    
    showNotification(message, 'info');
    trackUserActivity('lecture_start', { lecture: lectureId });
    
    setTimeout(() => {
        markContentCompleted(lectureId);
    }, 2500);
}

function playVideo(videoId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Playing video: ${videoId}. This would open the video player with 3D heart anatomy.`
        : `تشغيل الفيديو: ${videoId}. سيؤدي هذا إلى فتح مشغل الفيديو مع تشريح القلب ثلاثي الأبعاد.`;
    
    showNotification(message, 'info');
    trackUserActivity('video_start', { video: videoId });
    
    setTimeout(() => {
        markContentCompleted(videoId);
    }, 4000);
}

function startInteractiveTool(toolId) {
    const currentLang = document.documentElement.lang || 'en';
    let message = '';
    
    switch(toolId) {
        case 'electrode-placement':
            message = currentLang === 'en' 
                ? 'Launching electrode placement simulator. Drag and drop electrodes on the virtual patient.'
                : 'تشغيل محاكي وضع الأقطاب الكهربائية. اسحب وأفلت الأقطاب على المريض الافتراضي.';
            break;
        case 'ecg-filtering':
            message = currentLang === 'en' 
                ? 'Opening ECG filtering tool. Adjust filter parameters and observe real-time results.'
                : 'فتح أداة ترشيح تخطيط القلب. اضبط معاملات المرشح ولاحظ النتائج في الوقت الفعلي.';
            break;
        default:
            message = currentLang === 'en' 
                ? `Starting interactive tool: ${toolId}`
                : `بدء الأداة التفاعلية: ${toolId}`;
    }
    
    showNotification(message, 'success');
    trackUserActivity('tool_start', { tool: toolId });
    
    // Simulate tool completion
    setTimeout(() => {
        markContentCompleted(toolId);
    }, 3500);
}

function openGallery(galleryId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Opening image gallery: ${galleryId}. Browse through ECG signal examples.`
        : `فتح معرض الصور: ${galleryId}. تصفح أمثلة إشارات تخطيط القلب.`;
    
    showNotification(message, 'info');
    trackUserActivity('gallery_open', { gallery: galleryId });
    
    setTimeout(() => {
        markContentCompleted(galleryId);
    }, 2000);
}

function startQuiz(quizId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Starting quiz: ${quizId}. This would launch the interactive quiz interface.`
        : `بدء الاختبار: ${quizId}. سيؤدي هذا إلى تشغيل واجهة الاختبار التفاعلية.`;
    
    showNotification(message, 'warning');
    trackUserActivity('quiz_start', { quiz: quizId });
    
    // For module assessment, switch to quiz tab
    if (quizId === 'module-assessment') {
        switchTab('quiz');
    }
}

// ===== VIRTUAL LAB FUNCTIONALITY =====
function startLabStep(stepNumber) {
    const currentLang = document.documentElement.lang || 'en';
    const stepNames = {
        1: { en: 'Virtual Patient Setup', ar: 'إعداد المريض الافتراضي' },
        2: { en: 'Electrode Placement', ar: 'وضع الأقطاب الكهربائية' },
        3: { en: 'ECG Signal Acquisition', ar: 'اكتساب إشارة تخطيط القلب' },
        4: { en: 'Signal Analysis', ar: 'تحليل الإشارة' },
        5: { en: 'Troubleshooting', ar: 'استكشاف الأخطاء وإصلاحها' }
    };
    
    const stepName = stepNames[stepNumber][currentLang];
    const message = currentLang === 'en' 
        ? `Starting lab step ${stepNumber}: ${stepName}`
        : `بدء خطوة المعمل ${stepNumber}: ${stepName}`;
    
    showNotification(message, 'info');
    trackUserActivity('lab_step_start', { step: stepNumber, stepName });
    
    // Update current step
    moduleProgress.currentStep = stepNumber;
    updateProgressDisplay();
    
    // Simulate step completion
    setTimeout(() => {
        markLabStepCompleted(stepNumber);
    }, 3000);
}

function startVirtualLab() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Launching Virtual ECG Laboratory. This would open the full lab simulation interface.'
        : 'تشغيل معمل تخطيط القلب الافتراضي. سيؤدي هذا إلى فتح واجهة محاكاة المعمل الكاملة.';
    
    showNotification(message, 'success');
    trackUserActivity('virtual_lab_start', {});
    
    // In a real implementation, this would launch the full lab interface
    setTimeout(() => {
        moduleProgress.labCompleted = true;
        updateProgressDisplay();
        
        const completionMessage = currentLang === 'en' 
            ? 'Virtual lab completed successfully! You can now take the final quiz.'
            : 'تم إكمال المعمل الافتراضي بنجاح! يمكنك الآن أخذ الاختبار النهائي.';
        showNotification(completionMessage, 'success');
    }, 5000);
}

function previewQuiz() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Quiz preview: 15 questions covering ECG fundamentals, instrumentation, and signal processing.'
        : 'معاينة الاختبار: 15 سؤالاً يغطي أساسيات تخطيط القلب والأجهزة ومعالجة الإشارات.';
    
    showNotification(message, 'info');
    trackUserActivity('quiz_preview', {});
}

// ===== PROGRESS TRACKING =====
function initializeProgressTracking() {
    updateProgressDisplay();
}

function markContentCompleted(contentId) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Content completed: ${contentId}`
        : `تم إكمال المحتوى: ${contentId}`;
    
    showNotification(message, 'success');
    trackUserActivity('content_completed', { content: contentId });
    
    // Update progress
    updateProgressDisplay();
}

function markLabStepCompleted(stepNumber) {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? `Lab step ${stepNumber} completed!`
        : `تم إكمال خطوة المعمل ${stepNumber}!`;
    
    showNotification(message, 'success');
    trackUserActivity('lab_step_completed', { step: stepNumber });
}

function updateProgressDisplay() {
    // This would update progress indicators in the UI
    const progressPercentage = Math.round(
        ((moduleProgress.sectionsCompleted / moduleProgress.totalSections) * 60) +
        (moduleProgress.labCompleted ? 25 : 0) +
        (moduleProgress.quizCompleted ? 15 : 0)
    );
    
    // Update any progress bars or indicators
    console.log(`Module progress: ${progressPercentage}%`);
}

// ===== UTILITY FUNCTIONS =====
function setupInteractiveElements() {
    // Add any additional interactive element setup here
    console.log('Interactive elements set up');
}

function trackUserActivity(action, data) {
    // Track user interactions for analytics
    console.log(`User activity: ${action}`, data);
    
    // In a real implementation, this would send data to analytics service
    const activityData = {
        timestamp: new Date().toISOString(),
        action: action,
        data: data,
        module: 'ecg-signal-acquisition',
        user: 'current-user-id' // Would be actual user ID
    };
    
    // Store locally for demo purposes
    const activities = JSON.parse(localStorage.getItem('moduleActivities') || '[]');
    activities.push(activityData);
    localStorage.setItem('moduleActivities', JSON.stringify(activities));
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
    
    // Add animation styles if not exists
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`ECG Module language changed to: ${newLang}`);
    
    // Update any dynamic content that needs language refresh
    updateProgressDisplay();
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        toggleSection,
        switchTab,
        startPresentation,
        startAnimation,
        startLecture,
        startInteractiveTool,
        startVirtualLab,
        startQuiz,
        trackUserActivity
    };
}
