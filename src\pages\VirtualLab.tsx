
import React, { useContext } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

const VirtualLab: React.FC = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      title: 'Virtual Lab',
      description: 'Experience our interactive virtual lab.',
    },
    ar: {
      title: 'المعمل الافتراضي',
      description: 'جرب معملنا الافتراضي التفاعلي.',
    },
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-4">{translations[language].title}</h1>
      <p>{translations[language].description}</p>
      {/* Virtual lab content will be added here */}
    </div>
  );
};

export default VirtualLab;
