
import React, { useContext, useState } from 'react';
import { LanguageContext } from '../contexts/LanguageContext';

interface LabExperiment {
  id: string;
  title: { en: string; ar: string };
  description: { en: string; ar: string };
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: { en: string; ar: string };
  equipment: { en: string[]; ar: string[] };
  icon: string;
  category: string;
}

const VirtualLab: React.FC = () => {
  const { language } = useContext(LanguageContext);
  const [selectedExperiment, setSelectedExperiment] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);

  const translations = {
    en: {
      title: 'Interactive Virtual Laboratory',
      subtitle: 'Hands-on Biomedical Engineering Experiments',
      description: 'Practice with realistic simulations of biomedical equipment and procedures in a safe virtual environment.',
      selectExperiment: 'Select an Experiment',
      difficulty: 'Difficulty',
      duration: 'Duration',
      equipment: 'Equipment Used',
      startExperiment: 'Start Experiment',
      nextStep: 'Next Step',
      previousStep: 'Previous Step',
      completeExperiment: 'Complete Experiment',
      resetExperiment: 'Reset Experiment',
      beginner: 'Beginner',
      intermediate: 'Intermediate',
      advanced: 'Advanced',
      step: 'Step',
      of: 'of',
      categories: {
        ecg: 'ECG & Cardiac Monitoring',
        imaging: 'Medical Imaging',
        biosignals: 'Biosignal Processing',
        devices: 'Medical Devices'
      }
    },
    ar: {
      title: 'المختبر الافتراضي التفاعلي',
      subtitle: 'تجارب الهندسة الطبية الحيوية العملية',
      description: 'تدرب مع محاكاة واقعية للمعدات والإجراءات الطبية الحيوية في بيئة افتراضية آمنة.',
      selectExperiment: 'اختر تجربة',
      difficulty: 'مستوى الصعوبة',
      duration: 'المدة',
      equipment: 'المعدات المستخدمة',
      startExperiment: 'ابدأ التجربة',
      nextStep: 'الخطوة التالية',
      previousStep: 'الخطوة السابقة',
      completeExperiment: 'أكمل التجربة',
      resetExperiment: 'إعادة تعيين التجربة',
      beginner: 'مبتدئ',
      intermediate: 'متوسط',
      advanced: 'متقدم',
      step: 'خطوة',
      of: 'من',
      categories: {
        ecg: 'تخطيط القلب ومراقبة القلب',
        imaging: 'التصوير الطبي',
        biosignals: 'معالجة الإشارات الحيوية',
        devices: 'الأجهزة الطبية'
      }
    },
  };

  const experiments: LabExperiment[] = [
    {
      id: 'ecg-acquisition',
      title: {
        en: 'ECG Signal Acquisition',
        ar: 'اكتساب إشارة تخطيط القلب'
      },
      description: {
        en: 'Learn to properly place electrodes and acquire clean ECG signals from a virtual patient.',
        ar: 'تعلم كيفية وضع الأقطاب الكهربائية بشكل صحيح واكتساب إشارات تخطيط القلب النظيفة من مريض افتراضي.'
      },
      difficulty: 'beginner',
      duration: { en: '30 min', ar: '30 دقيقة' },
      equipment: {
        en: ['ECG Machine', 'Electrodes', 'Patient Simulator', 'Amplifier'],
        ar: ['جهاز تخطيط القلب', 'الأقطاب الكهربائية', 'محاكي المريض', 'المضخم']
      },
      icon: '💓',
      category: 'ecg'
    },
    {
      id: 'ecg-filtering',
      title: {
        en: 'ECG Signal Filtering',
        ar: 'ترشيح إشارة تخطيط القلب'
      },
      description: {
        en: 'Apply different filters to remove noise and artifacts from ECG signals.',
        ar: 'تطبيق مرشحات مختلفة لإزالة الضوضاء والتشويش من إشارات تخطيط القلب.'
      },
      difficulty: 'intermediate',
      duration: { en: '45 min', ar: '45 دقيقة' },
      equipment: {
        en: ['Signal Processor', 'Filter Bank', 'Oscilloscope', 'Spectrum Analyzer'],
        ar: ['معالج الإشارات', 'بنك المرشحات', 'راسم الذبذبات', 'محلل الطيف']
      },
      icon: '📊',
      category: 'biosignals'
    },
    {
      id: 'ultrasound-imaging',
      title: {
        en: 'Ultrasound Imaging Simulation',
        ar: 'محاكاة التصوير بالموجات فوق الصوتية'
      },
      description: {
        en: 'Practice ultrasound imaging techniques and learn to interpret ultrasound images.',
        ar: 'تدرب على تقنيات التصوير بالموجات فوق الصوتية وتعلم تفسير صور الموجات فوق الصوتية.'
      },
      difficulty: 'advanced',
      duration: { en: '60 min', ar: '60 دقيقة' },
      equipment: {
        en: ['Ultrasound Machine', 'Transducer', 'Phantom', 'Image Processor'],
        ar: ['جهاز الموجات فوق الصوتية', 'المحول', 'الوهمي', 'معالج الصور']
      },
      icon: '🏥',
      category: 'imaging'
    },
    {
      id: 'blood-pressure',
      title: {
        en: 'Blood Pressure Measurement',
        ar: 'قياس ضغط الدم'
      },
      description: {
        en: 'Learn proper techniques for measuring blood pressure using different methods.',
        ar: 'تعلم التقنيات الصحيحة لقياس ضغط الدم باستخدام طرق مختلفة.'
      },
      difficulty: 'beginner',
      duration: { en: '25 min', ar: '25 دقيقة' },
      equipment: {
        en: ['Sphygmomanometer', 'Stethoscope', 'Pressure Sensor', 'Display Unit'],
        ar: ['مقياس ضغط الدم', 'السماعة الطبية', 'مستشعر الضغط', 'وحدة العرض']
      },
      icon: '🩺',
      category: 'devices'
    }
  ];

  const ecgSteps = [
    {
      title: { en: 'Patient Preparation', ar: 'إعداد المريض' },
      content: {
        en: 'Position the virtual patient and prepare the skin for electrode placement.',
        ar: 'ضع المريض الافتراضي في الوضع الصحيح وأعد الجلد لوضع الأقطاب الكهربائية.'
      },
      image: '👤'
    },
    {
      title: { en: 'Electrode Placement', ar: 'وضع الأقطاب الكهربائية' },
      content: {
        en: 'Place the 12-lead ECG electrodes according to standard positions.',
        ar: 'ضع أقطاب تخطيط القلب الـ 12 وفقًا للمواضع القياسية.'
      },
      image: '🔌'
    },
    {
      title: { en: 'Signal Acquisition', ar: 'اكتساب الإشارة' },
      content: {
        en: 'Start the ECG machine and acquire the cardiac signal.',
        ar: 'ابدأ تشغيل جهاز تخطيط القلب واكتسب إشارة القلب.'
      },
      image: '📈'
    },
    {
      title: { en: 'Signal Analysis', ar: 'تحليل الإشارة' },
      content: {
        en: 'Analyze the acquired ECG signal for rhythm and abnormalities.',
        ar: 'حلل إشارة تخطيط القلب المكتسبة للإيقاع والشذوذات.'
      },
      image: '🔍'
    }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const selectedExp = experiments.find(exp => exp.id === selectedExperiment);
  const steps = selectedExperiment === 'ecg-acquisition' ? ecgSteps : [];

  return (
    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-12 px-6 rounded-lg mb-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4">{translations[language].title}</h1>
          <h2 className="text-xl mb-4 opacity-90">{translations[language].subtitle}</h2>
          <p className="text-lg opacity-80">{translations[language].description}</p>
        </div>
      </div>

      {!selectedExperiment ? (
        /* Experiment Selection */
        <div>
          <h2 className="text-2xl font-bold mb-6">{translations[language].selectExperiment}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {experiments.map((experiment) => (
              <div key={experiment.id} className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-green-500 p-6 text-center">
                  <div className="text-4xl mb-2">{experiment.icon}</div>
                  <h3 className="text-xl font-bold text-white">{experiment.title[language]}</h3>
                  <div className="text-sm opacity-80 mt-1">
                    {translations[language].categories[experiment.category]}
                  </div>
                </div>

                <div className="p-6">
                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {experiment.description[language]}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].difficulty}:</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(experiment.difficulty)}`}>
                        {translations[language][experiment.difficulty]}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{translations[language].duration}:</span>
                      <span className="text-sm font-medium">{experiment.duration[language]}</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">{translations[language].equipment}:</h4>
                    <div className="flex flex-wrap gap-1">
                      {experiment.equipment[language].map((item, index) => (
                        <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={() => setSelectedExperiment(experiment.id)}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200"
                  >
                    {translations[language].startExperiment}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        /* Experiment Interface */
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">{selectedExp?.title[language]}</h2>
            <button
              onClick={() => {
                setSelectedExperiment(null);
                setActiveStep(0);
              }}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              {translations[language].resetExperiment}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Steps Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-bold mb-4">
                  {translations[language].step} {activeStep + 1} {translations[language].of} {steps.length}
                </h3>
                <div className="space-y-3">
                  {steps.map((step, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        index === activeStep
                          ? 'bg-blue-100 border-2 border-blue-500'
                          : index < activeStep
                          ? 'bg-green-100 border-2 border-green-500'
                          : 'bg-gray-100 border-2 border-gray-300'
                      }`}
                      onClick={() => setActiveStep(index)}
                    >
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">{step.image}</span>
                        <span className="font-medium text-sm">{step.title[language]}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-lg p-8">
                {steps[activeStep] && (
                  <>
                    <div className="text-center mb-8">
                      <div className="text-6xl mb-4">{steps[activeStep].image}</div>
                      <h3 className="text-2xl font-bold mb-4">{steps[activeStep].title[language]}</h3>
                      <p className="text-gray-600 text-lg">{steps[activeStep].content[language]}</p>
                    </div>

                    {/* Interactive Area */}
                    <div className="bg-gray-50 rounded-lg p-8 mb-6 text-center">
                      <div className="text-4xl mb-4">🖥️</div>
                      <p className="text-gray-600">
                        {language === 'en'
                          ? 'Interactive simulation area - Click and drag to interact with virtual equipment'
                          : 'منطقة المحاكاة التفاعلية - انقر واسحب للتفاعل مع المعدات الافتراضية'
                        }
                      </p>
                    </div>

                    {/* Navigation Buttons */}
                    <div className="flex justify-between">
                      <button
                        onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
                        disabled={activeStep === 0}
                        className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                      >
                        {translations[language].previousStep}
                      </button>

                      {activeStep < steps.length - 1 ? (
                        <button
                          onClick={() => setActiveStep(activeStep + 1)}
                          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                        >
                          {translations[language].nextStep}
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            setSelectedExperiment(null);
                            setActiveStep(0);
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
                        >
                          {translations[language].completeExperiment}
                        </button>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VirtualLab;
