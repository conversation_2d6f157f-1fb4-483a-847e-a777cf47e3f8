/**
 * BioEngage LMS - Main JavaScript File
 * Author: Dr. <PERSON>, SUST - BME
 * Enhanced version with improved functionality and animations
 */

// ===== GLOBAL VARIABLES =====
let currentLanguage = 'en';
let isAnimationEnabled = true;

// ===== DOM CONTENT LOADED EVENT =====
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
});

// ===== MAIN INITIALIZATION FUNCTION =====
function initializeApp() {
    // Initialize language system
    initializeLanguageSystem();
    
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Initialize accessibility features
    initializeAccessibility();
    
    // Initialize page-specific features
    initializePageSpecificFeatures();
    
    // Add smooth page load animation
    addPageLoadAnimation();
    
    console.log('BioEngage LMS initialized successfully');
}

// ===== LANGUAGE SYSTEM =====
function initializeLanguageSystem() {
    const langToggleBtn = document.getElementById('lang-toggle');
    const htmlElement = document.documentElement;
    
    if (!langToggleBtn) return;
    
    // Load saved language preference
    const savedLang = localStorage.getItem('bioengageLang') || 'en';
    currentLanguage = savedLang;
    
    // Set initial language
    setLanguage(savedLang);
    
    // Add event listener for language toggle
    langToggleBtn.addEventListener('click', toggleLanguage);
    
    // Update flag and text based on current language
    updateLanguageToggleButton();
}

function setLanguage(lang) {
    const htmlElement = document.documentElement;
    
    // Update HTML lang attribute
    htmlElement.lang = lang;
    
    // Update all elements with language attributes
    updateTextContent(lang);
    
    // Update language toggle button
    updateLanguageToggleButton();
    
    // Save preference
    localStorage.setItem('bioengageLang', lang);
    
    currentLanguage = lang;
    
    // Trigger custom event for language change
    document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
}

function toggleLanguage() {
    const newLang = currentLanguage === 'en' ? 'ar' : 'en';
    setLanguage(newLang);
    
    // Add visual feedback
    const langToggle = document.getElementById('lang-toggle');
    if (langToggle) {
        langToggle.style.transform = 'scale(0.95)';
        setTimeout(() => {
            langToggle.style.transform = 'scale(1)';
        }, 150);
    }
}

function updateTextContent(lang) {
    // Update elements with data-lang attributes
    document.querySelectorAll('[data-lang-en], [data-lang-ar]').forEach(element => {
        const enText = element.getAttribute('data-lang-en');
        const arText = element.getAttribute('data-lang-ar');
        
        if (lang === 'en' && enText) {
            element.textContent = enText;
        } else if (lang === 'ar' && arText) {
            element.textContent = arText;
        }
    });
    
    // Update page title
    const pageTitle = document.querySelector('head title');
    if (pageTitle) {
        const enTitle = pageTitle.getAttribute('data-lang-en');
        const arTitle = pageTitle.getAttribute('data-lang-ar');
        if (lang === 'en' && enTitle) {
            pageTitle.textContent = enTitle;
        } else if (lang === 'ar' && arTitle) {
            pageTitle.textContent = arTitle;
        }
    }
}

function updateLanguageToggleButton() {
    const langToggle = document.getElementById('lang-toggle');
    if (!langToggle) return;
    
    const flag = langToggle.querySelector('.flag');
    const langText = langToggle.querySelector('.lang-text');
    
    if (currentLanguage === 'en') {
        if (flag) flag.textContent = '🇸🇦';
        if (langText) langText.textContent = 'العربية';
    } else {
        if (flag) flag.textContent = '🇺🇸';
        if (langText) langText.textContent = 'English';
    }
}

// ===== MOBILE MENU =====
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');
    
    if (!mobileMenuToggle || !mobileNav) return;
    
    mobileMenuToggle.addEventListener('click', () => {
        const isActive = mobileNav.classList.contains('active');
        
        if (isActive) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    });
    
    // Close mobile menu when clicking on links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!mobileMenuToggle.contains(e.target) && !mobileNav.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

function openMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');
    
    if (mobileMenuToggle) mobileMenuToggle.classList.add('active');
    if (mobileNav) mobileNav.classList.add('active');
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-nav');
    
    if (mobileMenuToggle) mobileMenuToggle.classList.remove('active');
    if (mobileNav) mobileNav.classList.remove('active');
    
    // Restore body scroll
    document.body.style.overflow = '';
}

// ===== ANIMATIONS =====
function initializeAnimations() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    isAnimationEnabled = !prefersReducedMotion;
    
    if (!isAnimationEnabled) return;
    
    // Initialize intersection observer for scroll animations
    initializeScrollAnimations();
    
    // Initialize hover animations
    initializeHoverAnimations();
}

function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for scroll animations
    const animatedElements = document.querySelectorAll('.feature-card, .stat-item, .cta');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

function initializeHoverAnimations() {
    // Add enhanced hover effects to interactive elements
    const interactiveElements = document.querySelectorAll('.feature-card, .stat-item, .btn');
    
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            if (isAnimationEnabled) {
                element.style.transform = 'translateY(-5px) scale(1.02)';
            }
        });
        
        element.addEventListener('mouseleave', () => {
            if (isAnimationEnabled) {
                element.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    // Add click feedback to buttons
    const buttons = document.querySelectorAll('.btn, .nav-link, .mobile-nav-link');
    
    buttons.forEach(button => {
        button.addEventListener('click', (e) => {
            // Add ripple effect
            createRippleEffect(e);
            
            // Add click animation
            if (isAnimationEnabled) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
        });
    });
    
    // Initialize counter animations for stats
    initializeCounterAnimations();
}

function createRippleEffect(e) {
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    const ripple = document.createElement('span');
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
    `;
    
    // Add ripple animation CSS if not exists
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

function initializeCounterAnimations() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    statNumbers.forEach(stat => observer.observe(stat));
}

function animateCounter(element) {
    const target = parseInt(element.textContent.replace(/\D/g, ''));
    const suffix = element.textContent.replace(/\d/g, '');
    let current = 0;
    const increment = target / 50;
    const duration = 1500;
    const stepTime = duration / 50;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + suffix;
    }, stepTime);
}

// ===== ACCESSIBILITY =====
function initializeAccessibility() {
    // Add keyboard navigation support
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Add focus management
    initializeFocusManagement();
    
    // Add ARIA labels for dynamic content
    updateAriaLabels();
}

function handleKeyboardNavigation(e) {
    // ESC key closes mobile menu
    if (e.key === 'Escape') {
        closeMobileMenu();
    }
    
    // Enter key activates language toggle
    if (e.key === 'Enter' && e.target.id === 'lang-toggle') {
        toggleLanguage();
    }
}

function initializeFocusManagement() {
    // Trap focus in mobile menu when open
    const mobileNav = document.getElementById('mobile-nav');
    if (!mobileNav) return;
    
    const focusableElements = mobileNav.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])');
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    mobileNav.addEventListener('keydown', (e) => {
        if (!mobileNav.classList.contains('active')) return;
        
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        }
    });
}

function updateAriaLabels() {
    // Update ARIA labels based on current language
    document.addEventListener('languageChanged', (e) => {
        const lang = e.detail.language;
        
        // Update mobile menu button aria-label
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        if (mobileMenuToggle) {
            const label = lang === 'en' ? 'Toggle navigation menu' : 'تبديل قائمة التنقل';
            mobileMenuToggle.setAttribute('aria-label', label);
        }
        
        // Update language toggle aria-label
        const langToggle = document.getElementById('lang-toggle');
        if (langToggle) {
            const label = lang === 'en' ? 'Switch to Arabic' : 'التبديل إلى الإنجليزية';
            langToggle.setAttribute('aria-label', label);
        }
    });
}

// ===== PAGE-SPECIFIC FEATURES =====
function initializePageSpecificFeatures() {
    const body = document.body;
    
    // Homepage specific features
    if (body.classList.contains('homepage')) {
        initializeHomepageFeatures();
    }
    
    // Modules page specific features
    if (body.classList.contains('modules-page')) {
        initializeModulesPageFeatures();
    }
    
    // Virtual lab page specific features
    if (body.classList.contains('virtual-lab-page')) {
        initializeVirtualLabFeatures();
    }
    
    // Training page specific features
    if (body.classList.contains('training-page')) {
        initializeTrainingPageFeatures();
    }
    
    // Interactive lectures page specific features
    if (body.classList.contains('interactive-lectures-page')) {
        initializeInteractiveLecturesFeatures();
    }
}

function initializeHomepageFeatures() {
    // Add parallax effect to hero section (if motion is enabled)
    if (isAnimationEnabled) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
    }
    
    // Add typing animation to hero title
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle && isAnimationEnabled) {
        addTypingAnimation(heroTitle);
    }
}

function addTypingAnimation(element) {
    const text = element.textContent;
    element.textContent = '';
    element.style.borderRight = '2px solid';
    element.style.animation = 'blink 1s infinite';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        } else {
            element.style.borderRight = 'none';
            element.style.animation = 'none';
        }
    };
    
    // Add blink animation CSS
    if (!document.querySelector('#typing-styles')) {
        const style = document.createElement('style');
        style.id = 'typing-styles';
        style.textContent = `
            @keyframes blink {
                0%, 50% { border-color: transparent; }
                51%, 100% { border-color: currentColor; }
            }
        `;
        document.head.appendChild(style);
    }
    
    setTimeout(typeWriter, 1000);
}

// Placeholder functions for other pages
function initializeModulesPageFeatures() {
    console.log('Modules page features initialized');
}

function initializeVirtualLabFeatures() {
    console.log('Virtual lab features initialized');
}

function initializeTrainingPageFeatures() {
    console.log('Training page features initialized');
}

function initializeInteractiveLecturesFeatures() {
    console.log('Interactive lectures features initialized');
}

// ===== PAGE LOAD ANIMATION =====
function addPageLoadAnimation() {
    if (!isAnimationEnabled) return;
    
    document.body.style.opacity = '0';
    document.body.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        document.body.style.opacity = '1';
        document.body.style.transform = 'translateY(0)';
    }, 100);
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
    console.error('BioEngage LMS Error:', e.error);
});

// ===== PERFORMANCE MONITORING =====
window.addEventListener('load', () => {
    if ('performance' in window) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log(`BioEngage LMS loaded in ${loadTime}ms`);
    }
});

// ===== EXPORT FOR MODULE USAGE =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        setLanguage,
        toggleLanguage,
        openMobileMenu,
        closeMobileMenu
    };
}
