/**
 * Slide Deck JavaScript
 * BioEngage LMS - Interactive Slide Presentation
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let currentSlide = 1;
let totalSlides = 11;
let isPlaying = false;
let autoPlayInterval = null;
let isFullscreen = false;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeSlideDeck();
});

function initializeSlideDeck() {
    // Initialize slide navigation
    initializeSlideNavigation();
    
    // Initialize controls
    initializeControls();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Update slide counter
    updateSlideCounter();
    
    console.log('Slide deck initialized successfully');
}

// ===== SLIDE NAVIGATION =====
function initializeSlideNavigation() {
    const slides = document.querySelectorAll('.slide');
    totalSlides = slides.length;
    
    // Update total slides counter
    document.getElementById('total-slides').textContent = totalSlides;
    
    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Touch/swipe navigation for mobile
    initializeTouchNavigation();
}

function handleKeyboardNavigation(e) {
    switch(e.key) {
        case 'ArrowRight':
        case ' ':
            e.preventDefault();
            nextSlide();
            break;
        case 'ArrowLeft':
            e.preventDefault();
            prevSlide();
            break;
        case 'Home':
            e.preventDefault();
            goToSlide(1);
            break;
        case 'End':
            e.preventDefault();
            goToSlide(totalSlides);
            break;
        case 'Escape':
            e.preventDefault();
            exitFullscreen();
            break;
        case 'F11':
            e.preventDefault();
            toggleFullscreen();
            break;
    }
}

function initializeTouchNavigation() {
    let startX = 0;
    let endX = 0;
    
    const slideContainer = document.getElementById('slide-container');
    
    slideContainer.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
    });
    
    slideContainer.addEventListener('touchend', (e) => {
        endX = e.changedTouches[0].clientX;
        handleSwipe();
    });
    
    function handleSwipe() {
        const threshold = 50;
        const diff = startX - endX;
        
        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                nextSlide(); // Swipe left - next slide
            } else {
                prevSlide(); // Swipe right - previous slide
            }
        }
    }
}

function nextSlide() {
    if (currentSlide < totalSlides) {
        goToSlide(currentSlide + 1);
    }
}

function prevSlide() {
    if (currentSlide > 1) {
        goToSlide(currentSlide - 1);
    }
}

function goToSlide(slideNumber) {
    if (slideNumber < 1 || slideNumber > totalSlides) return;
    
    // Remove active class from current slide
    const currentSlideElement = document.querySelector('.slide.active');
    if (currentSlideElement) {
        currentSlideElement.classList.remove('active');
        currentSlideElement.classList.add('prev');
    }
    
    // Add active class to new slide
    const newSlideElement = document.querySelector(`.slide[data-slide="${slideNumber}"]`);
    if (newSlideElement) {
        newSlideElement.classList.remove('prev');
        newSlideElement.classList.add('active');
        
        // Trigger animations for the new slide
        triggerSlideAnimations(newSlideElement);
    }
    
    currentSlide = slideNumber;
    updateSlideCounter();
    updateNavigationButtons();
    
    // Track slide change
    trackSlideChange(slideNumber);
}

// ===== CONTROLS =====
function initializeControls() {
    // Previous slide button
    document.getElementById('prev-slide').addEventListener('click', prevSlide);
    
    // Next slide button
    document.getElementById('next-slide').addEventListener('click', nextSlide);
    
    // Play/pause button
    document.getElementById('play-pause').addEventListener('click', toggleAutoPlay);
    
    // Fullscreen button
    document.getElementById('fullscreen-btn').addEventListener('click', toggleFullscreen);
}

function updateSlideCounter() {
    document.getElementById('current-slide').textContent = currentSlide;
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prev-slide');
    const nextBtn = document.getElementById('next-slide');
    
    prevBtn.disabled = currentSlide === 1;
    nextBtn.disabled = currentSlide === totalSlides;
}

function toggleAutoPlay() {
    const playPauseBtn = document.getElementById('play-pause');
    const icon = playPauseBtn.querySelector('i');
    
    if (isPlaying) {
        // Pause
        clearInterval(autoPlayInterval);
        isPlaying = false;
        icon.className = 'fas fa-play';
    } else {
        // Play
        autoPlayInterval = setInterval(() => {
            if (currentSlide < totalSlides) {
                nextSlide();
            } else {
                // End of slides, stop auto-play
                toggleAutoPlay();
            }
        }, 5000); // 5 seconds per slide
        
        isPlaying = true;
        icon.className = 'fas fa-pause';
    }
}

function toggleFullscreen() {
    const slideDeckContainer = document.querySelector('.slide-deck-container');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const icon = fullscreenBtn.querySelector('i');
    
    if (!isFullscreen) {
        // Enter fullscreen
        if (slideDeckContainer.requestFullscreen) {
            slideDeckContainer.requestFullscreen();
        } else if (slideDeckContainer.webkitRequestFullscreen) {
            slideDeckContainer.webkitRequestFullscreen();
        } else if (slideDeckContainer.msRequestFullscreen) {
            slideDeckContainer.msRequestFullscreen();
        }
        
        isFullscreen = true;
        icon.className = 'fas fa-compress';
        slideDeckContainer.classList.add('fullscreen-mode');
    } else {
        exitFullscreen();
    }
}

function exitFullscreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }
    
    isFullscreen = false;
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const icon = fullscreenBtn.querySelector('i');
    icon.className = 'fas fa-expand';
    
    const slideDeckContainer = document.querySelector('.slide-deck-container');
    slideDeckContainer.classList.remove('fullscreen-mode');
}

// ===== ANIMATIONS =====
function initializeAnimations() {
    // Initialize animation delays
    const animatedElements = document.querySelectorAll('[data-delay]');
    animatedElements.forEach(element => {
        const delay = element.dataset.delay;
        element.style.animationDelay = delay;
    });
}

function triggerSlideAnimations(slideElement) {
    // Reset all animations in the slide
    const animatedElements = slideElement.querySelectorAll('.animated-card, .fade-in, .fade-in-up, .fade-in-left, .fade-in-right');
    
    animatedElements.forEach(element => {
        element.style.animation = 'none';
        element.offsetHeight; // Trigger reflow
        element.style.animation = null;
    });
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    // Initialize ECG demo
    initializeECGDemo();
    
    // Initialize virtual lab launcher
    initializeVirtualLabLauncher();
    
    // Initialize interactive diagrams
    initializeInteractiveDiagrams();
}

function initializeECGDemo() {
    // ECG demo is initialized but controlled by slide-specific functions
    console.log('ECG demo initialized');
}

function startECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Starting ECG demonstration... Watch the heart rhythm and electrode signals!'
        : 'بدء عرض تخطيط القلب... راقب نظم القلب وإشارات الأقطاب الكهربائية!';
    
    showSlideNotification(message, 'info');
    
    // Animate ECG waveform
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animation = 'none';
        ecgPath.offsetHeight; // Trigger reflow
        ecgPath.style.animation = 'ecg-trace 3s ease-in-out infinite';
    }
    
    // Animate electrode pulses
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach((electrode, index) => {
        setTimeout(() => {
            electrode.style.animation = 'pulse 1s ease-in-out infinite';
        }, index * 200);
    });
}

function pauseECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'ECG demonstration paused.'
        : 'تم إيقاف عرض تخطيط القلب مؤقتاً.';
    
    showSlideNotification(message, 'warning');
    
    // Pause animations
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animationPlayState = 'paused';
    }
    
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach(electrode => {
        electrode.style.animationPlayState = 'paused';
    });
}

function resetECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'ECG demonstration reset.'
        : 'تم إعادة تعيين عرض تخطيط القلب.';
    
    showSlideNotification(message, 'info');
    
    // Reset animations
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animation = 'none';
        ecgPath.offsetHeight; // Trigger reflow
    }
    
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach(electrode => {
        electrode.style.animation = 'none';
    });
}

function initializeVirtualLabLauncher() {
    // Virtual lab launcher is ready
    console.log('Virtual lab launcher initialized');
}

function launchVirtualLab() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Launching Virtual Laboratory... This would open the full lab simulation interface.'
        : 'تشغيل المعمل الافتراضي... سيؤدي هذا إلى فتح واجهة محاكاة المعمل الكاملة.';
    
    showSlideNotification(message, 'success');
    
    // In a real implementation, this would launch the virtual lab
    setTimeout(() => {
        const successMessage = currentLang === 'en' 
            ? 'Virtual Lab launched successfully! You can now interact with medical equipment.'
            : 'تم تشغيل المعمل الافتراضي بنجاح! يمكنك الآن التفاعل مع المعدات الطبية.';
        showSlideNotification(successMessage, 'success');
    }, 2000);
}

function initializeInteractiveDiagrams() {
    // Add hover effects to interactive elements
    const orbitItems = document.querySelectorAll('.orbit-item');
    orbitItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            item.style.transform += ' scale(1.1)';
        });
        
        item.addEventListener('mouseleave', () => {
            item.style.transform = item.style.transform.replace(' scale(1.1)', '');
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function trackSlideChange(slideNumber) {
    // Track slide navigation for analytics
    console.log(`Navigated to slide ${slideNumber}`);
    
    // In a real implementation, this would send data to analytics service
    const slideData = {
        timestamp: new Date().toISOString(),
        slideNumber: slideNumber,
        totalSlides: totalSlides,
        sessionId: 'current-session-id'
    };
    
    // Store locally for demo purposes
    const slideHistory = JSON.parse(localStorage.getItem('slideHistory') || '[]');
    slideHistory.push(slideData);
    localStorage.setItem('slideHistory', JSON.stringify(slideHistory));
}

function showSlideNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `slide-notification slide-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
    
    // Add animation styles if not exists
    if (!document.querySelector('#slide-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'slide-notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// ===== FULLSCREEN EVENT LISTENERS =====
document.addEventListener('fullscreenchange', handleFullscreenChange);
document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
document.addEventListener('msfullscreenchange', handleFullscreenChange);

function handleFullscreenChange() {
    const isCurrentlyFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement);
    
    if (!isCurrentlyFullscreen && isFullscreen) {
        // Exited fullscreen
        exitFullscreen();
    }
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Slide deck language changed to: ${newLang}`);
    
    // Update any dynamic content that needs language refresh
    updateSlideCounter();
});

// ===== INTERACTIVE FILTER DEMO =====
function initializeFilterDemo() {
    // Initialize filter controls
    const filterType = document.getElementById('filter-type');
    const cutoffFreq = document.getElementById('cutoff-freq');
    const noiseLevel = document.getElementById('noise-level');

    if (filterType) {
        filterType.addEventListener('change', updateFilterDisplay);
    }

    if (cutoffFreq) {
        cutoffFreq.addEventListener('input', (e) => {
            document.getElementById('freq-value').textContent = `${e.target.value} Hz`;
            updateFilterDisplay();
        });
    }

    if (noiseLevel) {
        noiseLevel.addEventListener('input', (e) => {
            document.getElementById('noise-value').textContent = `${e.target.value}%`;
            updateFilterDisplay();
        });
    }
}

function updateFilterDisplay() {
    const filterType = document.getElementById('filter-type')?.value;
    const cutoffFreq = document.getElementById('cutoff-freq')?.value;
    const noiseLevel = document.getElementById('noise-level')?.value;

    // Update signal visualization based on filter settings
    updateSignalVisualization(filterType, cutoffFreq, noiseLevel);
}

function updateSignalVisualization(filterType, cutoffFreq, noiseLevel) {
    const noisePath = document.querySelector('.noise-path');
    const filteredPath = document.querySelector('.signal-path.filtered');

    if (noisePath) {
        noisePath.style.opacity = noiseLevel / 100;
    }

    if (filteredPath) {
        // Simulate filter effect based on type and cutoff frequency
        let pathData = 'M0,50 Q100,30 200,50 T400,50';

        switch(filterType) {
            case 'lowpass':
                // Smoother curve for low pass
                pathData = 'M0,50 Q100,35 200,50 Q300,45 400,50';
                break;
            case 'highpass':
                // More jagged for high pass
                pathData = 'M0,50 L50,45 L100,55 L150,45 L200,55 L250,45 L300,55 L350,45 L400,50';
                break;
            case 'bandpass':
                // Combination effect
                pathData = 'M0,50 Q100,40 200,50 Q300,40 400,50';
                break;
            case 'notch':
                // Remove specific frequency components
                pathData = 'M0,50 Q100,48 200,50 Q300,52 400,50';
                break;
        }

        filteredPath.setAttribute('d', pathData);
    }
}

function applyFilter() {
    const currentLang = document.documentElement.lang || 'en';
    const filterType = document.getElementById('filter-type')?.value;
    const message = currentLang === 'en'
        ? `Applying ${filterType} filter... Observe the signal changes!`
        : `تطبيق مرشح ${filterType}... لاحظ تغيرات الإشارة!`;

    showSlideNotification(message, 'info');

    // Animate filter application
    const filteredPath = document.querySelector('.signal-path.filtered');
    if (filteredPath) {
        filteredPath.style.strokeDasharray = '400';
        filteredPath.style.strokeDashoffset = '400';
        filteredPath.style.animation = 'none';

        setTimeout(() => {
            filteredPath.style.animation = 'ecg-trace 2s ease-in-out forwards';
        }, 100);
    }

    updateFilterDisplay();
}

function resetFilter() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en'
        ? 'Filter reset to default settings.'
        : 'تم إعادة تعيين المرشح إلى الإعدادات الافتراضية.';

    showSlideNotification(message, 'info');

    // Reset controls
    const filterType = document.getElementById('filter-type');
    const cutoffFreq = document.getElementById('cutoff-freq');
    const noiseLevel = document.getElementById('noise-level');

    if (filterType) filterType.value = 'lowpass';
    if (cutoffFreq) {
        cutoffFreq.value = 40;
        document.getElementById('freq-value').textContent = '40 Hz';
    }
    if (noiseLevel) {
        noiseLevel.value = 30;
        document.getElementById('noise-value').textContent = '30%';
    }

    // Reset signal visualization
    updateSignalVisualization('lowpass', 40, 30);
}

// ===== ENHANCED SLIDE INITIALIZATION =====
function initializeSlideSpecificContent() {
    // Initialize filter demo when slide 9 is active
    if (currentSlide === 9) {
        initializeFilterDemo();
    }

    // Initialize other slide-specific content as needed
    console.log(`Initialized content for slide ${currentSlide}`);
}

// Update the goToSlide function to include slide-specific initialization
const originalGoToSlide = goToSlide;
goToSlide = function(slideNumber) {
    originalGoToSlide(slideNumber);

    // Initialize slide-specific content
    setTimeout(() => {
        initializeSlideSpecificContent();
    }, 500); // Wait for slide transition
};

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        nextSlide,
        prevSlide,
        goToSlide,
        toggleAutoPlay,
        toggleFullscreen,
        startECGDemo,
        pauseECGDemo,
        resetECGDemo,
        launchVirtualLab,
        applyFilter,
        resetFilter,
        updateFilterDisplay
    };
}
