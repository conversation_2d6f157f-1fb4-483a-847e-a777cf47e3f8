/**
 * Enhanced Slide Deck JavaScript - BioEngage LMS
 * Advanced Interactive 30-Slide Presentation System
 * Author: Dr. <PERSON>, SUST - BME
 * Version: 2.0 - Enhanced with advanced features and animations
 */

// ===== ENHANCED GLOBAL VARIABLES =====
let currentSlide = 1;
let totalSlides = 30; // Standardized 30 slides per module
let isPlaying = false;
let autoPlayInterval = null;
let isFullscreen = false;
let currentModule = 'bme-fundamentals'; // Track current module
let slideHistory = []; // Track navigation history
let bookmarks = new Set(); // User bookmarks
let presentationStartTime = null;
let slideViewTimes = {}; // Track time spent on each slide
let currentSection = 'introduction';
let isTransitioning = false;
let touchStartX = 0;
let touchStartY = 0;
let lastSlideChangeTime = 0;

// Enhanced module configuration for 30-slide standardized decks
let slideModules = {
    'bme-fundamentals': {
        start: 1, end: 30,
        title: 'BME Fundamentals & Instrumentation',
        color: '#3b82f6',
        icon: '🧬',
        difficulty: 'Beginner',
        duration: 90,
        sections: {
            introduction: { start: 1, end: 5, color: '#10b981' },
            fundamentals: { start: 6, end: 15, color: '#3b82f6' },
            instrumentation: { start: 16, end: 25, color: '#8b5cf6' },
            applications: { start: 26, end: 30, color: '#ef4444' }
        }
    },
    'medical-imaging': {
        start: 1, end: 30,
        title: 'Medical Imaging Systems',
        color: '#10b981',
        icon: '📷',
        difficulty: 'Intermediate',
        duration: 120,
        sections: {
            introduction: { start: 1, end: 5, color: '#10b981' },
            fundamentals: { start: 6, end: 15, color: '#3b82f6' },
            instrumentation: { start: 16, end: 25, color: '#8b5cf6' },
            applications: { start: 26, end: 30, color: '#ef4444' }
        }
    },
    'biosignal-processing': {
        start: 1, end: 30,
        title: 'Biosignal Processing & Analysis',
        color: '#ef4444',
        icon: '📊',
        difficulty: 'Advanced',
        duration: 100,
        sections: {
            introduction: { start: 1, end: 5, color: '#10b981' },
            fundamentals: { start: 6, end: 15, color: '#3b82f6' },
            instrumentation: { start: 16, end: 25, color: '#8b5cf6' },
            applications: { start: 26, end: 30, color: '#ef4444' }
        }
    },
    'biomechanics': {
        start: 1, end: 30,
        title: 'Biomechanics & Rehabilitation',
        color: '#8b5cf6',
        icon: '🦴',
        difficulty: 'Intermediate',
        duration: 110,
        sections: {
            introduction: { start: 1, end: 5, color: '#10b981' },
            fundamentals: { start: 6, end: 15, color: '#3b82f6' },
            instrumentation: { start: 16, end: 25, color: '#8b5cf6' },
            applications: { start: 26, end: 30, color: '#ef4444' }
        }
    }
};

// Enhanced slide content templates with advanced timing and animations
let slideTemplates = {
    title: { type: 'title', duration: 8000, animation: 'fadeInScale', priority: 'high' },
    objectives: { type: 'objectives', duration: 12000, animation: 'slideInUp', priority: 'high' },
    introduction: { type: 'introduction', duration: 10000, animation: 'fadeInLeft', priority: 'medium' },
    theory: { type: 'theory', duration: 15000, animation: 'slideInRight', priority: 'high' },
    interactive: { type: 'interactive', duration: 20000, animation: 'bounceIn', priority: 'critical' },
    demonstration: { type: 'demonstration', duration: 25000, animation: 'zoomIn', priority: 'critical' },
    application: { type: 'application', duration: 18000, animation: 'slideInUp', priority: 'high' },
    case_study: { type: 'case_study', duration: 20000, animation: 'fadeInUp', priority: 'high' },
    virtual_lab: { type: 'virtual_lab', duration: 30000, animation: 'rotateIn', priority: 'critical' },
    assessment: { type: 'assessment', duration: 15000, animation: 'slideInDown', priority: 'medium' },
    summary: { type: 'summary', duration: 12000, animation: 'fadeInScale', priority: 'high' }
};

// Performance and accessibility settings
let performanceSettings = {
    enableAnimations: true,
    reducedMotion: false,
    autoSave: true,
    preloadSlides: 3,
    maxHistorySize: 50,
    animationDuration: 600,
    transitionDelay: 100
};

// ===== ENHANCED INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeEnhancedSlideDeck();
});

function initializeEnhancedSlideDeck() {
    // Check for reduced motion preference
    checkAccessibilityPreferences();

    // Initialize presentation timing
    presentationStartTime = Date.now();

    // Detect current module from URL or page title
    detectCurrentModule();

    // Initialize enhanced slide navigation
    initializeEnhancedSlideNavigation();

    // Initialize enhanced controls with keyboard shortcuts
    initializeEnhancedControls();

    // Initialize advanced animations and transitions
    initializeAdvancedAnimations();

    // Initialize interactive elements with touch support
    initializeEnhancedInteractiveElements();

    // Initialize progress tracking and analytics
    initializeProgressTracking();

    // Initialize section-based navigation
    initializeSectionNavigation();

    // Initialize auto-save functionality
    initializeAutoSave();

    // Update enhanced slide counter and progress
    updateEnhancedSlideCounter();

    // Preload upcoming slides for better performance
    preloadUpcomingSlides();

    // Initialize accessibility features
    initializeAccessibilityFeatures();

    console.log('Enhanced slide deck initialized successfully with advanced features');
}

function checkAccessibilityPreferences() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
        performanceSettings.enableAnimations = false;
        performanceSettings.reducedMotion = true;
        performanceSettings.animationDuration = 200;
    }

    // Check for high contrast preference
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    if (prefersHighContrast) {
        document.body.classList.add('high-contrast-mode');
    }
}

function detectCurrentModule() {
    // Detect module from URL
    const url = window.location.pathname;
    if (url.includes('bme-fundamentals')) {
        currentModule = 'bme-fundamentals';
    } else if (url.includes('medical-imaging')) {
        currentModule = 'medical-imaging';
    } else if (url.includes('biosignal')) {
        currentModule = 'biosignal-processing';
    } else if (url.includes('biomechanics')) {
        currentModule = 'biomechanics';
    }

    // Update page styling based on module
    updateModuleStyling();
}

function updateModuleStyling() {
    const moduleConfig = slideModules[currentModule];
    if (moduleConfig) {
        document.documentElement.style.setProperty('--module-primary-color', moduleConfig.color);
        document.documentElement.style.setProperty('--module-icon', `"${moduleConfig.icon}"`);

        // Update header with module-specific styling
        const header = document.querySelector('.slide-deck-header');
        if (header) {
            header.style.background = `linear-gradient(135deg, ${moduleConfig.color}, ${adjustColorBrightness(moduleConfig.color, 20)})`;
        }
    }
}

function adjustColorBrightness(color, percent) {
    // Simple color brightness adjustment
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

// ===== ENHANCED SLIDE NAVIGATION =====
function initializeEnhancedSlideNavigation() {
    const slides = document.querySelectorAll('.slide');
    totalSlides = slides.length;

    // Update total slides counter with animation
    const totalSlidesElement = document.getElementById('total-slides');
    if (totalSlidesElement) {
        animateNumber(totalSlidesElement, 0, totalSlides, 1000);
    }

    // Enhanced keyboard navigation with more shortcuts
    document.addEventListener('keydown', handleEnhancedKeyboardNavigation);

    // Enhanced touch/swipe navigation for mobile with gestures
    initializeEnhancedTouchNavigation();

    // Initialize slide preloading
    initializeSlidePreloading();

    // Initialize slide transition effects
    initializeSlideTransitions();

    // Add slide indicators
    createSlideIndicators();
}

function initializeSlidePreloading() {
    // Preload images and content for better performance
    const slides = document.querySelectorAll('.slide');
    slides.forEach((slide, index) => {
        if (index <= currentSlide + performanceSettings.preloadSlides) {
            preloadSlideContent(slide);
        }
    });
}

function preloadSlideContent(slide) {
    // Preload images in the slide
    const images = slide.querySelectorAll('img[data-src]');
    images.forEach(img => {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
    });

    // Preload any video content
    const videos = slide.querySelectorAll('video[data-src]');
    videos.forEach(video => {
        video.src = video.dataset.src;
        video.removeAttribute('data-src');
    });
}

function createSlideIndicators() {
    const indicatorContainer = document.createElement('div');
    indicatorContainer.className = 'slide-indicators';
    indicatorContainer.innerHTML = '';

    for (let i = 1; i <= totalSlides; i++) {
        const indicator = document.createElement('div');
        indicator.className = `slide-indicator ${i === currentSlide ? 'active' : ''}`;
        indicator.dataset.slide = i;
        indicator.addEventListener('click', () => goToSlideEnhanced(i));

        // Add section color coding
        const section = getSectionForSlide(i);
        if (section && slideModules[currentModule]?.sections[section]) {
            indicator.style.backgroundColor = slideModules[currentModule].sections[section].color;
        }

        indicatorContainer.appendChild(indicator);
    }

    // Insert indicators into the progress container
    const progressContainer = document.querySelector('.slide-progress-container');
    if (progressContainer) {
        progressContainer.appendChild(indicatorContainer);
    }
}

function getSectionForSlide(slideNumber) {
    const moduleConfig = slideModules[currentModule];
    if (!moduleConfig) return 'introduction';

    for (const [sectionName, sectionConfig] of Object.entries(moduleConfig.sections)) {
        if (slideNumber >= sectionConfig.start && slideNumber <= sectionConfig.end) {
            return sectionName;
        }
    }
    return 'introduction';
}

function initializeSlideTransitions() {
    // Add CSS classes for smooth transitions
    const slideContainer = document.getElementById('slide-container');
    if (slideContainer) {
        slideContainer.classList.add('enhanced-transitions');
    }
}

function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * easeOutCubic(progress));

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

function handleEnhancedKeyboardNavigation(e) {
    // Prevent navigation during transitions
    if (isTransitioning) return;

    // Check for modifier keys for advanced shortcuts
    const isCtrl = e.ctrlKey || e.metaKey;
    const isShift = e.shiftKey;
    const isAlt = e.altKey;

    switch(e.key) {
        case 'ArrowRight':
        case ' ':
            e.preventDefault();
            if (isShift) {
                jumpToNextSection();
            } else {
                nextSlideEnhanced();
            }
            break;
        case 'ArrowLeft':
            e.preventDefault();
            if (isShift) {
                jumpToPreviousSection();
            } else {
                prevSlideEnhanced();
            }
            break;
        case 'ArrowUp':
            e.preventDefault();
            goToSlideEnhanced(1); // Jump to first slide
            break;
        case 'ArrowDown':
            e.preventDefault();
            goToSlideEnhanced(totalSlides); // Jump to last slide
            break;
        case 'Home':
            e.preventDefault();
            goToSlideEnhanced(1);
            break;
        case 'End':
            e.preventDefault();
            goToSlideEnhanced(totalSlides);
            break;
        case 'Escape':
            e.preventDefault();
            if (isFullscreen) {
                exitFullscreen();
            } else {
                showSlideOverview();
            }
            break;
        case 'F11':
            e.preventDefault();
            toggleFullscreen();
            break;
        case 'p':
        case 'P':
            if (isCtrl) {
                e.preventDefault();
                toggleAutoPlay();
            }
            break;
        case 'b':
        case 'B':
            if (isCtrl) {
                e.preventDefault();
                toggleBookmark(currentSlide);
            }
            break;
        case 'h':
        case 'H':
            if (isCtrl) {
                e.preventDefault();
                showKeyboardShortcuts();
            }
            break;
        case 'r':
        case 'R':
            if (isCtrl) {
                e.preventDefault();
                resetPresentation();
            }
            break;
        case '1':
        case '2':
        case '3':
        case '4':
            if (isCtrl) {
                e.preventDefault();
                const sectionNumber = parseInt(e.key);
                jumpToSection(sectionNumber);
            }
            break;
        case 'g':
        case 'G':
            if (isCtrl) {
                e.preventDefault();
                showGoToSlideDialog();
            }
            break;
    }
}

function jumpToNextSection() {
    const currentSectionName = getSectionForSlide(currentSlide);
    const moduleConfig = slideModules[currentModule];
    if (!moduleConfig) return;

    const sections = Object.keys(moduleConfig.sections);
    const currentIndex = sections.indexOf(currentSectionName);

    if (currentIndex < sections.length - 1) {
        const nextSection = sections[currentIndex + 1];
        const nextSectionStart = moduleConfig.sections[nextSection].start;
        goToSlideEnhanced(nextSectionStart);
    }
}

function jumpToPreviousSection() {
    const currentSectionName = getSectionForSlide(currentSlide);
    const moduleConfig = slideModules[currentModule];
    if (!moduleConfig) return;

    const sections = Object.keys(moduleConfig.sections);
    const currentIndex = sections.indexOf(currentSectionName);

    if (currentIndex > 0) {
        const prevSection = sections[currentIndex - 1];
        const prevSectionStart = moduleConfig.sections[prevSection].start;
        goToSlideEnhanced(prevSectionStart);
    }
}

function jumpToSection(sectionNumber) {
    const moduleConfig = slideModules[currentModule];
    if (!moduleConfig) return;

    const sections = Object.keys(moduleConfig.sections);
    if (sectionNumber >= 1 && sectionNumber <= sections.length) {
        const sectionName = sections[sectionNumber - 1];
        const sectionStart = moduleConfig.sections[sectionName].start;
        goToSlideEnhanced(sectionStart);
    }
}

function initializeEnhancedTouchNavigation() {
    const slideContainer = document.getElementById('slide-container');
    if (!slideContainer) return;

    let startX = 0;
    let startY = 0;
    let endX = 0;
    let endY = 0;
    let startTime = 0;
    let isScrolling = false;

    slideContainer.addEventListener('touchstart', (e) => {
        if (isTransitioning) return;

        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        startTime = Date.now();
        isScrolling = false;

        // Store initial touch position
        touchStartX = startX;
        touchStartY = startY;
    }, { passive: true });

    slideContainer.addEventListener('touchmove', (e) => {
        if (isTransitioning) return;

        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;
        const deltaX = Math.abs(currentX - startX);
        const deltaY = Math.abs(currentY - startY);

        // Determine if user is scrolling vertically
        if (deltaY > deltaX && deltaY > 10) {
            isScrolling = true;
        }

        // Prevent horizontal swipe if scrolling vertically
        if (!isScrolling && deltaX > 10) {
            e.preventDefault();
        }
    }, { passive: false });

    slideContainer.addEventListener('touchend', (e) => {
        if (isTransitioning || isScrolling) return;

        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        const endTime = Date.now();

        handleEnhancedSwipe(endTime - startTime);
    }, { passive: true });

    function handleEnhancedSwipe(duration) {
        const threshold = 50;
        const velocityThreshold = 0.3; // pixels per millisecond
        const diffX = startX - endX;
        const diffY = startY - endY;
        const velocity = Math.abs(diffX) / duration;

        // Check for horizontal swipe
        if (Math.abs(diffX) > threshold && Math.abs(diffX) > Math.abs(diffY)) {
            if (velocity > velocityThreshold) {
                // Fast swipe - jump multiple slides
                if (diffX > 0) {
                    jumpSlides(3); // Swipe left - jump forward
                } else {
                    jumpSlides(-3); // Swipe right - jump backward
                }
            } else {
                // Normal swipe
                if (diffX > 0) {
                    nextSlideEnhanced(); // Swipe left - next slide
                } else {
                    prevSlideEnhanced(); // Swipe right - previous slide
                }
            }
        }

        // Check for vertical swipe (section navigation)
        else if (Math.abs(diffY) > threshold && Math.abs(diffY) > Math.abs(diffX)) {
            if (diffY > 0) {
                jumpToNextSection(); // Swipe up - next section
            } else {
                jumpToPreviousSection(); // Swipe down - previous section
            }
        }

        // Check for tap (show/hide controls)
        else if (Math.abs(diffX) < 10 && Math.abs(diffY) < 10 && duration < 300) {
            toggleSlideControls();
        }
    }

    // Add pinch-to-zoom gesture for accessibility
    let initialDistance = 0;

    slideContainer.addEventListener('touchstart', (e) => {
        if (e.touches.length === 2) {
            initialDistance = getDistance(e.touches[0], e.touches[1]);
        }
    });

    slideContainer.addEventListener('touchmove', (e) => {
        if (e.touches.length === 2) {
            const currentDistance = getDistance(e.touches[0], e.touches[1]);
            const scale = currentDistance / initialDistance;

            if (scale > 1.2) {
                // Pinch out - zoom in (show slide overview)
                showSlideOverview();
            } else if (scale < 0.8) {
                // Pinch in - zoom out (fullscreen)
                toggleFullscreen();
            }
        }
    });

    function getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }
}

function jumpSlides(count) {
    const targetSlide = Math.max(1, Math.min(totalSlides, currentSlide + count));
    goToSlideEnhanced(targetSlide);
}

function nextSlideEnhanced() {
    if (currentSlide < totalSlides && !isTransitioning) {
        goToSlideEnhanced(currentSlide + 1);
    }
}

function prevSlideEnhanced() {
    if (currentSlide > 1 && !isTransitioning) {
        goToSlideEnhanced(currentSlide - 1);
    }
}

function goToSlideEnhanced(slideNumber) {
    if (slideNumber < 1 || slideNumber > totalSlides || isTransitioning) return;

    // Prevent rapid slide changes
    const now = Date.now();
    if (now - lastSlideChangeTime < performanceSettings.transitionDelay) return;
    lastSlideChangeTime = now;

    isTransitioning = true;

    // Track slide view time
    trackSlideViewTime(currentSlide);

    // Add to history
    slideHistory.push({
        from: currentSlide,
        to: slideNumber,
        timestamp: now,
        section: getSectionForSlide(slideNumber)
    });

    // Limit history size
    if (slideHistory.length > performanceSettings.maxHistorySize) {
        slideHistory.shift();
    }

    // Remove active class from current slide with animation
    const currentSlideElement = document.querySelector('.slide.active');
    if (currentSlideElement) {
        currentSlideElement.classList.add('slide-exit');

        setTimeout(() => {
            currentSlideElement.classList.remove('active', 'slide-exit');
            currentSlideElement.classList.add('prev');
        }, performanceSettings.animationDuration / 2);
    }

    // Add active class to new slide with animation
    const newSlideElement = document.querySelector(`.slide[data-slide="${slideNumber}"]`);
    if (newSlideElement) {
        newSlideElement.classList.add('slide-enter');

        setTimeout(() => {
            newSlideElement.classList.remove('prev', 'slide-enter');
            newSlideElement.classList.add('active');

            // Trigger enhanced animations for the new slide
            triggerEnhancedSlideAnimations(newSlideElement);

            // Update section if changed
            updateCurrentSection(slideNumber);

            isTransitioning = false;
        }, performanceSettings.animationDuration / 2);
    } else {
        isTransitioning = false;
    }

    currentSlide = slideNumber;
    updateEnhancedSlideCounter();
    updateEnhancedNavigationButtons();
    updateSlideIndicators();
    updateProgressBar();

    // Preload upcoming slides
    preloadUpcomingSlides();

    // Track slide change with enhanced analytics
    trackEnhancedSlideChange(slideNumber);

    // Auto-save progress
    if (performanceSettings.autoSave) {
        saveProgress();
    }
}

// ===== MISSING ENHANCED FUNCTIONS =====
function initializeProgressTracking() {
    // Initialize slide view time tracking
    slideViewTimes[currentSlide] = Date.now();
}

function initializeSectionNavigation() {
    // Add section navigation functionality
    const sectionMarkers = document.querySelectorAll('.section-marker');
    sectionMarkers.forEach(marker => {
        marker.addEventListener('click', () => {
            const section = marker.dataset.section;
            jumpToSectionByName(section);
        });
    });
}

function initializeAutoSave() {
    // Auto-save progress every 30 seconds
    setInterval(() => {
        if (performanceSettings.autoSave) {
            saveProgress();
        }
    }, 30000);
}

function initializeAccessibilityFeatures() {
    // Add ARIA labels and keyboard navigation hints
    const slides = document.querySelectorAll('.slide');
    slides.forEach((slide, index) => {
        slide.setAttribute('aria-label', `Slide ${index + 1} of ${totalSlides}`);
        slide.setAttribute('role', 'tabpanel');
    });

    // Add skip navigation
    addSkipNavigation();
}

function preloadUpcomingSlides() {
    // Preload next few slides for better performance
    for (let i = 1; i <= performanceSettings.preloadSlides; i++) {
        const nextSlideNumber = currentSlide + i;
        if (nextSlideNumber <= totalSlides) {
            const nextSlide = document.querySelector(`.slide[data-slide="${nextSlideNumber}"]`);
            if (nextSlide) {
                preloadSlideContent(nextSlide);
            }
        }
    }
}

function updateEnhancedSlideCounter() {
    const currentSlideElement = document.getElementById('current-slide');
    if (currentSlideElement) {
        animateNumber(currentSlideElement, parseInt(currentSlideElement.textContent) || 1, currentSlide, 300);
    }

    // Update slide counter with section info
    const sectionInfo = document.querySelector('.section-info');
    if (sectionInfo) {
        const sectionName = currentSection.charAt(0).toUpperCase() + currentSection.slice(1);
        sectionInfo.textContent = sectionName;
    }
}

function initializeAdvancedAnimations() {
    // Initialize advanced animation system
    if (!performanceSettings.enableAnimations) return;

    // Add CSS animation classes
    const style = document.createElement('style');
    style.textContent = `
        .slide-enter { opacity: 0; transform: translateX(100px); }
        .slide-exit { opacity: 0; transform: translateX(-100px); }
        .enhanced-transitions .slide { transition: all ${performanceSettings.animationDuration}ms ease; }
        .fade-in-scale { animation: fadeInScale 0.8s ease-out; }
        @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
    `;
    document.head.appendChild(style);
}

function initializeEnhancedInteractiveElements() {
    // Enhanced interactive elements with better touch support
    initializeECGDemo();
    initializeVirtualLabLauncher();
    initializeInteractiveDiagrams();

    // Add gesture recognition
    initializeGestureRecognition();
}

function triggerEnhancedSlideAnimations(slideElement) {
    if (!performanceSettings.enableAnimations) return;

    // Reset all animations in the slide
    const animatedElements = slideElement.querySelectorAll('.animated-card, .fade-in, .fade-in-up, .fade-in-left, .fade-in-right');

    animatedElements.forEach((element, index) => {
        element.style.animation = 'none';
        element.offsetHeight; // Trigger reflow

        setTimeout(() => {
            element.style.animation = null;
            element.classList.add('animate-in');
        }, index * 100);
    });
}

function trackSlideViewTime(slideNumber) {
    if (slideViewTimes[slideNumber]) {
        const viewTime = Date.now() - slideViewTimes[slideNumber];
        // Store view time for analytics
        localStorage.setItem(`slide_${slideNumber}_view_time`, viewTime.toString());
    }
    slideViewTimes[slideNumber] = Date.now();
}

function trackEnhancedSlideChange(slideNumber) {
    // Enhanced analytics tracking
    const slideData = {
        timestamp: new Date().toISOString(),
        slideNumber: slideNumber,
        totalSlides: totalSlides,
        section: currentSection,
        module: currentModule,
        sessionId: getSessionId(),
        userAgent: navigator.userAgent,
        viewportSize: `${window.innerWidth}x${window.innerHeight}`
    };

    // Store locally for demo purposes
    const slideHistory = JSON.parse(localStorage.getItem('enhancedSlideHistory') || '[]');
    slideHistory.push(slideData);

    // Keep only last 100 entries
    if (slideHistory.length > 100) {
        slideHistory.shift();
    }

    localStorage.setItem('enhancedSlideHistory', JSON.stringify(slideHistory));
}

function saveProgress() {
    const progress = {
        currentSlide: currentSlide,
        currentSection: currentSection,
        module: currentModule,
        timestamp: Date.now(),
        bookmarks: Array.from(bookmarks),
        totalViewTime: Date.now() - presentationStartTime
    };

    localStorage.setItem(`progress_${currentModule}`, JSON.stringify(progress));
}

function updateCurrentSection(slideNumber) {
    const newSection = getSectionForSlide(slideNumber);
    if (newSection !== currentSection) {
        currentSection = newSection;
        updateSectionStyling();
        triggerSectionChangeEvent();
    }
}

function updateSectionStyling() {
    const moduleConfig = slideModules[currentModule];
    if (moduleConfig && moduleConfig.sections[currentSection]) {
        const sectionColor = moduleConfig.sections[currentSection].color;
        document.documentElement.style.setProperty('--current-section-color', sectionColor);

        // Update section markers
        const sectionMarkers = document.querySelectorAll('.section-marker');
        sectionMarkers.forEach(marker => {
            marker.classList.remove('active');
            if (marker.dataset.section === currentSection) {
                marker.classList.add('active');
            }
        });
    }
}

function triggerSectionChangeEvent() {
    const event = new CustomEvent('sectionChanged', {
        detail: {
            section: currentSection,
            slide: currentSlide,
            module: currentModule
        }
    });
    document.dispatchEvent(event);
}

function updateSlideIndicators() {
    const indicators = document.querySelectorAll('.slide-indicator');
    indicators.forEach((indicator, index) => {
        indicator.classList.remove('active');
        if (index + 1 === currentSlide) {
            indicator.classList.add('active');
        }
    });
}

function updateProgressBar() {
    const progressFill = document.getElementById('progress-fill');
    if (progressFill) {
        const progress = (currentSlide / totalSlides) * 100;
        progressFill.style.width = `${progress}%`;

        // Add smooth transition
        progressFill.style.transition = `width ${performanceSettings.animationDuration}ms ease`;
    }
}

// Legacy function support
function nextSlide() { nextSlideEnhanced(); }
function prevSlide() { prevSlideEnhanced(); }
function goToSlide(slideNumber) { goToSlideEnhanced(slideNumber); }

// ===== ENHANCED CONTROLS =====
function initializeEnhancedControls() {
    // Enhanced previous slide button
    const prevBtn = document.getElementById('prev-slide');
    if (prevBtn) {
        prevBtn.addEventListener('click', prevSlideEnhanced);
        prevBtn.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            jumpToPreviousSection();
        });
    }

    // Enhanced next slide button
    const nextBtn = document.getElementById('next-slide');
    if (nextBtn) {
        nextBtn.addEventListener('click', nextSlideEnhanced);
        nextBtn.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            jumpToNextSection();
        });
    }

    // Enhanced play/pause button
    const playPauseBtn = document.getElementById('play-pause');
    if (playPauseBtn) {
        playPauseBtn.addEventListener('click', toggleEnhancedAutoPlay);
    }

    // Enhanced fullscreen button
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }

    // Add new control buttons
    addEnhancedControlButtons();

    // Initialize control tooltips
    initializeControlTooltips();
}

function addEnhancedControlButtons() {
    const controlsContainer = document.querySelector('.control-buttons');
    if (!controlsContainer) return;

    // Add bookmark button
    const bookmarkBtn = document.createElement('button');
    bookmarkBtn.id = 'bookmark-btn';
    bookmarkBtn.className = 'control-btn';
    bookmarkBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
    bookmarkBtn.title = 'Bookmark this slide (Ctrl+B)';
    bookmarkBtn.addEventListener('click', () => toggleBookmark(currentSlide));

    // Add overview button
    const overviewBtn = document.createElement('button');
    overviewBtn.id = 'overview-btn';
    overviewBtn.className = 'control-btn';
    overviewBtn.innerHTML = '<i class="fas fa-th"></i>';
    overviewBtn.title = 'Show slide overview (Esc)';
    overviewBtn.addEventListener('click', showSlideOverview);

    // Add help button
    const helpBtn = document.createElement('button');
    helpBtn.id = 'help-btn';
    helpBtn.className = 'control-btn';
    helpBtn.innerHTML = '<i class="fas fa-question"></i>';
    helpBtn.title = 'Show keyboard shortcuts (Ctrl+H)';
    helpBtn.addEventListener('click', showKeyboardShortcuts);

    controlsContainer.appendChild(bookmarkBtn);
    controlsContainer.appendChild(overviewBtn);
    controlsContainer.appendChild(helpBtn);
}

function initializeControlTooltips() {
    const controlBtns = document.querySelectorAll('.control-btn[title]');
    controlBtns.forEach(btn => {
        btn.addEventListener('mouseenter', showTooltip);
        btn.addEventListener('mouseleave', hideTooltip);
    });
}

function updateEnhancedSlideCounter() {
    const currentSlideElement = document.getElementById('current-slide');
    if (currentSlideElement) {
        animateNumber(currentSlideElement, parseInt(currentSlideElement.textContent) || 1, currentSlide, 300);
    }

    // Update slide counter with section info
    const sectionInfo = document.querySelector('.section-info');
    if (sectionInfo) {
        const sectionName = currentSection.charAt(0).toUpperCase() + currentSection.slice(1);
        sectionInfo.textContent = sectionName;
    }
}

function updateEnhancedNavigationButtons() {
    const prevBtn = document.getElementById('prev-slide');
    const nextBtn = document.getElementById('next-slide');

    if (prevBtn) {
        prevBtn.disabled = currentSlide === 1;
        prevBtn.classList.toggle('disabled', currentSlide === 1);
    }

    if (nextBtn) {
        nextBtn.disabled = currentSlide === totalSlides;
        nextBtn.classList.toggle('disabled', currentSlide === totalSlides);
    }

    // Update bookmark button state
    const bookmarkBtn = document.getElementById('bookmark-btn');
    if (bookmarkBtn) {
        bookmarkBtn.classList.toggle('active', bookmarks.has(currentSlide));
    }
}

function toggleAutoPlay() {
    const playPauseBtn = document.getElementById('play-pause');
    const icon = playPauseBtn.querySelector('i');
    
    if (isPlaying) {
        // Pause
        clearInterval(autoPlayInterval);
        isPlaying = false;
        icon.className = 'fas fa-play';
    } else {
        // Play
        autoPlayInterval = setInterval(() => {
            if (currentSlide < totalSlides) {
                nextSlide();
            } else {
                // End of slides, stop auto-play
                toggleAutoPlay();
            }
        }, 5000); // 5 seconds per slide
        
        isPlaying = true;
        icon.className = 'fas fa-pause';
    }
}

function toggleFullscreen() {
    const slideDeckContainer = document.querySelector('.slide-deck-container');
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const icon = fullscreenBtn.querySelector('i');
    
    if (!isFullscreen) {
        // Enter fullscreen
        if (slideDeckContainer.requestFullscreen) {
            slideDeckContainer.requestFullscreen();
        } else if (slideDeckContainer.webkitRequestFullscreen) {
            slideDeckContainer.webkitRequestFullscreen();
        } else if (slideDeckContainer.msRequestFullscreen) {
            slideDeckContainer.msRequestFullscreen();
        }
        
        isFullscreen = true;
        icon.className = 'fas fa-compress';
        slideDeckContainer.classList.add('fullscreen-mode');
    } else {
        exitFullscreen();
    }
}

function exitFullscreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }
    
    isFullscreen = false;
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    const icon = fullscreenBtn.querySelector('i');
    icon.className = 'fas fa-expand';
    
    const slideDeckContainer = document.querySelector('.slide-deck-container');
    slideDeckContainer.classList.remove('fullscreen-mode');
}

// ===== ANIMATIONS =====
function initializeAnimations() {
    // Initialize animation delays
    const animatedElements = document.querySelectorAll('[data-delay]');
    animatedElements.forEach(element => {
        const delay = element.dataset.delay;
        element.style.animationDelay = delay;
    });
}

function triggerSlideAnimations(slideElement) {
    // Reset all animations in the slide
    const animatedElements = slideElement.querySelectorAll('.animated-card, .fade-in, .fade-in-up, .fade-in-left, .fade-in-right');
    
    animatedElements.forEach(element => {
        element.style.animation = 'none';
        element.offsetHeight; // Trigger reflow
        element.style.animation = null;
    });
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    // Initialize ECG demo
    initializeECGDemo();
    
    // Initialize virtual lab launcher
    initializeVirtualLabLauncher();
    
    // Initialize interactive diagrams
    initializeInteractiveDiagrams();
}

function initializeECGDemo() {
    // ECG demo is initialized but controlled by slide-specific functions
    console.log('ECG demo initialized');
}

function startECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Starting ECG demonstration... Watch the heart rhythm and electrode signals!'
        : 'بدء عرض تخطيط القلب... راقب نظم القلب وإشارات الأقطاب الكهربائية!';
    
    showSlideNotification(message, 'info');
    
    // Animate ECG waveform
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animation = 'none';
        ecgPath.offsetHeight; // Trigger reflow
        ecgPath.style.animation = 'ecg-trace 3s ease-in-out infinite';
    }
    
    // Animate electrode pulses
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach((electrode, index) => {
        setTimeout(() => {
            electrode.style.animation = 'pulse 1s ease-in-out infinite';
        }, index * 200);
    });
}

function pauseECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'ECG demonstration paused.'
        : 'تم إيقاف عرض تخطيط القلب مؤقتاً.';
    
    showSlideNotification(message, 'warning');
    
    // Pause animations
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animationPlayState = 'paused';
    }
    
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach(electrode => {
        electrode.style.animationPlayState = 'paused';
    });
}

function resetECGDemo() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'ECG demonstration reset.'
        : 'تم إعادة تعيين عرض تخطيط القلب.';
    
    showSlideNotification(message, 'info');
    
    // Reset animations
    const ecgPath = document.querySelector('.ecg-path');
    if (ecgPath) {
        ecgPath.style.animation = 'none';
        ecgPath.offsetHeight; // Trigger reflow
    }
    
    const electrodes = document.querySelectorAll('.electrode-dot');
    electrodes.forEach(electrode => {
        electrode.style.animation = 'none';
    });
}

function initializeVirtualLabLauncher() {
    // Virtual lab launcher is ready
    console.log('Virtual lab launcher initialized');
}

function launchVirtualLab() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Launching Virtual Laboratory... This would open the full lab simulation interface.'
        : 'تشغيل المعمل الافتراضي... سيؤدي هذا إلى فتح واجهة محاكاة المعمل الكاملة.';
    
    showSlideNotification(message, 'success');
    
    // In a real implementation, this would launch the virtual lab
    setTimeout(() => {
        const successMessage = currentLang === 'en' 
            ? 'Virtual Lab launched successfully! You can now interact with medical equipment.'
            : 'تم تشغيل المعمل الافتراضي بنجاح! يمكنك الآن التفاعل مع المعدات الطبية.';
        showSlideNotification(successMessage, 'success');
    }, 2000);
}

function initializeInteractiveDiagrams() {
    // Add hover effects to interactive elements
    const orbitItems = document.querySelectorAll('.orbit-item');
    orbitItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            item.style.transform += ' scale(1.1)';
        });
        
        item.addEventListener('mouseleave', () => {
            item.style.transform = item.style.transform.replace(' scale(1.1)', '');
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function trackSlideChange(slideNumber) {
    // Track slide navigation for analytics
    console.log(`Navigated to slide ${slideNumber}`);
    
    // In a real implementation, this would send data to analytics service
    const slideData = {
        timestamp: new Date().toISOString(),
        slideNumber: slideNumber,
        totalSlides: totalSlides,
        sessionId: 'current-session-id'
    };
    
    // Store locally for demo purposes
    const slideHistory = JSON.parse(localStorage.getItem('slideHistory') || '[]');
    slideHistory.push(slideData);
    localStorage.setItem('slideHistory', JSON.stringify(slideHistory));
}

function showSlideNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `slide-notification slide-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
    
    // Add animation styles if not exists
    if (!document.querySelector('#slide-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'slide-notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// ===== FULLSCREEN EVENT LISTENERS =====
document.addEventListener('fullscreenchange', handleFullscreenChange);
document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
document.addEventListener('msfullscreenchange', handleFullscreenChange);

function handleFullscreenChange() {
    const isCurrentlyFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement);
    
    if (!isCurrentlyFullscreen && isFullscreen) {
        // Exited fullscreen
        exitFullscreen();
    }
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`Slide deck language changed to: ${newLang}`);
    
    // Update any dynamic content that needs language refresh
    updateSlideCounter();
});

// ===== INTERACTIVE FILTER DEMO =====
function initializeFilterDemo() {
    // Initialize filter controls
    const filterType = document.getElementById('filter-type');
    const cutoffFreq = document.getElementById('cutoff-freq');
    const noiseLevel = document.getElementById('noise-level');

    if (filterType) {
        filterType.addEventListener('change', updateFilterDisplay);
    }

    if (cutoffFreq) {
        cutoffFreq.addEventListener('input', (e) => {
            document.getElementById('freq-value').textContent = `${e.target.value} Hz`;
            updateFilterDisplay();
        });
    }

    if (noiseLevel) {
        noiseLevel.addEventListener('input', (e) => {
            document.getElementById('noise-value').textContent = `${e.target.value}%`;
            updateFilterDisplay();
        });
    }
}

function updateFilterDisplay() {
    const filterType = document.getElementById('filter-type')?.value;
    const cutoffFreq = document.getElementById('cutoff-freq')?.value;
    const noiseLevel = document.getElementById('noise-level')?.value;

    // Update signal visualization based on filter settings
    updateSignalVisualization(filterType, cutoffFreq, noiseLevel);
}

function updateSignalVisualization(filterType, cutoffFreq, noiseLevel) {
    const noisePath = document.querySelector('.noise-path');
    const filteredPath = document.querySelector('.signal-path.filtered');

    if (noisePath) {
        noisePath.style.opacity = noiseLevel / 100;
    }

    if (filteredPath) {
        // Simulate filter effect based on type and cutoff frequency
        let pathData = 'M0,50 Q100,30 200,50 T400,50';

        switch(filterType) {
            case 'lowpass':
                // Smoother curve for low pass
                pathData = 'M0,50 Q100,35 200,50 Q300,45 400,50';
                break;
            case 'highpass':
                // More jagged for high pass
                pathData = 'M0,50 L50,45 L100,55 L150,45 L200,55 L250,45 L300,55 L350,45 L400,50';
                break;
            case 'bandpass':
                // Combination effect
                pathData = 'M0,50 Q100,40 200,50 Q300,40 400,50';
                break;
            case 'notch':
                // Remove specific frequency components
                pathData = 'M0,50 Q100,48 200,50 Q300,52 400,50';
                break;
        }

        filteredPath.setAttribute('d', pathData);
    }
}

function applyFilter() {
    const currentLang = document.documentElement.lang || 'en';
    const filterType = document.getElementById('filter-type')?.value;
    const message = currentLang === 'en'
        ? `Applying ${filterType} filter... Observe the signal changes!`
        : `تطبيق مرشح ${filterType}... لاحظ تغيرات الإشارة!`;

    showSlideNotification(message, 'info');

    // Animate filter application
    const filteredPath = document.querySelector('.signal-path.filtered');
    if (filteredPath) {
        filteredPath.style.strokeDasharray = '400';
        filteredPath.style.strokeDashoffset = '400';
        filteredPath.style.animation = 'none';

        setTimeout(() => {
            filteredPath.style.animation = 'ecg-trace 2s ease-in-out forwards';
        }, 100);
    }

    updateFilterDisplay();
}

function resetFilter() {
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en'
        ? 'Filter reset to default settings.'
        : 'تم إعادة تعيين المرشح إلى الإعدادات الافتراضية.';

    showSlideNotification(message, 'info');

    // Reset controls
    const filterType = document.getElementById('filter-type');
    const cutoffFreq = document.getElementById('cutoff-freq');
    const noiseLevel = document.getElementById('noise-level');

    if (filterType) filterType.value = 'lowpass';
    if (cutoffFreq) {
        cutoffFreq.value = 40;
        document.getElementById('freq-value').textContent = '40 Hz';
    }
    if (noiseLevel) {
        noiseLevel.value = 30;
        document.getElementById('noise-value').textContent = '30%';
    }

    // Reset signal visualization
    updateSignalVisualization('lowpass', 40, 30);
}

// ===== ENHANCED SLIDE INITIALIZATION =====
function initializeSlideSpecificContent() {
    // Initialize filter demo when slide 9 is active
    if (currentSlide === 9) {
        initializeFilterDemo();
    }

    // Initialize other slide-specific content as needed
    console.log(`Initialized content for slide ${currentSlide}`);
}

// Update the goToSlide function to include slide-specific initialization
const originalGoToSlide = goToSlide;
goToSlide = function(slideNumber) {
    originalGoToSlide(slideNumber);

    // Initialize slide-specific content
    setTimeout(() => {
        initializeSlideSpecificContent();
    }, 500); // Wait for slide transition
};

// ===== MEDICAL IMAGING INTERACTIVE FUNCTIONS =====

function takeXRay() {
    const currentLang = document.documentElement.lang || 'en';
    const kvp = document.getElementById('kvp-slider')?.value || 80;
    const mas = document.getElementById('mas-slider')?.value || 10;

    const message = currentLang === 'en'
        ? `Taking X-Ray with ${kvp} kVp and ${mas} mAs... Adjusting image contrast and brightness!`
        : `التقاط الأشعة السينية بـ ${kvp} كيلو فولت و ${mas} ميلي أمبير ثانية... ضبط تباين وسطوع الصورة!`;

    showSlideNotification(message, 'info');

    // Simulate X-ray exposure effect
    const xrayResult = document.getElementById('xray-result');
    if (xrayResult) {
        xrayResult.style.filter = `brightness(${kvp/100}) contrast(${mas/10})`;
        xrayResult.style.animation = 'xray-flash 0.5s ease-in-out';

        setTimeout(() => {
            xrayResult.style.animation = '';
        }, 500);
    }

    // Update parameter displays
    const kvpValue = document.getElementById('kvp-value');
    const masValue = document.getElementById('mas-value');
    if (kvpValue) kvpValue.textContent = `${kvp} kVp`;
    if (masValue) masValue.textContent = `${mas} mAs`;
}

function startMRIScan() {
    const currentLang = document.documentElement.lang || 'en';
    const sequence = document.getElementById('sequence-type')?.value || 't1';
    const tr = document.getElementById('tr-slider')?.value || 500;
    const te = document.getElementById('te-slider')?.value || 20;

    const message = currentLang === 'en'
        ? `Starting ${sequence.toUpperCase()} MRI scan with TR=${tr}ms, TE=${te}ms... Acquiring brain images!`
        : `بدء فحص الرنين المغناطيسي ${sequence.toUpperCase()} مع TR=${tr}مللي ثانية، TE=${te}مللي ثانية... اكتساب صور الدماغ!`;

    showSlideNotification(message, 'info');

    // Simulate MRI scanning process
    const scanProgress = document.getElementById('scan-progress');
    const mriResult = document.getElementById('mri-result');

    if (scanProgress) {
        const progressMessages = {
            en: ['Initializing...', 'Shimming...', 'Acquiring...', 'Processing...', 'Complete!'],
            ar: ['التهيئة...', 'المعايرة...', 'الاكتساب...', 'المعالجة...', 'مكتمل!']
        };

        const messages = progressMessages[currentLang] || progressMessages.en;
        let step = 0;

        const progressInterval = setInterval(() => {
            if (step < messages.length) {
                scanProgress.textContent = messages[step];
                step++;
            } else {
                clearInterval(progressInterval);

                // Apply sequence-specific contrast
                if (mriResult) {
                    switch(sequence) {
                        case 't1':
                            mriResult.style.filter = 'brightness(1.2) contrast(1.5)';
                            break;
                        case 't2':
                            mriResult.style.filter = 'brightness(0.8) contrast(2)';
                            break;
                        case 'flair':
                            mriResult.style.filter = 'brightness(1) contrast(2.5) saturate(0.5)';
                            break;
                        case 'dwi':
                            mriResult.style.filter = 'brightness(0.6) contrast(3)';
                            break;
                    }
                }
            }
        }, 800);
    }

    // Update parameter displays
    const trValue = document.getElementById('tr-value');
    const teValue = document.getElementById('te-value');
    if (trValue) trValue.textContent = `${tr} ms`;
    if (teValue) teValue.textContent = `${te} ms`;
}

function startUltrasound() {
    const currentLang = document.documentElement.lang || 'en';
    const frequency = document.getElementById('us-frequency')?.value || 5;
    const gain = document.getElementById('us-gain')?.value || 50;
    const depth = document.getElementById('us-depth')?.value || 15;

    const message = currentLang === 'en'
        ? `Starting ultrasound with ${frequency}MHz, ${gain}dB gain, ${depth}cm depth... Real-time imaging active!`
        : `بدء الموجات فوق الصوتية بـ ${frequency}ميجاهرتز، كسب ${gain}ديسيبل، عمق ${depth}سم... التصوير في الوقت الفعلي نشط!`;

    showSlideNotification(message, 'info');

    // Animate ultrasound scanning
    const usResult = document.getElementById('us-result');
    if (usResult) {
        // Adjust image based on parameters
        const brightness = gain / 100;
        const penetration = Math.max(0.3, 1 - (frequency - 2) / 13);

        usResult.style.filter = `brightness(${brightness}) contrast(${1 + frequency/10})`;
        usResult.style.opacity = penetration;

        // Animate scan lines
        const scanLines = usResult.querySelectorAll('.scan-line');
        scanLines.forEach((line, index) => {
            line.style.animationDelay = `${index * 0.1}s`;
            line.style.animationDuration = `${3 - frequency/5}s`;
        });
    }

    // Update parameter displays
    const freqValue = document.getElementById('frequency-value');
    const gainValue = document.getElementById('gain-value');
    const depthValue = document.getElementById('depth-value');
    if (freqValue) freqValue.textContent = `${frequency} MHz`;
    if (gainValue) gainValue.textContent = `${gain} dB`;
    if (depthValue) depthValue.textContent = `${depth} cm`;
}

function applyImageProcessing() {
    const currentLang = document.documentElement.lang || 'en';
    const brightness = document.getElementById('brightness')?.value || 0;
    const contrast = document.getElementById('contrast')?.value || 1;
    const sharpening = document.getElementById('sharpening')?.value || 0;

    const message = currentLang === 'en'
        ? `Applying image processing: Brightness=${brightness}, Contrast=${contrast}, Sharpening=${sharpening}%`
        : `تطبيق معالجة الصورة: السطوع=${brightness}، التباين=${contrast}، الحدة=${sharpening}%`;

    showSlideNotification(message, 'info');

    // Apply processing to the processed image
    const processedImage = document.getElementById('processed-medical-image');
    if (processedImage) {
        const brightnessFilter = `brightness(${1 + brightness/50})`;
        const contrastFilter = `contrast(${contrast})`;
        const sharpnessFilter = sharpening > 0 ? `blur(${Math.max(0, 1 - sharpening/50)}px)` : '';

        processedImage.style.filter = `${brightnessFilter} ${contrastFilter} ${sharpnessFilter}`;
        processedImage.style.transition = 'filter 0.5s ease';
    }

    // Update value displays
    const brightnessValue = document.getElementById('brightness-value');
    const contrastValue = document.getElementById('contrast-value');
    const sharpeningValue = document.getElementById('sharpening-value');
    if (brightnessValue) brightnessValue.textContent = brightness;
    if (contrastValue) contrastValue.textContent = contrast;
    if (sharpeningValue) sharpeningValue.textContent = sharpening;
}

// ===== ENHANCED SLIDE INITIALIZATION =====
function initializeSlideSpecificContent() {
    // Initialize content based on current slide
    switch(currentSlide) {
        case 9:
            initializeFilterDemo();
            break;
        case 13:
            initializeXRayDemo();
            break;
        case 14:
            initializeMRIDemo();
            break;
        case 15:
            initializeUltrasoundDemo();
            break;
        case 16:
            initializeImageProcessingDemo();
            break;
    }

    console.log(`Initialized content for slide ${currentSlide}`);
}

function initializeXRayDemo() {
    // Initialize X-ray parameter controls
    const kvpSlider = document.getElementById('kvp-slider');
    const masSlider = document.getElementById('mas-slider');

    if (kvpSlider) {
        kvpSlider.addEventListener('input', (e) => {
            document.getElementById('kvp-value').textContent = `${e.target.value} kVp`;
        });
    }

    if (masSlider) {
        masSlider.addEventListener('input', (e) => {
            document.getElementById('mas-value').textContent = `${e.target.value} mAs`;
        });
    }
}

function initializeMRIDemo() {
    // Initialize MRI parameter controls
    const trSlider = document.getElementById('tr-slider');
    const teSlider = document.getElementById('te-slider');

    if (trSlider) {
        trSlider.addEventListener('input', (e) => {
            document.getElementById('tr-value').textContent = `${e.target.value} ms`;
        });
    }

    if (teSlider) {
        teSlider.addEventListener('input', (e) => {
            document.getElementById('te-value').textContent = `${e.target.value} ms`;
        });
    }
}

function initializeUltrasoundDemo() {
    // Initialize ultrasound parameter controls
    const freqSlider = document.getElementById('us-frequency');
    const gainSlider = document.getElementById('us-gain');
    const depthSlider = document.getElementById('us-depth');

    if (freqSlider) {
        freqSlider.addEventListener('input', (e) => {
            document.getElementById('frequency-value').textContent = `${e.target.value} MHz`;
        });
    }

    if (gainSlider) {
        gainSlider.addEventListener('input', (e) => {
            document.getElementById('gain-value').textContent = `${e.target.value} dB`;
        });
    }

    if (depthSlider) {
        depthSlider.addEventListener('input', (e) => {
            document.getElementById('depth-value').textContent = `${e.target.value} cm`;
        });
    }
}

function initializeImageProcessingDemo() {
    // Initialize image processing controls
    const brightnessSlider = document.getElementById('brightness');
    const contrastSlider = document.getElementById('contrast');
    const sharpeningSlider = document.getElementById('sharpening');

    if (brightnessSlider) {
        brightnessSlider.addEventListener('input', (e) => {
            document.getElementById('brightness-value').textContent = e.target.value;
        });
    }

    if (contrastSlider) {
        contrastSlider.addEventListener('input', (e) => {
            document.getElementById('contrast-value').textContent = e.target.value;
        });
    }

    if (sharpeningSlider) {
        sharpeningSlider.addEventListener('input', (e) => {
            document.getElementById('sharpening-value').textContent = e.target.value;
        });
    }
}

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        nextSlide,
        prevSlide,
        goToSlide,
        toggleAutoPlay,
        toggleFullscreen,
        startECGDemo,
        pauseECGDemo,
        resetECGDemo,
        launchVirtualLab,
        applyFilter,
        resetFilter,
        updateFilterDisplay,
        takeXRay,
        startMRIScan,
        startUltrasound,
        applyImageProcessing,
        // Enhanced functions
        nextSlideEnhanced,
        prevSlideEnhanced,
        goToSlideEnhanced,
        toggleEnhancedAutoPlay,
        updateEnhancedSlideCounter,
        initializeEnhancedSlideDeck,
        toggleBookmark,
        showSlideOverview,
        showKeyboardShortcuts,
        resetPresentation
    };
}

// ===== ENHANCED UTILITY FUNCTIONS =====
function toggleEnhancedAutoPlay() {
    if (isPlaying) {
        clearInterval(autoPlayInterval);
        isPlaying = false;
        const playBtn = document.getElementById('play-pause');
        if (playBtn) playBtn.innerHTML = '<i class="fas fa-play"></i>';
    } else {
        const slideTemplate = slideTemplates[getCurrentSlideType()] || slideTemplates.theory;
        autoPlayInterval = setInterval(() => {
            if (currentSlide < totalSlides) {
                nextSlideEnhanced();
            } else {
                toggleEnhancedAutoPlay(); // Stop at end
            }
        }, slideTemplate.duration);
        isPlaying = true;
        const playBtn = document.getElementById('play-pause');
        if (playBtn) playBtn.innerHTML = '<i class="fas fa-pause"></i>';
    }
}

function getCurrentSlideType() {
    const currentSlideElement = document.querySelector('.slide.active');
    return currentSlideElement?.dataset.type || 'theory';
}

function toggleBookmark(slideNumber) {
    if (bookmarks.has(slideNumber)) {
        bookmarks.delete(slideNumber);
        showNotification(`Bookmark removed from slide ${slideNumber}`, 'info');
    } else {
        bookmarks.add(slideNumber);
        showNotification(`Slide ${slideNumber} bookmarked`, 'success');
    }
    updateEnhancedNavigationButtons();
    saveProgress();
}

function showSlideOverview() {
    // Create slide overview modal
    const modal = document.createElement('div');
    modal.className = 'slide-overview-modal';
    modal.innerHTML = `
        <div class="overview-content">
            <div class="overview-header">
                <h3>Slide Overview</h3>
                <button class="close-overview">&times;</button>
            </div>
            <div class="overview-grid">
                ${generateOverviewGrid()}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    modal.querySelector('.close-overview').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function generateOverviewGrid() {
    let grid = '';
    for (let i = 1; i <= totalSlides; i++) {
        const section = getSectionForSlide(i);
        const isBookmarked = bookmarks.has(i);
        const isCurrent = i === currentSlide;

        grid += `
            <div class="overview-slide ${isCurrent ? 'current' : ''} ${isBookmarked ? 'bookmarked' : ''}"
                 data-slide="${i}" onclick="goToSlideFromOverview(${i})">
                <div class="slide-number">${i}</div>
                <div class="slide-section">${section}</div>
                ${isBookmarked ? '<i class="fas fa-bookmark bookmark-icon"></i>' : ''}
            </div>
        `;
    }
    return grid;
}

function goToSlideFromOverview(slideNumber) {
    goToSlideEnhanced(slideNumber);
    const modal = document.querySelector('.slide-overview-modal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

function showKeyboardShortcuts() {
    const modal = document.createElement('div');
    modal.className = 'shortcuts-modal';
    modal.innerHTML = `
        <div class="shortcuts-content">
            <div class="shortcuts-header">
                <h3>Keyboard Shortcuts</h3>
                <button class="close-shortcuts">&times;</button>
            </div>
            <div class="shortcuts-grid">
                <div class="shortcut-group">
                    <h4>Navigation</h4>
                    <div class="shortcut-item">
                        <kbd>→</kbd> <kbd>Space</kbd> <span>Next slide</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>←</kbd> <span>Previous slide</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Shift</kbd> + <kbd>→</kbd> <span>Next section</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Shift</kbd> + <kbd>←</kbd> <span>Previous section</span>
                    </div>
                </div>
                <div class="shortcut-group">
                    <h4>Controls</h4>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>P</kbd> <span>Play/Pause</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>F11</kbd> <span>Fullscreen</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>B</kbd> <span>Bookmark</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Esc</kbd> <span>Overview</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    modal.querySelector('.close-shortcuts').addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `slide-notification slide-notification-${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getSessionId() {
    let sessionId = sessionStorage.getItem('slideSessionId');
    if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('slideSessionId', sessionId);
    }
    return sessionId;
}

function jumpToSectionByName(sectionName) {
    const moduleConfig = slideModules[currentModule];
    if (moduleConfig && moduleConfig.sections[sectionName]) {
        const sectionStart = moduleConfig.sections[sectionName].start;
        goToSlideEnhanced(sectionStart);
    }
}

function resetPresentation() {
    if (confirm('Reset presentation to the beginning?')) {
        goToSlideEnhanced(1);
        bookmarks.clear();
        slideHistory = [];
        presentationStartTime = Date.now();
        showNotification('Presentation reset', 'info');
    }
}

function toggleSlideControls() {
    const controls = document.querySelector('.slide-deck-header');
    if (controls) {
        controls.classList.toggle('hidden');
    }
}

function showGoToSlideDialog() {
    const slideNumber = prompt(`Go to slide (1-${totalSlides}):`);
    if (slideNumber && !isNaN(slideNumber)) {
        const num = parseInt(slideNumber);
        if (num >= 1 && num <= totalSlides) {
            goToSlideEnhanced(num);
        }
    }
}

// ===== GLOBAL FUNCTIONS FOR HTML ONCLICK =====
window.goToSlideFromOverview = goToSlideFromOverview;
window.nextSlideEnhanced = nextSlideEnhanced;
window.prevSlideEnhanced = prevSlideEnhanced;
window.goToSlideEnhanced = goToSlideEnhanced;
window.toggleBookmark = toggleBookmark;
window.showSlideOverview = showSlideOverview;
window.showKeyboardShortcuts = showKeyboardShortcuts;
