/**
 * Module Detail JavaScript
 * BioEngage LMS - Individual Module Page Functionality
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let currentModule = null;
let userProgress = {
    completedTopics: 2,
    totalTopics: 9,
    timeSpent: 1.2,
    lastAccessed: new Date()
};

// ===== SAMPLE MODULE DATA =====
const sampleModules = {
    'inst-001': {
        id: 'inst-001',
        title: { en: 'ECG Signal Acquisition and Analysis', ar: 'اكتساب وتحليل إشارات تخطيط القلب' },
        description: { en: 'Comprehensive ECG technology, signal processing, and clinical interpretation', ar: 'تقنية تخطيط القلب الشاملة ومعالجة الإشارات والتفسير السريري' },
        category: 'instrumentation',
        difficulty: 'intermediate',
        duration: { en: '5 hours', ar: '5 ساعات' },
        topics: 9,
        labs: 2,
        quizzes: 3,
        projects: 1,
        icon: '💓',
        curriculum: [
            {
                id: 'section-1',
                title: { en: 'Introduction to ECG', ar: 'مقدمة في تخطيط القلب' },
                items: [
                    { id: 'item-1-1', type: 'lesson', title: { en: 'What is an ECG?', ar: 'ما هو تخطيط القلب؟' }, duration: '15 min', status: 'completed' },
                    { id: 'item-1-2', type: 'lesson', title: { en: 'Heart Electrical System', ar: 'النظام الكهربائي للقلب' }, duration: '20 min', status: 'completed' },
                    { id: 'item-1-3', type: 'quiz', title: { en: 'Basic ECG Quiz', ar: 'اختبار تخطيط القلب الأساسي' }, duration: '10 min', status: 'in-progress' }
                ]
            },
            {
                id: 'section-2',
                title: { en: 'ECG Leads and Electrode Placement', ar: 'أقطاب تخطيط القلب ووضع الأقطاب الكهربائية' },
                items: [
                    { id: 'item-2-1', type: 'lesson', title: { en: 'Standard 12-Lead System', ar: 'نظام الـ 12 قطب القياسي' }, duration: '25 min', status: 'locked' },
                    { id: 'item-2-2', type: 'lesson', title: { en: 'Electrode Placement Techniques', ar: 'تقنيات وضع الأقطاب الكهربائية' }, duration: '20 min', status: 'locked' },
                    { id: 'item-2-3', type: 'lab', title: { en: 'Virtual ECG Setup Lab', ar: 'معمل إعداد تخطيط القلب الافتراضي' }, duration: '30 min', status: 'locked' }
                ]
            },
            {
                id: 'section-3',
                title: { en: 'Signal Acquisition Hardware', ar: 'أجهزة اكتساب الإشارات' },
                items: [
                    { id: 'item-3-1', type: 'lesson', title: { en: 'ECG Amplifiers', ar: 'مضخمات تخطيط القلب' }, duration: '30 min', status: 'locked' },
                    { id: 'item-3-2', type: 'lesson', title: { en: 'Analog-to-Digital Conversion', ar: 'التحويل من التناظري إلى الرقمي' }, duration: '25 min', status: 'locked' },
                    { id: 'item-3-3', type: 'lesson', title: { en: 'Noise and Interference', ar: 'الضوضاء والتداخل' }, duration: '20 min', status: 'locked' }
                ]
            }
        ]
    },
    'inst-002': {
        id: 'inst-002',
        title: { en: 'Blood Pressure Monitoring Systems', ar: 'أنظمة مراقبة ضغط الدم' },
        description: { en: 'Non-invasive and invasive blood pressure measurement techniques', ar: 'تقنيات قياس ضغط الدم غير الغازية والغازية' },
        category: 'instrumentation',
        difficulty: 'intermediate',
        duration: { en: '4 hours', ar: '4 ساعات' },
        topics: 8,
        labs: 2,
        quizzes: 2,
        projects: 1,
        icon: '🩺'
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeModuleDetail();
});

function initializeModuleDetail() {
    // Get module ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const moduleId = urlParams.get('module') || 'inst-001'; // Default to ECG module
    
    // Load module data
    loadModuleData(moduleId);
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Initialize progress tracking
    initializeProgressTracking();
    
    // Initialize curriculum
    initializeCurriculum();
    
    console.log(`Module detail page initialized for module: ${moduleId}`);
}

// ===== MODULE DATA LOADING =====
function loadModuleData(moduleId) {
    // In a real application, this would fetch from an API
    currentModule = sampleModules[moduleId] || sampleModules['inst-001'];
    
    // Update page content
    updateModuleContent();
    
    // Update breadcrumb
    updateBreadcrumb();
    
    // Update page title
    updatePageTitle();
}

function updateModuleContent() {
    if (!currentModule) return;
    
    const currentLang = document.documentElement.lang || 'en';
    
    // Update module header
    updateElement('module-icon', currentModule.icon);
    updateElement('module-title', currentModule.title[currentLang]);
    updateElement('module-description', currentModule.description[currentLang]);
    updateElement('module-difficulty', getDifficultyText(currentModule.difficulty, currentLang));
    updateElement('module-duration', currentModule.duration[currentLang]);
    
    // Update stats
    updateElement('module-topics', currentModule.topics);
    updateElement('module-labs', currentModule.labs);
    updateElement('module-quizzes', currentModule.quizzes);
    updateElement('module-projects', currentModule.projects);
    
    // Update difficulty badge class
    const difficultyBadge = document.getElementById('module-difficulty');
    if (difficultyBadge) {
        difficultyBadge.className = `difficulty-badge-large ${currentModule.difficulty}`;
    }
}

function updateBreadcrumb() {
    if (!currentModule) return;
    
    const currentLang = document.documentElement.lang || 'en';
    const breadcrumbTitle = document.getElementById('current-module-title');
    
    if (breadcrumbTitle) {
        breadcrumbTitle.textContent = currentModule.title[currentLang];
    }
}

function updatePageTitle() {
    if (!currentModule) return;
    
    const currentLang = document.documentElement.lang || 'en';
    const pageTitle = document.querySelector('head title');
    
    if (pageTitle) {
        const titleText = currentLang === 'en' 
            ? `${currentModule.title[currentLang]} - BioEngage LMS`
            : `${currentModule.title[currentLang]} - بايو إنجيج`;
        pageTitle.textContent = titleText;
    }
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    // Start module button
    const startBtn = document.getElementById('start-module-btn');
    if (startBtn) {
        startBtn.addEventListener('click', startModule);
    }
    
    // Bookmark button
    const bookmarkBtn = document.getElementById('bookmark-btn');
    if (bookmarkBtn) {
        bookmarkBtn.addEventListener('click', toggleBookmark);
    }
    
    // Share button
    const shareBtn = document.getElementById('share-btn');
    if (shareBtn) {
        shareBtn.addEventListener('click', shareModule);
    }
}

function startModule() {
    // In a real application, this would navigate to the learning interface
    const currentLang = document.documentElement.lang || 'en';
    const message = currentLang === 'en' 
        ? 'Starting module... This would navigate to the learning interface.'
        : 'بدء الوحدة... سيؤدي هذا إلى الانتقال إلى واجهة التعلم.';
    
    alert(message);
    
    // Simulate progress update
    userProgress.lastAccessed = new Date();
    updateProgressDisplay();
}

function toggleBookmark() {
    const bookmarkBtn = document.getElementById('bookmark-btn');
    const icon = bookmarkBtn.querySelector('i');
    const text = bookmarkBtn.querySelector('span');
    const currentLang = document.documentElement.lang || 'en';
    
    if (icon.classList.contains('fas')) {
        // Remove bookmark
        icon.classList.remove('fas');
        icon.classList.add('far');
        text.textContent = currentLang === 'en' ? 'Bookmark' : 'إضافة للمفضلة';
        
        // Visual feedback
        bookmarkBtn.style.background = 'var(--gray-100)';
        bookmarkBtn.style.color = 'var(--gray-700)';
    } else {
        // Add bookmark
        icon.classList.remove('far');
        icon.classList.add('fas');
        text.textContent = currentLang === 'en' ? 'Bookmarked' : 'مضاف للمفضلة';
        
        // Visual feedback
        bookmarkBtn.style.background = 'var(--primary-blue)';
        bookmarkBtn.style.color = 'white';
    }
}

function shareModule() {
    if (navigator.share && currentModule) {
        const currentLang = document.documentElement.lang || 'en';
        navigator.share({
            title: currentModule.title[currentLang],
            text: currentModule.description[currentLang],
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? 'Module URL copied to clipboard!'
                : 'تم نسخ رابط الوحدة إلى الحافظة!';
            
            // Show temporary notification
            showNotification(message);
        });
    }
}

// ===== PROGRESS TRACKING =====
function initializeProgressTracking() {
    updateProgressDisplay();
    updateProgressRing();
}

function updateProgressDisplay() {
    const progressPercentage = Math.round((userProgress.completedTopics / userProgress.totalTopics) * 100);
    
    // Update progress text
    const progressText = document.querySelector('.progress-text');
    if (progressText) {
        progressText.textContent = `${progressPercentage}%`;
    }
    
    // Update progress details
    const progressItems = document.querySelectorAll('.progress-item');
    if (progressItems.length >= 2) {
        const currentLang = document.documentElement.lang || 'en';
        
        progressItems[0].querySelector('span:last-child').textContent = 
            `${userProgress.completedTopics}/${userProgress.totalTopics} ${currentLang === 'en' ? 'topics' : 'مواضيع'}`;
        
        progressItems[1].querySelector('span:last-child').textContent = 
            `${userProgress.timeSpent} ${currentLang === 'en' ? 'hours' : 'ساعات'}`;
    }
}

function updateProgressRing() {
    const progressRing = document.querySelector('.progress-ring-progress');
    if (progressRing) {
        const radius = 36;
        const circumference = 2 * Math.PI * radius;
        const progressPercentage = (userProgress.completedTopics / userProgress.totalTopics) * 100;
        const offset = circumference - (progressPercentage / 100) * circumference;
        
        progressRing.style.strokeDasharray = circumference;
        progressRing.style.strokeDashoffset = offset;
    }
}

// ===== CURRICULUM =====
function initializeCurriculum() {
    if (!currentModule || !currentModule.curriculum) return;
    
    const curriculumContainer = document.getElementById('module-curriculum');
    if (!curriculumContainer) return;
    
    curriculumContainer.innerHTML = '';
    
    currentModule.curriculum.forEach((section, index) => {
        const sectionElement = createCurriculumSection(section, index);
        curriculumContainer.appendChild(sectionElement);
    });
}

function createCurriculumSection(section, index) {
    const currentLang = document.documentElement.lang || 'en';
    const sectionDiv = document.createElement('div');
    sectionDiv.className = 'curriculum-section';
    sectionDiv.dataset.sectionId = section.id;
    
    // Calculate section stats
    const totalItems = section.items.length;
    const completedItems = section.items.filter(item => item.status === 'completed').length;
    const totalDuration = section.items.reduce((sum, item) => {
        const minutes = parseInt(item.duration.match(/\d+/)[0]);
        return sum + minutes;
    }, 0);
    
    sectionDiv.innerHTML = `
        <div class="curriculum-header" onclick="toggleCurriculumSection('${section.id}')">
            <div class="curriculum-title">
                <span class="curriculum-icon">📚</span>
                ${section.title[currentLang]}
            </div>
            <div class="curriculum-meta">
                ${completedItems}/${totalItems} • ${totalDuration} min
            </div>
            <i class="fas fa-chevron-down curriculum-toggle"></i>
        </div>
        <div class="curriculum-content">
            <div class="curriculum-items">
                ${section.items.map(item => createCurriculumItem(item)).join('')}
            </div>
        </div>
    `;
    
    // Expand first section by default
    if (index === 0) {
        sectionDiv.classList.add('expanded');
    }
    
    return sectionDiv;
}

function createCurriculumItem(item) {
    const currentLang = document.documentElement.lang || 'en';
    const typeIcons = {
        lesson: '📖',
        lab: '🔬',
        quiz: '📝',
        project: '🎯'
    };
    
    const statusClasses = {
        completed: 'completed',
        'in-progress': 'in-progress',
        locked: 'locked'
    };
    
    const statusTexts = {
        completed: { en: 'Completed', ar: 'مكتمل' },
        'in-progress': { en: 'In Progress', ar: 'قيد التنفيذ' },
        locked: { en: 'Locked', ar: 'مقفل' }
    };
    
    return `
        <div class="curriculum-item" data-item-id="${item.id}">
            <div class="curriculum-item-icon">${typeIcons[item.type] || '📄'}</div>
            <div class="curriculum-item-info">
                <div class="curriculum-item-title">${item.title[currentLang]}</div>
                <div class="curriculum-item-meta">
                    <span>${item.duration}</span>
                    <span>${item.type}</span>
                </div>
            </div>
            <div class="curriculum-item-status ${statusClasses[item.status]}">
                ${statusTexts[item.status][currentLang]}
            </div>
        </div>
    `;
}

function toggleCurriculumSection(sectionId) {
    const section = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (section) {
        section.classList.toggle('expanded');
    }
}

// ===== UTILITY FUNCTIONS =====
function updateElement(id, content) {
    const element = document.getElementById(id);
    if (element) {
        if (typeof content === 'string') {
            element.textContent = content;
        } else {
            element.innerHTML = content;
        }
    }
}

function getDifficultyText(difficulty, lang) {
    const texts = {
        beginner: { en: 'Beginner', ar: 'مبتدئ' },
        intermediate: { en: 'Intermediate', ar: 'متوسط' },
        advanced: { en: 'Advanced', ar: 'متقدم' }
    };
    
    return texts[difficulty] ? texts[difficulty][lang] : difficulty;
}

function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-blue);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
    
    // Add animation styles if not exists
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    
    if (currentModule) {
        updateModuleContent();
        updateBreadcrumb();
        updatePageTitle();
        initializeCurriculum();
        updateProgressDisplay();
    }
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadModuleData,
        toggleBookmark,
        shareModule,
        toggleCurriculumSection
    };
}
