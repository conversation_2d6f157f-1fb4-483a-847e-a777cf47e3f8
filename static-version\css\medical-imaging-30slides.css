/* ===== MEDICAL IMAGING 30-SLIDE STYLES ===== */

/* Modalities Overview */
.modalities-overview {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 1rem;
    padding: 2rem;
}

.modalities-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.modality-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.modality-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #059669, #10b981);
    transform: scaleX(0);
    transition: transform var(--transition-fast);
}

.modality-card:hover::before {
    transform: scaleX(1);
}

.modality-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: #10b981;
}

.modality-card.active {
    border-color: #059669;
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), var(--white));
}

.modality-card.active::before {
    transform: scaleX(1);
}

.modality-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.modality-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.modality-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.modality-specs {
    margin-bottom: 2rem;
}

.spec-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.spec-value {
    font-weight: 500;
    color: #059669;
    font-size: 0.875rem;
    background: rgba(5, 150, 105, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
}

.modality-applications h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.modality-applications ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.modality-applications li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.4;
}

.modality-applications li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    font-size: 1rem;
    flex-shrink: 0;
}

/* Interactive Card Effects */
.interactive-card {
    position: relative;
    overflow: hidden;
}

.interactive-card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(5, 150, 105, 0.1) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    border-radius: 50%;
    pointer-events: none;
}

.interactive-card:hover::after {
    width: 300px;
    height: 300px;
}

/* X-Ray Imaging Specific Styles */
.modality-card[data-modality="xray"] .modality-icon {
    color: #dc2626;
    animation: xray-pulse 2s ease-in-out infinite;
}

@keyframes xray-pulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.1);
        opacity: 1;
    }
}

.modality-card[data-modality="xray"]:hover {
    border-color: #dc2626;
}

.modality-card[data-modality="xray"]::before {
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

/* CT Scanning Specific Styles */
.modality-card[data-modality="ct"] .modality-icon {
    color: #2563eb;
    animation: ct-rotate 3s linear infinite;
}

@keyframes ct-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.modality-card[data-modality="ct"]:hover {
    border-color: #2563eb;
}

.modality-card[data-modality="ct"]::before {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

/* MRI Specific Styles */
.modality-card[data-modality="mri"] .modality-icon {
    color: #7c3aed;
    animation: mri-magnetic 2.5s ease-in-out infinite;
}

@keyframes mri-magnetic {
    0%, 100% { 
        transform: scale(1) rotate(0deg);
        filter: hue-rotate(0deg);
    }
    50% { 
        transform: scale(1.05) rotate(5deg);
        filter: hue-rotate(30deg);
    }
}

.modality-card[data-modality="mri"]:hover {
    border-color: #7c3aed;
}

.modality-card[data-modality="mri"]::before {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
}

/* Ultrasound Specific Styles */
.modality-card[data-modality="ultrasound"] .modality-icon {
    color: #0891b2;
    animation: ultrasound-wave 1.5s ease-in-out infinite;
}

@keyframes ultrasound-wave {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    25% { 
        transform: scale(1.1);
        opacity: 1;
    }
    50% { 
        transform: scale(0.9);
        opacity: 0.6;
    }
    75% { 
        transform: scale(1.05);
        opacity: 0.9;
    }
}

.modality-card[data-modality="ultrasound"]:hover {
    border-color: #0891b2;
}

.modality-card[data-modality="ultrasound"]::before {
    background: linear-gradient(135deg, #0891b2, #06b6d4);
}

/* Nuclear Medicine Specific Styles */
.modality-card[data-modality="nuclear"] .modality-icon {
    color: #ea580c;
    animation: nuclear-glow 2s ease-in-out infinite;
}

@keyframes nuclear-glow {
    0%, 100% { 
        transform: scale(1);
        filter: drop-shadow(0 0 5px rgba(234, 88, 12, 0.3));
    }
    50% { 
        transform: scale(1.1);
        filter: drop-shadow(0 0 15px rgba(234, 88, 12, 0.6));
    }
}

.modality-card[data-modality="nuclear"]:hover {
    border-color: #ea580c;
}

.modality-card[data-modality="nuclear"]::before {
    background: linear-gradient(135deg, #ea580c, #f97316);
}

/* Medical Imaging Page Specific Styles */
.medical-imaging-30slides-page .slide-deck-header {
    background: linear-gradient(135deg, #059669, #10b981);
}

.medical-imaging-30slides-page .control-btn {
    background: rgba(255, 255, 255, 0.2);
}

.medical-imaging-30slides-page .control-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
}

.medical-imaging-30slides-page .section-marker.active {
    background: linear-gradient(135deg, #059669, #10b981);
}

/* Enhanced Hover Effects */
.modality-card:hover .modality-icon {
    transform: scale(1.2);
    transition: transform 0.3s ease;
}

.modality-card:hover .spec-value {
    background: rgba(5, 150, 105, 0.2);
    transform: scale(1.05);
}

.modality-card:hover .modality-applications li::before {
    animation: checkmark-bounce 0.5s ease;
}

@keyframes checkmark-bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.3); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .modalities-comparison {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .modality-card {
        padding: 1.5rem;
    }
    
    .modality-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 768px) {
    .modalities-overview {
        padding: 1.5rem;
    }
    
    .modality-card {
        padding: 1rem;
    }
    
    .modality-icon {
        font-size: 2rem;
    }
    
    .modality-header h3 {
        font-size: 1.125rem;
    }
    
    .spec-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .modality-applications {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .modality-card {
        padding: 0.75rem;
    }
    
    .modality-icon {
        font-size: 1.5rem;
    }
    
    .modality-header {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }
    
    .spec-row {
        padding: 0.5rem 0;
    }
    
    .modality-applications li {
        font-size: 0.75rem;
    }
}

/* Loading Animation for Modality Cards */
.modality-card.loading {
    position: relative;
    overflow: hidden;
}

.modality-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Selection Indicator */
.modality-card.selected {
    border-color: #059669;
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), var(--white));
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.modality-card.selected::before {
    transform: scaleX(1);
    height: 6px;
}

.modality-card.selected .modality-icon {
    transform: scale(1.1);
    filter: brightness(1.2);
}
