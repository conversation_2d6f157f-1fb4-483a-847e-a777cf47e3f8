/**
 * 30-Slide Module JavaScript
 * BioEngage LMS - Standardized 30-Slide Interactive Presentations
 * Author: Dr. <PERSON>, SUST - BME
 */

// ===== GLOBAL VARIABLES =====
let currentSlideNumber = 1;
let totalSlidesCount = 30;
let currentSection = 'introduction';
let sectionRanges = {
    introduction: { start: 1, end: 5 },
    fundamentals: { start: 6, end: 15 },
    instrumentation: { start: 16, end: 25 },
    applications: { start: 26, end: 30 }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initialize30SlideModule();
});

function initialize30SlideModule() {
    // Initialize progress tracking
    initializeProgressTracking();
    
    // Initialize section navigation
    initializeSectionNavigation();
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Set up slide-specific content
    setupSlideSpecificContent();
    
    console.log('30-slide module initialized successfully');
}

// ===== PROGRESS TRACKING =====
function initializeProgressTracking() {
    updateProgressBar();
    updateSectionHighlight();
    
    // Listen for slide changes
    document.addEventListener('slideChanged', (e) => {
        currentSlideNumber = e.detail.slideNumber;
        updateProgressBar();
        updateSectionHighlight();
        updateCurrentSection();
    });
}

function updateProgressBar() {
    const progressFill = document.getElementById('progress-fill');
    if (progressFill) {
        const progressPercentage = (currentSlideNumber / totalSlidesCount) * 100;
        progressFill.style.width = `${progressPercentage}%`;
    }
}

function updateSectionHighlight() {
    const sectionMarkers = document.querySelectorAll('.section-marker');
    sectionMarkers.forEach(marker => {
        marker.classList.remove('active');
    });
    
    // Find current section
    for (const [section, range] of Object.entries(sectionRanges)) {
        if (currentSlideNumber >= range.start && currentSlideNumber <= range.end) {
            currentSection = section;
            const activeMarker = document.querySelector(`[data-section="${section}"]`);
            if (activeMarker) {
                activeMarker.classList.add('active');
            }
            break;
        }
    }
}

function updateCurrentSection() {
    // Update timeline section highlighting
    const timelineSections = document.querySelectorAll('.timeline-section');
    timelineSections.forEach(section => {
        section.classList.remove('active');
    });
    
    const activeTimelineSection = document.querySelector(`.timeline-section[data-section="${currentSection}"]`);
    if (activeTimelineSection) {
        activeTimelineSection.classList.add('active');
    }
}

// ===== SECTION NAVIGATION =====
function initializeSectionNavigation() {
    const sectionMarkers = document.querySelectorAll('.section-marker');
    sectionMarkers.forEach(marker => {
        marker.addEventListener('click', () => {
            const section = marker.dataset.section;
            navigateToSection(section);
        });
    });
    
    // Add timeline section navigation
    const timelineSections = document.querySelectorAll('.timeline-section');
    timelineSections.forEach(section => {
        section.addEventListener('click', () => {
            const sectionName = section.dataset.section;
            navigateToSection(sectionName);
        });
    });
}

function navigateToSection(sectionName) {
    if (sectionRanges[sectionName]) {
        const targetSlide = sectionRanges[sectionName].start;
        goToSlide(targetSlide);
        
        const currentLang = document.documentElement.lang || 'en';
        const sectionNames = {
            introduction: { en: 'Introduction', ar: 'المقدمة' },
            fundamentals: { en: 'Fundamentals', ar: 'الأساسيات' },
            instrumentation: { en: 'Instrumentation', ar: 'الأجهزة' },
            applications: { en: 'Applications', ar: 'التطبيقات' }
        };
        
        const sectionDisplayName = sectionNames[sectionName][currentLang];
        const message = currentLang === 'en' 
            ? `Navigating to ${sectionDisplayName} section (Slide ${targetSlide})`
            : `الانتقال إلى قسم ${sectionDisplayName} (الشريحة ${targetSlide})`;
        
        show30SlideNotification(message, 'info');
    }
}

// ===== INTERACTIVE ELEMENTS =====
function initializeInteractiveElements() {
    // Add hover effects to objective cards
    const objectiveCards = document.querySelectorAll('.objective-card');
    objectiveCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
        });
        
        card.addEventListener('click', () => {
            const objectiveTitle = card.querySelector('h3').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Exploring objective: ${objectiveTitle}`
                : `استكشاف الهدف: ${objectiveTitle}`;
            
            show30SlideNotification(message, 'info');
            
            // Highlight objective temporarily
            card.style.background = 'rgba(37, 99, 235, 0.1)';
            card.style.borderColor = '#2563eb';
            setTimeout(() => {
                card.style.background = '';
                card.style.borderColor = '';
            }, 2000);
        });
    });
    
    // Add interactive elements to timeline sections
    const timelineSections = document.querySelectorAll('.timeline-section');
    timelineSections.forEach(section => {
        const listItems = section.querySelectorAll('.section-content li');
        listItems.forEach(item => {
            item.addEventListener('click', () => {
                const content = item.textContent;
                const currentLang = document.documentElement.lang || 'en';
                const message = currentLang === 'en' 
                    ? `Learning focus: ${content.substring(0, 50)}...`
                    : `التركيز على التعلم: ${content.substring(0, 50)}...`;
                
                show30SlideNotification(message, 'info');
                
                // Animate item
                item.style.background = 'rgba(59, 130, 246, 0.2)';
                item.style.transform = 'translateX(10px) scale(1.05)';
                setTimeout(() => {
                    item.style.background = '';
                    item.style.transform = '';
                }, 1500);
            });
        });
    });
}

// ===== SLIDE-SPECIFIC CONTENT =====
function setupSlideSpecificContent() {
    // Initialize content based on current slide
    switch(currentSlideNumber) {
        case 1:
            initializeTitleSlide();
            break;
        case 2:
            initializeObjectivesSlide();
            break;
        case 3:
            initializeCourseStructureSlide();
            break;
        default:
            initializeStandardSlide();
    }
}

function initializeTitleSlide() {
    // Animate course statistics
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.animation = 'fadeInUp 0.8s ease-out forwards';
        }, 1000 + (index * 200));
    });
    
    // Add click interaction to stats
    statItems.forEach(item => {
        item.addEventListener('click', () => {
            const statText = item.textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en' 
                ? `Course statistic: ${statText}`
                : `إحصائية الدورة: ${statText}`;
            
            show30SlideNotification(message, 'info');
        });
    });
}

function initializeObjectivesSlide() {
    // Stagger objective card animations
    const objectiveCards = document.querySelectorAll('.objective-card');
    objectiveCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.8s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 500 + (index * 200));
    });
}

function initializeCourseStructureSlide() {
    // Animate timeline sections
    const timelineSections = document.querySelectorAll('.timeline-section');
    timelineSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateX(-30px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.8s ease-out';
            section.style.opacity = '1';
            section.style.transform = 'translateX(0)';
        }, 300 + (index * 150));
    });
}

function initializeStandardSlide() {
    // Standard slide initialization
    console.log(`Initializing standard slide ${currentSlideNumber}`);
}

// ===== UTILITY FUNCTIONS =====
function show30SlideNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `slide-notification slide-notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    const colors = {
        info: '#2563eb',
        success: '#059669',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10001;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
        font-weight: 500;
        font-size: 0.875rem;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getSlideProgress() {
    return {
        current: currentSlideNumber,
        total: totalSlidesCount,
        percentage: (currentSlideNumber / totalSlidesCount) * 100,
        section: currentSection,
        sectionProgress: getSectionProgress()
    };
}

function getSectionProgress() {
    const range = sectionRanges[currentSection];
    if (!range) return 0;
    
    const sectionSlide = currentSlideNumber - range.start + 1;
    const sectionTotal = range.end - range.start + 1;
    return (sectionSlide / sectionTotal) * 100;
}

// ===== KEYBOARD SHORTCUTS =====
document.addEventListener('keydown', (e) => {
    switch(e.key) {
        case '1':
            if (e.ctrlKey) {
                e.preventDefault();
                navigateToSection('introduction');
            }
            break;
        case '2':
            if (e.ctrlKey) {
                e.preventDefault();
                navigateToSection('fundamentals');
            }
            break;
        case '3':
            if (e.ctrlKey) {
                e.preventDefault();
                navigateToSection('instrumentation');
            }
            break;
        case '4':
            if (e.ctrlKey) {
                e.preventDefault();
                navigateToSection('applications');
            }
            break;
        case 'p':
            if (e.ctrlKey) {
                e.preventDefault();
                const progress = getSlideProgress();
                const currentLang = document.documentElement.lang || 'en';
                const message = currentLang === 'en' 
                    ? `Progress: ${progress.percentage.toFixed(1)}% (${progress.current}/${progress.total}) - ${progress.section} section`
                    : `التقدم: ${progress.percentage.toFixed(1)}% (${progress.current}/${progress.total}) - قسم ${progress.section}`;
                show30SlideNotification(message, 'info');
            }
            break;
    }
});

// ===== LANGUAGE CHANGE HANDLER =====
document.addEventListener('languageChanged', (e) => {
    const newLang = e.detail.language;
    console.log(`30-slide module language changed to: ${newLang}`);
    
    // Update any dynamic content if needed
    updateProgressBar();
    updateSectionHighlight();
});

// ===== BODY SYSTEM INTERACTION =====
let currentBodySystem = 'cardiovascular';
let bodySystemData = {
    cardiovascular: {
        title: { en: 'Cardiovascular System', ar: 'جهاز القلب والأوعية الدموية' },
        description: {
            en: 'Pumps blood throughout the body, delivering oxygen and nutrients to tissues',
            ar: 'يضخ الدم في جميع أنحاء الجسم، وينقل الأكسجين والمواد المغذية إلى الأنسجة'
        },
        specs: {
            heartRate: '60-100 bpm',
            bloodPressure: '120/80 mmHg'
        }
    },
    respiratory: {
        title: { en: 'Respiratory System', ar: 'الجهاز التنفسي' },
        description: {
            en: 'Facilitates gas exchange, bringing oxygen into the body and removing carbon dioxide',
            ar: 'يسهل تبادل الغازات، وإدخال الأكسجين إلى الجسم وإزالة ثاني أكسيد الكربون'
        },
        specs: {
            respiratoryRate: '12-20 breaths/min',
            tidalVolume: '500 mL'
        }
    },
    nervous: {
        title: { en: 'Nervous System', ar: 'الجهاز العصبي' },
        description: {
            en: 'Controls and coordinates body functions through electrical and chemical signals',
            ar: 'يتحكم وينسق وظائف الجسم من خلال الإشارات الكهربائية والكيميائية'
        },
        specs: {
            nerveConduction: '50-120 m/s',
            neuronCount: '86 billion'
        }
    },
    musculoskeletal: {
        title: { en: 'Musculoskeletal System', ar: 'الجهاز العضلي الهيكلي' },
        description: {
            en: 'Provides structure, support, and enables movement through bones, muscles, and joints',
            ar: 'يوفر البنية والدعم ويمكن الحركة من خلال العظام والعضلات والمفاصل'
        },
        specs: {
            boneCount: '206 bones',
            muscleCount: '600+ muscles'
        }
    }
};

function selectBodySystem(systemName) {
    if (!bodySystemData[systemName]) return;

    currentBodySystem = systemName;

    // Update button states
    document.querySelectorAll('.system-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-system="${systemName}"]`).classList.add('active');

    // Update system overlays
    document.querySelectorAll('.system-overlay').forEach(overlay => {
        overlay.classList.remove('active');
    });
    document.querySelector(`.system-overlay.${systemName}`).classList.add('active');

    // Update system information
    updateBodySystemInfo(systemName);

    // Show notification
    const currentLang = document.documentElement.lang || 'en';
    const systemTitle = bodySystemData[systemName].title[currentLang];
    const message = currentLang === 'en'
        ? `Exploring ${systemTitle} - Interactive anatomy visualization`
        : `استكشاف ${systemTitle} - تصور تشريحي تفاعلي`;

    show30SlideNotification(message, 'info');
}

function updateBodySystemInfo(systemName) {
    const systemData = bodySystemData[systemName];
    const currentLang = document.documentElement.lang || 'en';

    // Update title
    const titleElement = document.getElementById('system-title');
    if (titleElement) {
        titleElement.textContent = systemData.title[currentLang];
    }

    // Update description
    const descriptionElement = document.getElementById('system-description');
    if (descriptionElement) {
        descriptionElement.textContent = systemData.description[currentLang];
    }

    // Update specifications
    const specs = systemData.specs;
    const specKeys = Object.keys(specs);

    if (specKeys.length >= 1) {
        const firstSpecElement = document.querySelector('.spec-value');
        if (firstSpecElement) {
            firstSpecElement.textContent = specs[specKeys[0]];
        }
    }

    if (specKeys.length >= 2) {
        const secondSpecElement = document.querySelectorAll('.spec-value')[1];
        if (secondSpecElement) {
            secondSpecElement.textContent = specs[specKeys[1]];
        }
    }
}

// ===== TIMELINE INTERACTION =====
function initializeTimelineAnimations() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        const delay = item.dataset.delay || `${index * 0.2}s`;
        item.style.animationDelay = delay;

        // Add click interaction
        item.addEventListener('click', () => {
            const year = item.dataset.year;
            const title = item.querySelector('h3').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en'
                ? `${year}: ${title} - Click to learn more about this milestone`
                : `${year}: ${title} - انقر لمعرفة المزيد عن هذا الإنجاز`;

            show30SlideNotification(message, 'info');

            // Highlight timeline item
            item.style.transform = 'scale(1.05)';
            setTimeout(() => {
                item.style.transform = '';
            }, 1000);
        });
    });
}

// ===== CAREER SECTOR INTERACTION =====
function initializeCareerSectors() {
    const sectorCards = document.querySelectorAll('.sector-card');
    sectorCards.forEach(card => {
        card.addEventListener('click', () => {
            const sectorTitle = card.querySelector('h4').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en'
                ? `Career focus: ${sectorTitle} - Explore opportunities in this sector`
                : `التركيز المهني: ${sectorTitle} - استكشف الفرص في هذا القطاع`;

            show30SlideNotification(message, 'info');

            // Animate sector card
            card.style.background = 'rgba(16, 185, 129, 0.1)';
            card.style.borderColor = '#10b981';
            card.style.transform = 'translateY(-8px) scale(1.02)';

            setTimeout(() => {
                card.style.background = '';
                card.style.borderColor = '';
                card.style.transform = '';
            }, 2000);
        });
    });

    // Add stat card interactions
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('click', () => {
            const statNumber = card.querySelector('.stat-number').textContent;
            const statLabel = card.querySelector('.stat-label').textContent;
            const currentLang = document.documentElement.lang || 'en';
            const message = currentLang === 'en'
                ? `Industry statistic: ${statNumber} ${statLabel}`
                : `إحصائية الصناعة: ${statNumber} ${statLabel}`;

            show30SlideNotification(message, 'info');
        });
    });
}

// ===== ENHANCED SLIDE INITIALIZATION =====
function setupSlideSpecificContent() {
    // Initialize content based on current slide
    switch(currentSlideNumber) {
        case 1:
            initializeTitleSlide();
            break;
        case 2:
            initializeObjectivesSlide();
            break;
        case 3:
            initializeCourseStructureSlide();
            break;
        case 4:
            initializeTimelineAnimations();
            break;
        case 5:
            initializeCareerSectors();
            break;
        case 6:
            selectBodySystem('cardiovascular'); // Initialize with cardiovascular system
            break;
        default:
            initializeStandardSlide();
    }
}

// ===== GLOBAL FUNCTIONS FOR HTML ONCLICK =====
window.selectBodySystem = selectBodySystem;

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        navigateToSection,
        updateProgressBar,
        updateSectionHighlight,
        getSlideProgress,
        getSectionProgress,
        show30SlideNotification,
        selectBodySystem,
        updateBodySystemInfo,
        initializeTimelineAnimations,
        initializeCareerSectors
    };
}
